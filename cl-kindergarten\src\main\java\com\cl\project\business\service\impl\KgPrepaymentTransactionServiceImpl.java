package com.cl.project.business.service.impl;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;

import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.mapper.KgStudentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgPrepaymentTransactionMapper;
import com.cl.project.business.domain.KgPrepaymentTransaction;
import com.cl.project.business.service.IKgPrepaymentTransactionService;

/**
 * 预交款流水Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class KgPrepaymentTransactionServiceImpl implements IKgPrepaymentTransactionService 
{
    @Autowired
    private KgPrepaymentTransactionMapper kgPrepaymentTransactionMapper;

    @Autowired
    private KgStudentMapper kgStudentMapper;

    /**
     * 查询预交款流水
     * 
     * @param transactionId 预交款流水ID
     * @return 预交款流水
     */
    @Override
    public KgPrepaymentTransaction selectKgPrepaymentTransactionById(Long transactionId)
    {
        return kgPrepaymentTransactionMapper.selectKgPrepaymentTransactionById(transactionId);
    }

    /**
     * 查询预交款流水列表
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 预交款流水
     */
    @Override
    public List<KgPrepaymentTransaction> selectKgPrepaymentTransactionList(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        List<KgPrepaymentTransaction> kgPrepaymentTransactions = kgPrepaymentTransactionMapper.selectKgPrepaymentTransactionList(kgPrepaymentTransaction);

        return kgPrepaymentTransactions;
    }

    /**
     * 新增预交款流水
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 结果
     */
    @Override
    public int insertKgPrepaymentTransaction(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        kgPrepaymentTransaction.setCreateTime(DateUtils.getNowDate());
        return kgPrepaymentTransactionMapper.insertKgPrepaymentTransaction(kgPrepaymentTransaction);
    }

    /**
     * 修改预交款流水
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 结果
     */
    @Override
    public int updateKgPrepaymentTransaction(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        kgPrepaymentTransaction.setUpdateTime(DateUtils.getNowDate());
        return kgPrepaymentTransactionMapper.updateKgPrepaymentTransaction(kgPrepaymentTransaction);
    }

    /**
     * 批量删除预交款流水
     * 
     * @param transactionIds 需要删除的预交款流水ID
     * @return 结果
     */
    @Override
    public int deleteKgPrepaymentTransactionByIds(Long[] transactionIds)
    {
        return kgPrepaymentTransactionMapper.deleteKgPrepaymentTransactionByIds(transactionIds);
    }

    /**
     * 删除预交款流水信息
     * 
     * @param transactionId 预交款流水ID
     * @return 结果
     */
    @Override
    public int deleteKgPrepaymentTransactionById(Long transactionId)
    {
        return kgPrepaymentTransactionMapper.deleteKgPrepaymentTransactionById(transactionId);
    }

    /**
     * 获取流水统计数据
     * 
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getTransactionStatistics()
    {
        try {
            // 获取当前公司ID
            Long comId = Long.valueOf(SecurityUtils.getCurrComId());
            
            // 获取今日开始和结束时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            String startTime = today + " 00:00:00";
            String endTime = today + " 23:59:59";
            
            // 调用Mapper查询今日统计数据
            Map<String, Object> todayStats = kgPrepaymentTransactionMapper.getTransactionStatistics(comId, startTime, endTime);
            
            // 调用Mapper查询总统计数据
            Map<String, Object> totalStats = kgPrepaymentTransactionMapper.getTransactionStatistics(comId, null, null);
            
            // 组装返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("todayRecharge", todayStats.get("recharge_total") != null ? todayStats.get("recharge_total") : 0);
            result.put("todayRechargeCount", todayStats.get("recharge_count") != null ? todayStats.get("recharge_count") : 0);
            result.put("todayConsume", todayStats.get("consume_total") != null ? todayStats.get("consume_total") : 0);
            result.put("todayConsumeCount", todayStats.get("consume_count") != null ? todayStats.get("consume_count") : 0);
            
            // 计算今日净值 (充值 - 消费)
            Object todayRecharge = result.get("todayRecharge");
            Object todayConsume = result.get("todayConsume");
            double netValue = 0;
            if (todayRecharge instanceof Number && todayConsume instanceof Number) {
                netValue = ((Number) todayRecharge).doubleValue() - ((Number) todayConsume).doubleValue();
            }
            result.put("todayNet", netValue);
            
            result.put("totalTransactions", totalStats.get("total_count") != null ? totalStats.get("total_count") : 0);
            
            return result;
        } catch (Exception e) {
            // 如果查询出错，返回默认值
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("todayRecharge", 0);
            defaultResult.put("todayRechargeCount", 0);
            defaultResult.put("todayConsume", 0);
            defaultResult.put("todayConsumeCount", 0);
            defaultResult.put("todayNet", 0);
            defaultResult.put("totalTransactions", 0);
            return defaultResult;
        }
    }
}
