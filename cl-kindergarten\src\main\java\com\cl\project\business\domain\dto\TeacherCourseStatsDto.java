package com.cl.project.business.domain.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 教师课时统计DTO
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class TeacherCourseStatsDto {
    
    /** 教师ID */
    private Long teacherId;
    
    /** 教师编号 */
    private String teacherCode;
    
    /** 教师姓名 */
    private String teacherName;
    
    /** 统计年份 */
    private Integer statYear;
    
    /** 统计月份 */
    private Integer statMonth;
    
    /** 总授课节数 */
    private Integer totalSessions;
    
    /** 总课时费 */
    private BigDecimal totalCourseFee;
    
    /** 课程明细 */
    private List<CourseDetailDto> courseDetails;
    
    /** 最后更新时间 */
    private Date updateTime;
    
    /**
     * 课程明细DTO
     */
    public static class CourseDetailDto {
        /** 课程ID */
        private Long courseId;
        
        /** 课程名称 */
        private String courseName;
        
        /** 课程类型 */
        private String courseType;
        
        /** 授课节数 */
        private Integer sessions;
        
        /** 单节价格 */
        private BigDecimal pricePerSession;
        
        /** 小计金额 */
        private BigDecimal subtotal;
        
        public CourseDetailDto() {}
        
        public CourseDetailDto(Long courseId, String courseName, String courseType, 
                              Integer sessions, BigDecimal pricePerSession) {
            this.courseId = courseId;
            this.courseName = courseName;
            this.courseType = courseType;
            this.sessions = sessions;
            this.pricePerSession = pricePerSession;
            this.subtotal = pricePerSession.multiply(new BigDecimal(sessions));
        }
        
        // Getters and Setters
        public Long getCourseId() { return courseId; }
        public void setCourseId(Long courseId) { this.courseId = courseId; }
        
        public String getCourseName() { return courseName; }
        public void setCourseName(String courseName) { this.courseName = courseName; }
        
        public String getCourseType() { return courseType; }
        public void setCourseType(String courseType) { this.courseType = courseType; }
        
        public Integer getSessions() { return sessions; }
        public void setSessions(Integer sessions) { 
            this.sessions = sessions;
            if (this.pricePerSession != null && sessions != null) {
                this.subtotal = this.pricePerSession.multiply(new BigDecimal(sessions));
            }
        }
        
        public BigDecimal getPricePerSession() { return pricePerSession; }
        public void setPricePerSession(BigDecimal pricePerSession) { 
            this.pricePerSession = pricePerSession;
            if (this.sessions != null && pricePerSession != null) {
                this.subtotal = pricePerSession.multiply(new BigDecimal(this.sessions));
            }
        }
        
        public BigDecimal getSubtotal() { return subtotal; }
        public void setSubtotal(BigDecimal subtotal) { this.subtotal = subtotal; }
    }
    
    // Getters and Setters
    public Long getTeacherId() { return teacherId; }
    public void setTeacherId(Long teacherId) { this.teacherId = teacherId; }
    
    public String getTeacherCode() { return teacherCode; }
    public void setTeacherCode(String teacherCode) { this.teacherCode = teacherCode; }
    
    public String getTeacherName() { return teacherName; }
    public void setTeacherName(String teacherName) { this.teacherName = teacherName; }
    
    public Integer getStatYear() { return statYear; }
    public void setStatYear(Integer statYear) { this.statYear = statYear; }
    
    public Integer getStatMonth() { return statMonth; }
    public void setStatMonth(Integer statMonth) { this.statMonth = statMonth; }
    
    public Integer getTotalSessions() { return totalSessions; }
    public void setTotalSessions(Integer totalSessions) { this.totalSessions = totalSessions; }
    
    public BigDecimal getTotalCourseFee() { return totalCourseFee; }
    public void setTotalCourseFee(BigDecimal totalCourseFee) { this.totalCourseFee = totalCourseFee; }
    
    public List<CourseDetailDto> getCourseDetails() { return courseDetails; }
    public void setCourseDetails(List<CourseDetailDto> courseDetails) { this.courseDetails = courseDetails; }
    
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
}
