package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgClass;

/**
 * 班级信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgClassService 
{
    /**
     * 查询班级信息
     * 
     * @param classId 班级信息ID
     * @return 班级信息
     */
    public KgClass selectKgClassById(Long classId);
    
    /**
     * 根据钉钉部门ID查询班级信息
     * 
     * @param dingtalkDeptId 钉钉部门ID
     * @return 班级信息
     */
    public KgClass selectKgClassByDingtalkDeptId(Long dingtalkDeptId);

    /**
     * 查询班级信息列表
     * 
     * @param kgClass 班级信息
     * @return 班级信息集合
     */
    public List<KgClass> selectKgClassList(KgClass kgClass);

    /**
     * 新增班级信息
     * 
     * @param kgClass 班级信息
     * @return 结果
     */
    public int insertKgClass(KgClass kgClass);

    /**
     * 修改班级信息
     *
     * @param kgClass 班级信息
     * @return 结果
     */
    public int updateKgClass(KgClass kgClass);

    /**
     * 批量删除班级信息
     *
     * @param classIds 需要删除的班级信息ID
     * @return 结果
     */
    public int deleteKgClassByIds(Long[] classIds);

    /**
     * 更新班级当前人数
     *
     * @param classId 班级ID
     * @return 结果
     */
    public int updateClassCurrentCount(Long classId);

    /**
     * 批量更新所有班级当前人数
     *
     * @return 更新的班级数量
     */
    public int updateAllClassCurrentCount();

    /**
     * 删除班级信息信息
     * 
     * @param classId 班级信息ID
     * @return 结果
     */
    public int deleteKgClassById(Long classId);
}
