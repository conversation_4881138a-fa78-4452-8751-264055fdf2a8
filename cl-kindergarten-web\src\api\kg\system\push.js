import request from '@/utils/request'

// 查询消息推送记录列表
export function listPush(query) {
  return request({
    url: '/business/push/list',
    method: 'get',
    params: query
  })
}

// 查询消息推送记录详细
export function getPush(pushId) {
  return request({
    url: '/business/push/' + pushId,
    method: 'get'
  })
}

// 新增消息推送记录
export function addPush(data) {
  return request({
    url: '/business/push',
    method: 'post',
    data: data
  })
}

// 修改消息推送记录
export function updatePush(data) {
  return request({
    url: '/business/push',
    method: 'put',
    data: data
  })
}

// 删除消息推送记录
export function delPush(pushId) {
  return request({
    url: '/business/push/' + pushId,
    method: 'delete'
  })
}

// 导出消息推送记录
export function exportPush(query) {
  return request({
    url: '/business/push/export',
    method: 'get',
    params: query
  })
}

// 手动发送推送消息
export function sendPushMessage(pushIds) {
  return request({
    url: '/business/push/send',
    method: 'post',
    data: pushIds
  })
}

// 重新发送失败的推送消息
export function resendFailedPush(pushIds) {
  return request({
    url: '/business/push/resend',
    method: 'post',
    data: pushIds
  })
}

// 标记消息为已读
export function markPushAsRead(pushIds) {
  return request({
    url: '/business/push/markRead',
    method: 'post',
    data: pushIds
  })
}

// 批量创建推送消息（用于账单发送）
export function batchCreatePushMessages(data) {
  return request({
    url: '/business/push/batchCreate',
    method: 'post',
    data: data
  })
}

// 获取推送统计信息
export function getPushStatistics(query) {
  return request({
    url: '/business/push/statistics',
    method: 'get',
    params: query
  })
}

// 获取家长消息列表（供家长端使用）
export function getParentMessages(parentId) {
  return request({
    url: '/business/push/parent/' + parentId,
    method: 'get'
  })
}
