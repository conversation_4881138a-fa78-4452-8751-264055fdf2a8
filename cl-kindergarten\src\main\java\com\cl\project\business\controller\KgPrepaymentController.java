package com.cl.project.business.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.web.page.TableDataInfo;
import com.cl.project.business.domain.KgPrepaymentAccount;
import com.cl.project.business.domain.KgPrepaymentTransaction;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.service.IKgPrepaymentAccountService;
import com.cl.project.business.service.IKgPrepaymentTransactionService;
import com.cl.project.business.dto.PrepaymentRechargeDto;
import com.cl.project.business.dto.PrepaymentBalanceDto;
import com.cl.project.business.dto.PrepaymentStatisticsDto;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预交款管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/business/prepayment")
public class KgPrepaymentController extends BaseController
{
    @Autowired
    private IKgPrepaymentAccountService kgPrepaymentAccountService;
    
    @Autowired
    private IKgPrepaymentTransactionService kgPrepaymentTransactionService;

    @Autowired
    private KgStudentMapper kgStudentMapper;
    /**
     * 查询预交款余额列表
     */
    @SaCheckPermission("kg:prepayment:balance:list")
    @GetMapping("/balance/list")
    public TableDataInfo getBalanceList(PrepaymentBalanceDto queryParams)
    {
        startPage();
        List<PrepaymentBalanceDto> list = kgPrepaymentAccountService.selectBalanceList(queryParams);
        return getDataTable(list);
    }

    /**
     * 查询预交款余额统计
     */
    @SaCheckPermission("kg:prepayment:balance:view")
    @GetMapping("/stats/balance")
    public AjaxResult getBalanceStatistics()
    {
        PrepaymentStatisticsDto statistics = kgPrepaymentAccountService.getBalanceStatistics();
        return AjaxResult.success(statistics);
    }

    /**
     * 查询学生预交款余额详情
     */
    @SaCheckPermission("kg:prepayment:balance:view")
    @GetMapping("/student/{studentId}/balance")
    public AjaxResult getStudentBalance(@PathVariable("studentId") Long studentId)
    {
        PrepaymentBalanceDto balance = kgPrepaymentAccountService.getStudentBalanceDetail(studentId);
        return AjaxResult.success(balance);
    }

    /**
     * 预交款充值
     */
    @SaCheckPermission("kg:prepayment:recharge")
    @Log(title = "预交款充值", businessType = BusinessType.OTHER)
    @PostMapping("/recharge")
    public AjaxResult recharge(@RequestBody PrepaymentRechargeDto rechargeDto)
    {
        int result = kgPrepaymentAccountService.rechargeBalance(rechargeDto);
        return toAjax(result);
    }

    /**
     * 批量扣费
     */
    @SaCheckPermission("kg:prepayment:deduct")
    @Log(title = "批量扣费", businessType = BusinessType.OTHER)
    @PostMapping("/deduct/batch")
    public AjaxResult batchDeduct(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<Long> studentIds = (List<Long>) params.get("studentIds");
        String accountType = (String) params.get("accountType");
        Double amount = Double.valueOf(params.get("amount").toString());
        String description = (String) params.get("description");
        
        int result = kgPrepaymentAccountService.batchDeduct(studentIds, accountType, amount, description);
        return AjaxResult.success("成功扣费 " + result + " 个账户");
    }

    /**
     * 余额转移
     */
    @SaCheckPermission("kg:prepayment:transfer")
    @Log(title = "余额转移", businessType = BusinessType.OTHER)
    @PostMapping("/transfer")
    public AjaxResult transfer(@RequestBody Map<String, Object> params)
    {
        Long fromStudentId = Long.valueOf(params.get("fromStudentId").toString());
        Long toStudentId = Long.valueOf(params.get("toStudentId").toString());
        String accountType = (String) params.get("accountType");
        Double amount = Double.valueOf(params.get("amount").toString());
        String description = (String) params.get("description");
        
        int result = kgPrepaymentAccountService.transferBalance(fromStudentId, toStudentId, accountType, amount, description);
        return toAjax(result);
    }

    /**
     * 冻结/解冻账户
     */
    @SaCheckPermission("kg:prepayment:manage")
    @Log(title = "冻结/解冻账户", businessType = BusinessType.UPDATE)
    @PutMapping("/account/{accountId}/freeze")
    public AjaxResult freezeAccount(@PathVariable("accountId") Long accountId, @RequestBody Map<String, Object> params)
    {
        Boolean freeze = (Boolean) params.get("freeze");
        int result = kgPrepaymentAccountService.freezeAccount(accountId, freeze);
        return toAjax(result);
    }

    /**
     * 批量发送余额不足提醒
     */
    @SaCheckPermission("kg:prepayment:notify")
    @Log(title = "余额不足提醒", businessType = BusinessType.OTHER)
    @PostMapping("/notify/insufficient")
    public AjaxResult notifyInsufficientBalance()
    {
        int result = kgPrepaymentAccountService.notifyInsufficientBalance();
        return AjaxResult.success("成功发送 " + result + " 条提醒");
    }

    /**
     * 导出预交款余额
     */
    @SaCheckPermission("kg:prepayment:export")
    @Log(title = "导出预交款余额", businessType = BusinessType.EXPORT)
    @PostMapping("/balance/export")
    public void exportBalance(HttpServletResponse response, PrepaymentBalanceDto queryParams)
    {
        // TODO: 实现导出逻辑
        // List<PrepaymentBalanceDto> list = kgPrepaymentAccountService.selectBalanceList(queryParams);
    }

    /**
     * 查询预交款流水列表
     */
    @SaCheckPermission("kg:prepayment:transaction:list")
    @GetMapping("/transaction/list")
    public TableDataInfo getTransactionList(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        startPage();
        List<KgPrepaymentTransaction> list = kgPrepaymentTransactionService.selectKgPrepaymentTransactionList(kgPrepaymentTransaction);
        // 优化：批量查询学生信息，避免N+1问题
        if (!list.isEmpty()) {
            // 收集所有不重复的学生ID
            Set<Long> studentIds = list.stream()
                    .map(KgPrepaymentTransaction::getStudentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询学生信息并构建ID到姓名和班级名的映射
            Map<Long, KgStudent> studentMap = new HashMap<>();
            if (!studentIds.isEmpty()) {
                // 使用现有的selectKgStudentList方法进行批量查询
                KgStudent queryStudent = new KgStudent();
                queryStudent.setComId(kgPrepaymentTransaction.getComId());
                List<KgStudent> students = kgStudentMapper.selectKgStudentList(queryStudent);
                // 构建学生ID到KgStudent的映射，只保留需要的学生
                studentMap = students.stream()
                        .filter(student -> studentIds.contains(student.getStudentId()))
                        .collect(Collectors.toMap(
                                KgStudent::getStudentId,
                                s -> s,
                                (existing, replacement) -> existing // 处理重复key的情况
                        ));
            }
            // 设置学生姓名和班级名称
            for (KgPrepaymentTransaction prepaymentTransaction : list) {
                KgStudent student = studentMap.get(prepaymentTransaction.getStudentId());
                if (student != null) {
                    prepaymentTransaction.setStudentName(student.getStudentName());
                    prepaymentTransaction.setClassName(student.getClassName());
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 获取预交款流水统计
     */
    @SaCheckPermission("kg:prepayment:transaction:view")
    @GetMapping("/transaction/statistics")
    public AjaxResult getTransactionStatistics()
    {
        try {
            Map<String, Object> statistics = kgPrepaymentTransactionService.getTransactionStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            // 如果统计方法不存在，返回默认值
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("todayRecharge", 0);
            defaultStats.put("todayRechargeCount", 0);
            defaultStats.put("todayConsume", 0);
            defaultStats.put("todayConsumeCount", 0);
            defaultStats.put("todayNet", 0);
            defaultStats.put("totalTransactions", 0);
            return AjaxResult.success(defaultStats);
        }
    }

    /**
     * 获取预交款流水详细信息
     */
    @SaCheckPermission("kg:prepayment:transaction:view")
    @GetMapping("/transaction/{transactionId}")
    public AjaxResult getTransaction(@PathVariable("transactionId") Long transactionId)
    {
        return AjaxResult.success(kgPrepaymentTransactionService.selectKgPrepaymentTransactionById(transactionId));
    }

    /**
     * 新增预交款流水
     */
    @SaCheckPermission("kg:prepayment:transaction:add")
    @Log(title = "预交款流水", businessType = BusinessType.INSERT)
    @PostMapping("/transaction")
    public AjaxResult addTransaction(@RequestBody KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        return toAjax(kgPrepaymentTransactionService.insertKgPrepaymentTransaction(kgPrepaymentTransaction));
    }

    /**
     * 修改预交款流水
     */
    @SaCheckPermission("kg:prepayment:transaction:edit")
    @Log(title = "预交款流水", businessType = BusinessType.UPDATE)
    @PutMapping("/transaction")
    public AjaxResult editTransaction(@RequestBody KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        return toAjax(kgPrepaymentTransactionService.updateKgPrepaymentTransaction(kgPrepaymentTransaction));
    }

    /**
     * 删除预交款流水
     */
    @SaCheckPermission("kg:prepayment:transaction:remove")
    @Log(title = "预交款流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/transaction/{transactionIds}")
    public AjaxResult removeTransaction(@PathVariable Long[] transactionIds)
    {
        return toAjax(kgPrepaymentTransactionService.deleteKgPrepaymentTransactionByIds(transactionIds));
    }

    /**
     * 查询预交款账户列表
     */
    @SaCheckPermission("kg:prepayment:account:list")
    @GetMapping("/account/list")
    public TableDataInfo getAccountList(KgPrepaymentAccount kgPrepaymentAccount)
    {
        startPage();
        List<KgPrepaymentAccount> list = kgPrepaymentAccountService.selectKgPrepaymentAccountList(kgPrepaymentAccount);
        return getDataTable(list);
    }

    /**
     * 获取预交款账户详细信息
     */
    @SaCheckPermission("kg:prepayment:account:view")
    @GetMapping("/account/{accountId}")
    public AjaxResult getAccount(@PathVariable("accountId") Long accountId)
    {
        return AjaxResult.success(kgPrepaymentAccountService.selectKgPrepaymentAccountById(accountId));
    }

    /**
     * 新增预交款账户
     */
    @SaCheckPermission("kg:prepayment:account:add")
    @Log(title = "预交款账户", businessType = BusinessType.INSERT)
    @PostMapping("/account")
    public AjaxResult addAccount(@RequestBody KgPrepaymentAccount kgPrepaymentAccount)
    {
        return toAjax(kgPrepaymentAccountService.insertKgPrepaymentAccount(kgPrepaymentAccount));
    }

    /**
     * 修改预交款账户
     */
    @SaCheckPermission("kg:prepayment:account:edit")
    @Log(title = "预交款账户", businessType = BusinessType.UPDATE)
    @PutMapping("/account")
    public AjaxResult editAccount(@RequestBody KgPrepaymentAccount kgPrepaymentAccount)
    {
        return toAjax(kgPrepaymentAccountService.updateKgPrepaymentAccount(kgPrepaymentAccount));
    }

    /**
     * 删除预交款账户
     */
    @SaCheckPermission("kg:prepayment:account:remove")
    @Log(title = "预交款账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/account/{accountIds}")
    public AjaxResult removeAccount(@PathVariable Long[] accountIds)
    {
        return toAjax(kgPrepaymentAccountService.deleteKgPrepaymentAccountByIds(accountIds));
    }
}
