package com.cl.project.business.dto;

import java.math.BigDecimal;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 预交款充值DTO对象
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class PrepaymentRechargeDto 
{
    /** 学生ID */
    @Excel(name = "学生ID")
    private Long studentId;

    /** 账户类型(tuition:园费账户, course:托管费账户) */
    @Excel(name = "账户类型")
    private String accountType;

    /** 充值金额 */
    @Excel(name = "充值金额")
    private BigDecimal amount;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 操作员ID */
    private Long operatorId;

    /** 操作员姓名 */
    private String operatorName;

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setAccountType(String accountType) 
    {
        this.accountType = accountType;
    }

    public String getAccountType() 
    {
        return accountType;
    }

    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }

    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }

    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }

    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }

    public void setOperatorName(String operatorName) 
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName() 
    {
        return operatorName;
    }

    @Override
    public String toString() {
        return "PrepaymentRechargeDto{" +
                "studentId=" + studentId +
                ", accountType='" + accountType + '\'' +
                ", amount=" + amount +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", remark='" + remark + '\'' +
                ", operatorId=" + operatorId +
                ", operatorName='" + operatorName + '\'' +
                '}';
    }
}
