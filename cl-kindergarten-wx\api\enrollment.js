import request from '@/utils/request.js'

// 查询报名记录列表
export function listEnrollment(params) {
  return request.get('/business/enrollment/list', params)
}

// 查询报名记录详细
export function getEnrollment(enrollmentId) {
  return request.get(`/business/enrollment/${enrollmentId}`)
}

// 新增报名记录
export function addEnrollment(data) {
  return request.post('/business/enrollment', data)
}

// 修改报名记录
export function updateEnrollment(data) {
  return request.put('/business/enrollment', data)
}

// 删除报名记录
export function delEnrollment(enrollmentId) {
  return request.delete(`/business/enrollment/${enrollmentId}`)
}

// 获取活跃的报名记录列表（用于考勤）
export function listActiveEnrollment(params) {
  return request.get('/business/enrollment/active', params)
}

// 导出报名记录
export function exportEnrollment(params) {
  return request.get('/business/enrollment/export', params)
}

// 获取所有学生列表
export function listAllStudent(params) {
  return request.get('/business/student/allList', params)
}

// 获取所有课程列表
export function listAllCourse(params) {
  return request.get('/business/course/listAll', params)
}