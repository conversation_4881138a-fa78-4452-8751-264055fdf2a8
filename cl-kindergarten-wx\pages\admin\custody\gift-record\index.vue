<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">赠送记录</text>
				</view>
			</view>
		</view>


		<!-- 搜索筛选栏 -->
		<view class="search-filter-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input"
					placeholder="搜索学生或课程" 
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
			
			<!-- 简化的筛选按钮 -->
			<view class="filter-actions">
				<view class="filter-chip" @click="showFilterPopup = true">
					<text>筛选</text>
				</view>
			</view>
		</view>

		<!-- 记录展示区域 -->
		<view v-if="giftRecordList.length === 0" class="empty-container">
			<view class="empty-illustration">
				<view class="empty-emoji">🎁</view>
			</view>
			<text class="empty-title">暂无赠送记录</text>
			<text class="empty-subtitle">暂无相关记录</text>
		</view>

		<view v-if="giftRecordList.length > 0" class="content">
			<text class="list-header">赠送记录 ({{ giftRecordList.length }} 条)</text>
			
			<view 
				v-for="item in giftRecordList" 
				:key="item.recordId" 
				class="gift-record-card"
				@click="handleDetail(item)"
			>
				<!-- 卡片头部 -->
				<view class="card-header">
					<view class="left-section">
						<view class="avatar-container">
							<text class="avatar-text">{{ getStudentInitial(item.studentName) }}</text>
						</view>
						<view class="info-section">
							<text class="primary-text">{{ item.studentName || '未知学生' }}</text>
							<text class="secondary-text">{{ item.giftCourseName || '未知课程' }}</text>
						</view>
					</view>
					<view :class="'status-badge ' + getStatusClass(item.status)">
						<text class="badge-text">{{ getStatusText(item.status) }}</text>
					</view>
				</view>

				<!-- 课时信息 -->
				<view class="card-body">
					<view class="progress-container">
						<view class="progress-info">
							<text class="progress-label">课时进度</text>
							<text class="progress-text">{{ item.usedSessions || 0 }}/{{ item.giftSessions || 0 }}节</text>
						</view>
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: getProgressPercentage(item) + '%' }"></view>
						</view>
						<text class="remaining-text">剩余：{{ item.remainingSessions || 0 }}节</text>
					</view>
				</view>

				<!-- 其他信息 -->
				<view class="details-grid">
					<view v-if="item.triggerAmount" class="detail-cell">
						<text class="cell-label">触发金额</text>
						<text class="cell-value">¥{{ item.triggerAmount }}</text>
					</view>
					<view v-if="item.giftMonth" class="detail-cell">
						<text class="cell-label">赠送月份</text>
						<text class="cell-value">{{ item.giftMonth }}</text>
					</view>
					<view class="detail-cell">
						<text class="cell-label">过期时间</text>
						<text class="cell-value">{{ item.expireDate ? formatExpireDate(item.expireDate) : '永不过期' }}</text>
					</view>
				</view>

				<!-- 创建时间 -->
				<view class="card-footer">
					<text class="create-time">{{ formatTime(item.createTime) }}</text>
				</view>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<u-popup v-model="showFilterPopup" mode="bottom" height="500px" border-radius="20" :safe-area-inset-bottom="true">
			<view class="filter-popup">
				<view class="popup-header">
					<view class="header-indicator"></view>
					<text class="popup-title">筛选条件</text>
					<view class="close-btn" @click="showFilterPopup = false">
						<u-icon name="close" size="20" color="#666666"></u-icon>
					</view>
				</view>
				
				<scroll-view class="filter-content" scroll-y="true">
					<view class="filter-section">
						<view class="section-header">
							<view class="section-icon">📊</view>
							<text class="section-title">状态筛选</text>
						</view>
						<view class="status-grid">
							<view 
								v-for="(status, index) in statusOptions" 
								:key="index"
								class="status-card" 
								:class="{ 
									active: (queryParams.status === undefined || queryParams.status === null) ? 
										status.value === null : 
										queryParams.status === status.value 
								}"
								@click="selectStatus(status.value)"
							>
								<view class="status-icon" :class="'icon-' + (status.value || 'none')"></view>
								<text class="status-text">{{ status.label }}</text>
							</view>
						</view>
					</view>

					<view class="filter-section">
						<view class="section-header">
							<view class="section-icon">📅</view>
							<text class="section-title">赠送月份</text>
						</view>
						<view class="input-wrapper">
							<picker 
								mode="date" 
								:value="currentDate" 
								fields="month"
								@change="onDateChange"
								class="month-picker"
							>
								<view class="picker-content">
									<input 
										class="month-input" 
										placeholder="选择月份，如：2025-01" 
										:value="queryParams.giftMonth"
										type="text"
										readonly
									/>
									<view class="input-icon">
										<u-icon name="calendar" size="16" color="#999999"></u-icon>
									</view>
								</view>
							</picker>
						</view>
					</view>
				</scroll-view>

				<view class="filter-footer">
					<view class="footer-buttons">
						<view class="footer-btn reset-btn" @click="resetFilter">
							<u-icon name="refresh" size="16" color="#666666"></u-icon>
							<text class="btn-text">重置</text>
						</view>
						<view class="footer-btn confirm-btn" @click="applyFilter">
							<u-icon name="checkmark" size="16" color="#ffffff"></u-icon>
							<text class="btn-text">确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 详情弹窗 -->
		<u-popup v-model="showDetailPopup" mode="center" width="92%" border-radius="20" :safe-area-inset-bottom="false">
			<view class="detail-modal" v-if="currentRecord">
				<view class="detail-header">
					<view class="header-content">
						<view class="record-avatar">
							<text class="avatar-char">{{ currentRecord.studentName ? currentRecord.studentName.substring(0, 1) : 'S' }}</text>
						</view>
						<view class="record-info">
							<text class="record-name">{{ currentRecord.studentName }}</text>
							<text class="record-course">{{ currentRecord.giftCourseName }}</text>
						</view>
					</view>
					<view class="header-actions">
						<view :class="'record-status ' + getStatusClass(currentRecord.status)">
							<text class="status-text">{{ getStatusText(currentRecord.status) }}</text>
						</view>
						<view class="close-btn" @click="showDetailPopup = false">
							<u-icon name="close" size="18" color="#999999"></u-icon>
						</view>
					</view>
				</view>
				
				<scroll-view class="detail-content" scroll-y="true">
					<view class="detail-sections">
						<!-- 进度卡片 -->
						<view class="progress-card">
							<view class="progress-header">
								<view class="progress-title">
									<view class="title-icon">📊</view>
									<text class="title-text">课时使用进度</text>
								</view>
								<text class="progress-percentage">{{ getProgressPercentage(currentRecord) }}%</text>
							</view>
							<view class="progress-track">
								<view 
									class="progress-bar-fill" 
									:style="{ width: getProgressPercentage(currentRecord) + '%' }"
								></view>
							</view>
							<view class="progress-stats">
								<view class="stat-item">
									<text class="stat-value">{{ currentRecord.usedSessions || 0 }}</text>
									<text class="stat-label">已用课时</text>
								</view>
								<view class="stat-divider"></view>
								<view class="stat-item">
									<text class="stat-value">{{ currentRecord.remainingSessions || 0 }}</text>
									<text class="stat-label">剩余课时</text>
								</view>
								<view class="stat-divider"></view>
								<view class="stat-item">
									<text class="stat-value">{{ currentRecord.giftSessions }}</text>
									<text class="stat-label">总课时</text>
								</view>
							</view>
						</view>

						<!-- 基本信息卡片 -->
						<view class="info-card">
							<view class="card-title">
								<view class="title-icon">📋</view>
								<text class="title-text">基本信息</text>
							</view>
							<view class="info-grid">
								<view class="info-item">
									<view class="item-icon">🏷️</view>
									<view class="item-content">
										<text class="item-label">记录编号</text>
										<text class="item-value"># {{ currentRecord.recordId }}</text>
									</view>
								</view>
								<view class="info-item" v-if="currentRecord.triggerAmount">
									<view class="item-icon">💰</view>
									<view class="item-content">
										<text class="item-label">触发金额</text>
										<text class="item-value">¥{{ currentRecord.triggerAmount }}</text>
									</view>
								</view>
								<view class="info-item" v-if="currentRecord.giftMonth">
									<view class="item-icon">🗓️</view>
									<view class="item-content">
										<text class="item-label">赠送月份</text>
										<text class="item-value">{{ currentRecord.giftMonth }}</text>
									</view>
								</view>
								<view class="info-item">
									<view class="item-icon">⏰</view>
									<view class="item-content">
										<text class="item-label">创建时间</text>
										<text class="item-value">{{ formatTime(currentRecord.createTime) }}</text>
									</view>
								</view>
								<view class="info-item">
									<view class="item-icon">📅</view>
									<view class="item-content">
										<text class="item-label">过期时间</text>
										<text class="item-value">{{ currentRecord.expireDate || '永不过期' }}</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 备注信息卡片 -->
						<view class="remark-card" v-if="currentRecord.remark">
							<view class="card-title">
								<view class="title-icon">📝</view>
								<text class="title-text">备注信息</text>
							</view>
							<view class="remark-content">
								<text class="remark-text">{{ currentRecord.remark }}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>


	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'
import {
	getGiftRecordList,
	getGiftRecordDetail
} from '@/api/api.js'

export default {
	data() {
		return {
			// 列表数据
			giftRecordList: [],
			
			// 视图模式: 'grid' 网格模式, 'card' 卡片列表模式
			viewMode: 'grid',
			
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10
			},
			
			// 搜索关键词
			searchKeyword: '',
			searchTimeout: null,
			
			// 弹窗状态
			showFilterPopup: false,
			showDetailPopup: false,
			
			// 当前日期（用于picker默认值）
			currentDate: new Date().toISOString().slice(0, 7) + '-01',
			
			// 当前操作的记录
			currentRecord: null,
			
			// 状态选项
			statusOptions: [
				{ label: '全部', value: null },
				{ label: '有效', value: 'active' },
				{ label: '已用完', value: 'used_up' },
				{ label: '已过期', value: 'expired' }
			]
		}
	},
	
	onLoad() {
		// 读取用户视图偏好
		const savedViewMode = uni.getStorageSync('gift_record_view_mode')
		if (savedViewMode) {
			this.viewMode = savedViewMode
		}
		
		// 加载数据
		this.loadData()
	},
	
	methods: {
		// 加载数据
		async loadData() {
			this.queryList(1, 10)
		},
		
		// 查询列表数据
		async queryList(pageNum, pageSize) {
			try {
				console.log('开始查询赠送记录列表，参数:', { pageNum, pageSize })
				
				// 简化参数处理
				const params = {
					pageNum,
					pageSize
				};
				
				// 只有在有值时才添加筛选参数
				if (this.searchKeyword) {
					params.studentName = this.searchKeyword;
					params.giftCourseName = this.searchKeyword;
				}
				if (this.queryParams.giftMonth) {
					params.giftMonth = this.queryParams.giftMonth;
				}
				if (this.queryParams.status) {
					params.status = this.queryParams.status;
				}
				
				console.log('实际请求参数:', params)
				const response = await getGiftRecordList(params)
				console.log('API响应:', response)
				
				if (response && response.code === 200) {
					const data = response.rows || []
					this.giftRecordList = data
					console.log('获取到赠送记录:', this.giftRecordList.length, '条记录')
					if (data.length > 0) {
						console.log('第一条记录示例:', data[0])
					}
				} else {
					this.giftRecordList = []
					console.error('API返回错误:', response)
					toast(response?.msg || '获取数据失败')
				}
			} catch (error) {
				console.error('查询赠送记录失败:', error)
				this.giftRecordList = []
				toast('网络错误，请稍后重试')
			}
		},
		
		
		// 搜索输入处理
		onSearchInput() {
			if (this.searchTimeout) {
				clearTimeout(this.searchTimeout)
			}
			this.searchTimeout = setTimeout(() => {
				this.handleSearch()
			}, 500)
		},
		
		// 执行搜索
		handleSearch() {
			this.queryParams.pageNum = 1
			this.loadData()
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.queryParams.pageNum = 1
			this.loadData()
		},
		
		// 选择状态筛选
		selectStatus(status) {
			// 使用$set确保响应式更新
			this.$set(this.queryParams, 'status', status)
			console.log('Selected status:', status, 'Current status:', this.queryParams.status)
		},
		
		// 日期选择器变化
		onDateChange(e) {
			const dateStr = e.detail.value
			// 格式化为YYYY-MM
			this.queryParams.giftMonth = dateStr.slice(0, 7)
			this.currentDate = dateStr
		},
		
		// 重置筛选
		resetFilter() {
			this.queryParams = {
				pageNum: 1,
				pageSize: 10
			}
			this.searchKeyword = ''
			this.currentDate = new Date().toISOString().slice(0, 7) + '-01'
		},
		
		// 应用筛选
		applyFilter() {
			this.showFilterPopup = false
			this.queryParams.pageNum = 1
			this.loadData()
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				'active': 'status-active',
				'used_up': 'status-used',
				'expired': 'status-expired'
			}
			return statusMap[status] || 'status-default'
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'active': '有效',
				'used_up': '已用完',
				'expired': '已过期'
			}
			return statusMap[status] || status || '未知'
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '-'
			const date = new Date(time)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
		},
		
		// 查看详情
		async handleDetail(item) {
			try {
				const response = await getGiftRecordDetail(item.recordId)
				if (response && response.code === 200) {
					this.currentRecord = response.data
					this.showDetailPopup = true
				} else {
					toast(response?.msg || '获取详情失败')
				}
			} catch (error) {
				console.error('获取赠送记录详情失败:', error)
				toast('网络错误，请稍后重试')
			}
		},
		
		
		
		
		
		
		// 切换视图模式
		toggleViewMode() {
			this.viewMode = this.viewMode === 'grid' ? 'card' : 'grid'
			// 保存用户偏好到本地存储
			uni.setStorageSync('gift_record_view_mode', this.viewMode)
		},
		
		// 计算进度百分比
		getProgressPercentage(item) {
			if (!item.giftSessions || item.giftSessions === 0) return 0
			const percentage = ((item.usedSessions || 0) / item.giftSessions) * 100
			return Math.min(Math.round(percentage), 100)
		},
		
		// 格式化过期日期（简短版）
		formatExpireDate(dateStr) {
			if (!dateStr) return '永不过期'
			const date = new Date(dateStr)
			const now = new Date()
			const diffTime = date - now
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
			
			if (diffDays < 0) return '已过期'
			if (diffDays === 0) return '今日过期'
			if (diffDays <= 7) return `${diffDays}天后过期`
			if (diffDays <= 30) return `${Math.ceil(diffDays/7)}周后过期`
			return `${Math.ceil(diffDays/30)}月后过期`
		},
		
		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '-'
			const date = new Date(dateStr)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 获取状态样式（内联样式）
		getStatusStyle(status) {
			const baseStyle = 'padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 24rpx; font-weight: 500;'
			switch(status) {
				case 'active':
					return baseStyle + ' background: rgba(76, 175, 80, 0.1); color: #4caf50;'
				case 'used_up':
					return baseStyle + ' background: rgba(255, 152, 0, 0.1); color: #ff9800;'
				case 'expired':
					return baseStyle + ' background: rgba(244, 67, 54, 0.1); color: #f44336;'
				default:
					return baseStyle + ' background: rgba(96, 125, 139, 0.1); color: #607d8b;'
			}
		},

		// 获取学生姓名首字母
		getStudentInitial(studentName) {
			if (!studentName) return 'S'
			return studentName.charAt(0).toUpperCase()
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	position: absolute;
	left: 32rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.header-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.header-title .title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 搜索筛选栏 */
.search-filter-section {
	background: #ffffff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	background: #f8f9fa;
	border-radius: 24rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
	background: transparent;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	margin-left: 16rpx;
	cursor: pointer;
}

.filter-actions {
	display: flex;
	gap: 16rpx;
}

.filter-chip {
	flex: 1;
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
}

/* 内容区域 */
.content {
	padding: 0 30rpx 120rpx;
}

.list-header {
	padding: 20rpx;
	display: block;
	font-size: 28rpx;
	color: #666666;
}

/* 赠送记录卡片 */
.gift-record-card {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.left-section {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.avatar-container {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.info-section {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.primary-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.secondary-text {
	font-size: 24rpx;
	color: #999999;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.badge-text {
	font-size: 24rpx;
}

.card-body {
	padding: 0 32rpx 24rpx;
}

.progress-container {
	margin-bottom: 20rpx;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.progress-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
}

.progress-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #667eea;
}

.progress-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.progress-fill {
	height: 100%;
	border-radius: 4rpx;
	background: linear-gradient(90deg, #4caf50 0%, #66bb6a 100%);
}

.remaining-text {
	font-size: 24rpx;
	color: #666666;
}

.details-grid {
	display: flex;
	justify-content: space-between;
	background: #f8f9fa;
	margin: 0 32rpx;
	border-radius: 12rpx;
	padding: 20rpx 24rpx;
	gap: 20rpx;
}

.detail-cell {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	flex: 1;
}

.cell-label {
	font-size: 24rpx;
	color: #666666;
}

.cell-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.card-footer {
	padding: 20rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #f8f9fa;
}

.create-time {
	font-size: 22rpx;
	color: #999999;
}


/* 工具栏 */
.toolbar {
	padding: 0 30rpx 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.search-container {
	flex: 1;
}

.search-box {
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 50rpx;
	padding: 0 32rpx;
	height: 72rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	margin-left: 16rpx;
	color: #333333;
}

.filter-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.filter-chip, .view-toggle {
	background: rgba(255, 255, 255, 0.9);
	backdrop-filter: blur(20px);
	border-radius: 50rpx;
	padding: 20rpx 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	font-size: 26rpx;
	color: #666666;
	font-weight: 500;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 1);
	}
}

.view-toggle {
	padding: 20rpx;
}

/* 内容区域 */
.content {
	padding: 0 30rpx 120rpx;
}

/* 网格布局 */
.grid-container {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.gift-card {
	width: calc(50% - 10rpx);
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 24rpx;
	padding: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;

	&:active {
		transform: translateY(-6rpx);
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #667eea, #764ba2);
		border-radius: 24rpx 24rpx 0 0;
	}
}

.card-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.student-badge {
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.student-initial {
	font-size: 20rpx;
	font-weight: 600;
	color: #ffffff;
}

.status-indicator {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;

	&.status-active {
		background: rgba(76, 175, 80, 0.2);
	}

	&.status-used {
		background: rgba(255, 152, 0, 0.2);
	}

	&.status-expired {
		background: rgba(244, 67, 54, 0.2);
	}

	.status-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background: currentColor;
	}

	&.status-active .status-dot {
		color: #4CAF50;
	}

	&.status-used .status-dot {
		color: #FF9800;
	}

	&.status-expired .status-dot {
		color: #F44336;
	}
}

.student-section {
	margin-bottom: 20rpx;
}

.student-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	margin-bottom: 4rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.course-name {
	font-size: 22rpx;
	color: #888888;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.session-section {
	margin-bottom: 20rpx;
}

.session-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: rgba(103, 126, 234, 0.05);
	border-radius: 16rpx;
	padding: 16rpx;
}

.session-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;

	&.gift {
		color: #667eea;
	}

	&.used {
		color: #FF9800;
	}

	&.remaining {
		color: #4CAF50;
	}
}

.session-value {
	font-size: 24rpx;
	font-weight: 700;
	margin-bottom: 4rpx;
}

.session-unit {
	font-size: 20rpx;
	opacity: 0.7;
}

.session-divider {
	width: 1rpx;
	height: 32rpx;
	background: rgba(0, 0, 0, 0.1);
	margin: 0 8rpx;
}

.meta-section {
	margin-bottom: 20rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(0, 0, 0, 0.03);
	border-radius: 12rpx;
	padding: 8rpx 12rpx;
}

.meta-text {
	font-size: 20rpx;
	color: #666666;
}

.expire-info {
	margin-top: 12rpx;
}

.expire-text {
	font-size: 18rpx;
	color: #999999;
	text-align: right;
}


/* 卡片列表模式 */
.card-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.list-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
	}
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.left-section {
	display: flex;
	align-items: center;
	flex: 1;
}

.avatar-container {
	width: 64rpx;
	height: 64rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.avatar-text {
	font-size: 24rpx;
	font-weight: 600;
	color: #ffffff;
}

.info-section {
	flex: 1;
}

.primary-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.secondary-text {
	font-size: 26rpx;
	color: #666666;
	display: block;
}

.right-section {
	margin-left: 20rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 500;

	&.status-active {
		background: rgba(76, 175, 80, 0.1);
		color: #4CAF50;
	}

	&.status-used {
		background: rgba(255, 152, 0, 0.1);
		color: #FF9800;
	}

	&.status-expired {
		background: rgba(244, 67, 54, 0.1);
		color: #F44336;
	}
}

.badge-text {
	font-size: 22rpx;
}

.card-body {
	margin-bottom: 24rpx;
}

.progress-container {
	margin-bottom: 20rpx;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.progress-label {
	font-size: 26rpx;
	color: #666666;
}

.progress-text {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
}

.progress-bar {
	height: 8rpx;
	background: rgba(103, 126, 234, 0.1);
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #667eea, #764ba2);
	transition: width 0.3s ease;
}

.details-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.detail-cell {
	flex: 1;
	min-width: 0;
	background: rgba(103, 126, 234, 0.05);
	border-radius: 12rpx;
	padding: 16rpx;
}

.cell-label {
	font-size: 22rpx;
	color: #888888;
	display: block;
	margin-bottom: 8rpx;
}

.cell-value {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.card-footer {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.08);
}

.create-time {
	font-size: 22rpx;
	color: #999999;
}


/* 空状态 */
.empty-container {
	text-align: center;
	padding: 120rpx 40rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 32rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
	margin-top: 40rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.empty-illustration {
	position: relative;
	margin-bottom: 40rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.empty-emoji {
	font-size: 120rpx;
	z-index: 2;
	position: relative;
}

.empty-circles {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.circle {
	position: absolute;
	border-radius: 50%;
	opacity: 0.1;
}

.circle-1 {
	width: 160rpx;
	height: 160rpx;
	background: #667eea;
	animation: float 6s ease-in-out infinite;
}

.circle-2 {
	width: 100rpx;
	height: 100rpx;
	background: #764ba2;
	top: -30rpx;
	left: 20rpx;
	animation: float 4s ease-in-out infinite reverse;
}

.circle-3 {
	width: 60rpx;
	height: 60rpx;
	background: #4CAF50;
	bottom: -20rpx;
	right: 15rpx;
	animation: float 5s ease-in-out infinite;
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20rpx); }
}

.empty-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-subtitle {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 32rpx;
	display: block;
	line-height: 1.5;
}


/* 筛选弹窗样式 */
.filter-popup {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	height: 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.popup-header {
	padding: 20rpx 32rpx 24rpx;
	background: #ffffff;
	border-bottom: 1rpx solid #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	flex-shrink: 0;
}

.header-indicator {
	position: absolute;
	top: 12rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 80rpx;
	height: 8rpx;
	background: #e0e0e0;
	border-radius: 4rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	text-align: center;
	flex: 1;
}

.close-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: #e9ecef;
		transform: scale(0.95);
	}
}

.filter-content {
	flex: 1;
	padding: 0 32rpx;
	overflow-y: auto;
}

.filter-section {
	margin-bottom: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.section-icon {
	font-size: 28rpx;
	margin-right: 16rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.status-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.status-card {
	background: #f8f9fa;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;

	&:active {
		transform: scale(0.98);
	}

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
		
		.status-text {
			color: #ffffff;
		}
		
		.status-icon {
			background: rgba(255, 255, 255, 0.2);
			&::before {
				color: #ffffff;
			}
		}
	}
}

.status-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #e0e7ff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	
	&::before {
		font-size: 20rpx;
		color: #667eea;
		font-weight: 600;
	}
	
	&.icon-none::before { content: "全"; }
	&.icon-active::before { content: "✓"; color: #4CAF50; }
	&.icon-used_up::before { content: "!"; color: #FF9800; }
	&.icon-expired::before { content: "×"; color: #F44336; }
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
	color: #333333;
	transition: color 0.3s ease;
}

.input-wrapper {
	position: relative;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid #f0f0f0;
	overflow: hidden;
	transition: all 0.3s ease;

	&:focus-within {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
}

.month-picker {
	width: 100%;
	height: 88rpx;
}

.picker-content {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	position: relative;
}

.month-input {
	width: 100%;
	height: 88rpx;
	padding: 0 56rpx 0 24rpx;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
	border: none;
	outline: none;

	&::placeholder {
		color: #999999;
	}
}

.input-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.filter-footer {
	padding: 24rpx 32rpx 32rpx;
	background: #ffffff;
	border-top: 1rpx solid #f5f5f5;
	flex-shrink: 0;
}

.footer-buttons {
	display: flex;
	gap: 16rpx;
}

.footer-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&.reset-btn {
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		
		.btn-text {
			color: #666666;
		}

		&:active {
			background: #e9ecef;
			transform: translateY(2rpx);
		}
	}

	&.confirm-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: 2rpx solid transparent;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
		
		.btn-text {
			color: #ffffff;
		}

		&:active {
			transform: translateY(-2rpx);
			box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
		}
	}
}

.btn-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 详情弹窗样式 */
.detail-modal {
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	max-height: 80vh;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.detail-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 32rpx;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 1rpx;
		background: rgba(255, 255, 255, 0.1);
	}
}

.header-content {
	display: flex;
	align-items: center;
	flex: 1;
	gap: 20rpx;
}

.record-avatar {
	width: 72rpx;
	height: 72rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-char {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

.record-info {
	flex: 1;
}

.record-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	display: block;
	margin-bottom: 8rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.record-course {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.header-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.record-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	
	.status-text {
		font-size: 22rpx;
		font-weight: 500;
		color: #ffffff;
	}
	
	&.status-active {
		background: rgba(76, 175, 80, 0.3);
	}
	&.status-used {
		background: rgba(255, 152, 0, 0.3);
	}
	&.status-expired {
		background: rgba(244, 67, 54, 0.3);
	}
}

.close-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.25);
		transform: scale(0.95);
	}
}

.detail-content {
	flex: 1;
	overflow-y: auto;
}

.detail-sections {
	padding: 32rpx;
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 进度卡片 */
.progress-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 28rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.progress-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.title-icon {
	font-size: 24rpx;
}

.progress-title .title-text,
.card-title .title-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.progress-percentage {
	font-size: 32rpx;
	font-weight: 700;
	color: #667eea;
}

.progress-track {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.progress-bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	border-radius: 6rpx;
	transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		top: 0;
		right: -2rpx;
		width: 4rpx;
		height: 100%;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 2rpx;
	}
}

.progress-stats {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.stat-value {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 4rpx;
}

.stat-label {
	font-size: 22rpx;
	color: #666666;
}

.stat-divider {
	width: 1rpx;
	height: 48rpx;
	background: #e0e0e0;
	margin: 0 20rpx;
}

/* 基本信息卡片 */
.info-card, .remark-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 28rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.card-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.info-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s ease;

	&:active {
		background: #f0f1f3;
		transform: scale(0.98);
	}
}

.item-icon {
	font-size: 24rpx;
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #ffffff;
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.item-label {
	font-size: 22rpx;
	color: #666666;
}

.item-value {
	font-size: 26rpx;
	font-weight: 500;
	color: #333333;
	word-break: break-all;
}

/* 备注卡片 */
.remark-content {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border-left: 4rpx solid #667eea;
}

.remark-text {
	font-size: 26rpx;
	line-height: 1.6;
	color: #333333;
}

/* Status styles */
.status-active {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50 !important;
}

.status-used {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800 !important;
}

.status-expired {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336 !important;
}

.status-default {
	background: rgba(96, 125, 139, 0.1);
	color: #607d8b !important;
}
</style>
