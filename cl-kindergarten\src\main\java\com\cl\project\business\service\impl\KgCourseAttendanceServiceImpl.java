package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Date;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgCourseAttendanceMapper;
import com.cl.project.business.mapper.KgCourseEnrollmentMapper;
import com.cl.project.business.mapper.KgCourseMapper;
import com.cl.project.business.domain.KgCourseAttendance;
import com.cl.project.business.domain.KgCourseEnrollment;
import com.cl.project.business.domain.KgCourse;
import com.cl.project.business.service.IKgCourseAttendanceService;

/**
 * 托管考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseAttendanceServiceImpl implements IKgCourseAttendanceService 
{
    @Autowired
    private KgCourseAttendanceMapper kgCourseAttendanceMapper;
    
    @Autowired
    private KgCourseEnrollmentMapper kgCourseEnrollmentMapper;
    
    @Autowired
    private KgCourseMapper kgCourseMapper;

    /**
     * 查询托管考勤记录
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 托管考勤记录
     */
    @Override
    public KgCourseAttendance selectKgCourseAttendanceById(Long attendanceId)
    {
        return kgCourseAttendanceMapper.selectKgCourseAttendanceById(attendanceId);
    }

    /**
     * 查询托管考勤记录列表
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 托管考勤记录
     */
    @Override
    public List<KgCourseAttendance> selectKgCourseAttendanceList(KgCourseAttendance kgCourseAttendance)
    {
        return kgCourseAttendanceMapper.selectKgCourseAttendanceList(kgCourseAttendance);
    }

    /**
     * 新增托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    @Override
    public int insertKgCourseAttendance(KgCourseAttendance kgCourseAttendance)
    {
        // 如果没有指定老师，使用课程的默认老师
        if (kgCourseAttendance.getTeacherId() == null && kgCourseAttendance.getCourseId() != null) {
            KgCourse course = kgCourseMapper.selectKgCourseById(kgCourseAttendance.getCourseId());
            if (course != null && course.getDefaultTeacherId() != null) {
                kgCourseAttendance.setTeacherId(course.getDefaultTeacherId());
            }
        }
        
        kgCourseAttendance.setCreateTime(DateUtils.getNowDate());
        return kgCourseAttendanceMapper.insertKgCourseAttendance(kgCourseAttendance);
    }

    /**
     * 修改托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    @Override
    public int updateKgCourseAttendance(KgCourseAttendance kgCourseAttendance)
    {
        kgCourseAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgCourseAttendanceMapper.updateKgCourseAttendance(kgCourseAttendance);
    }

    /**
     * 批量删除托管考勤记录
     * 
     * @param attendanceIds 需要删除的托管考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseAttendanceByIds(Long[] attendanceIds)
    {
        return kgCourseAttendanceMapper.deleteKgCourseAttendanceByIds(attendanceIds);
    }

    /**
     * 删除托管考勤记录信息
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseAttendanceById(Long attendanceId)
    {
        return kgCourseAttendanceMapper.deleteKgCourseAttendanceById(attendanceId);
    }
    
    /**
     * 确认单个考勤记录
     * 
     * @param attendanceId 考勤记录ID
     * @param confirmedBy 确认人ID
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmCourseAttendance(Long attendanceId, Long confirmedBy)
    {
        // 1. 获取考勤记录
        KgCourseAttendance attendance = kgCourseAttendanceMapper.selectKgCourseAttendanceById(attendanceId);
        if (attendance == null || attendance.getIsConfirmed() == 1) {
            return 0; // 考勤记录不存在或已确认
        }
        
        // 2. 记录原始出席状态，用于判断是否需要更新课时
        boolean wasPresent = "present".equals(attendance.getAttendanceStatus());
        
        // 3. 更新考勤为已确认状态
        attendance.setIsConfirmed(1L);
        attendance.setConfirmedBy(confirmedBy);
        attendance.setConfirmedTime(new Date());
        int result = kgCourseAttendanceMapper.updateKgCourseAttendance(attendance);
        
        // 4. 如果原始状态是出席且确认成功，更新报名记录的已用课时
        if (result > 0 && wasPresent) {
            KgCourseEnrollment enrollment = kgCourseEnrollmentMapper.selectKgCourseEnrollmentById(attendance.getEnrollmentId());
            if (enrollment != null) {
                enrollment.setUsedSessions(enrollment.getUsedSessions() + 1);
                kgCourseEnrollmentMapper.updateKgCourseEnrollment(enrollment);
            }
        }
        
        return result;
    }
    
    /**
     * 批量确认考勤记录
     * 
     * @param attendanceIds 考勤记录ID列表
     * @param confirmedBy 确认人ID
     * @return 结果
     */
    @Override
    @Transactional
    public int batchConfirmCourseAttendance(List<Long> attendanceIds, Long confirmedBy)
    {
        int count = 0;
        for (Long attendanceId : attendanceIds) {
            count += confirmCourseAttendance(attendanceId, confirmedBy);
        }
        return count;
    }
}
