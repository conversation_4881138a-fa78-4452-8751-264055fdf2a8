<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgGiftRecordMapper">
    
    <resultMap type="KgGiftRecord" id="KgGiftRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="giftCourseId"    column="gift_course_id"    />
        <result property="giftCourseName"    column="gift_course_name"    />
        <result property="giftSessions"    column="gift_sessions"    />
        <result property="triggerAmount"    column="trigger_amount"    />
        <result property="triggerBillId"    column="trigger_bill_id"    />
        <result property="giftMonth"    column="gift_month"    />
        <result property="usedSessions"    column="used_sessions"    />
        <result property="remainingSessions"    column="remaining_sessions"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgGiftRecordVo">
        select record_id, rule_id, student_id, student_name, gift_course_id, gift_course_name, gift_sessions, trigger_amount, trigger_bill_id, gift_month, used_sessions, remaining_sessions, expire_date, status, com_id, create_time, remark from kg_gift_record
    </sql>

    <select id="selectKgGiftRecordList" parameterType="KgGiftRecord" resultMap="KgGiftRecordResult">
        <include refid="selectKgGiftRecordVo"/>
        <where>  
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="giftCourseId != null "> and gift_course_id = #{giftCourseId}</if>
            <if test="giftCourseName != null  and giftCourseName != ''"> and gift_course_name like concat('%', #{giftCourseName}, '%')</if>
            <if test="giftSessions != null "> and gift_sessions = #{giftSessions}</if>
            <if test="triggerAmount != null "> and trigger_amount = #{triggerAmount}</if>
            <if test="triggerBillId != null "> and trigger_bill_id = #{triggerBillId}</if>
            <if test="giftMonth != null  and giftMonth != ''"> and gift_month = #{giftMonth}</if>
            <if test="usedSessions != null "> and used_sessions = #{usedSessions}</if>
            <if test="remainingSessions != null "> and remaining_sessions = #{remainingSessions}</if>
            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null "> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgGiftRecordByRecordId" parameterType="Long" resultMap="KgGiftRecordResult">
        <include refid="selectKgGiftRecordVo"/>
        where record_id = #{recordId}
    </select>
        
    <insert id="insertKgGiftRecord" parameterType="KgGiftRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into kg_gift_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="studentName != null and studentName != ''">student_name,</if>
            <if test="giftCourseId != null">gift_course_id,</if>
            <if test="giftCourseName != null">gift_course_name,</if>
            <if test="giftSessions != null">gift_sessions,</if>
            <if test="triggerAmount != null">trigger_amount,</if>
            <if test="triggerBillId != null">trigger_bill_id,</if>
            <if test="giftMonth != null and giftMonth != ''">gift_month,</if>
            <if test="usedSessions != null">used_sessions,</if>
            <if test="remainingSessions != null">remaining_sessions,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="studentName != null and studentName != ''">#{studentName},</if>
            <if test="giftCourseId != null">#{giftCourseId},</if>
            <if test="giftCourseName != null">#{giftCourseName},</if>
            <if test="giftSessions != null">#{giftSessions},</if>
            <if test="triggerAmount != null">#{triggerAmount},</if>
            <if test="triggerBillId != null">#{triggerBillId},</if>
            <if test="giftMonth != null and giftMonth != ''">#{giftMonth},</if>
            <if test="usedSessions != null">#{usedSessions},</if>
            <if test="remainingSessions != null">#{remainingSessions},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgGiftRecord" parameterType="KgGiftRecord">
        update kg_gift_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="studentName != null and studentName != ''">student_name = #{studentName},</if>
            <if test="giftCourseId != null">gift_course_id = #{giftCourseId},</if>
            <if test="giftCourseName != null">gift_course_name = #{giftCourseName},</if>
            <if test="giftSessions != null">gift_sessions = #{giftSessions},</if>
            <if test="triggerAmount != null">trigger_amount = #{triggerAmount},</if>
            <if test="triggerBillId != null">trigger_bill_id = #{triggerBillId},</if>
            <if test="giftMonth != null and giftMonth != ''">gift_month = #{giftMonth},</if>
            <if test="usedSessions != null">used_sessions = #{usedSessions},</if>
            <if test="remainingSessions != null">remaining_sessions = #{remainingSessions},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteKgGiftRecordByRecordId" parameterType="Long">
        delete from kg_gift_record where record_id = #{recordId}
    </delete>

    <delete id="deleteKgGiftRecordByRecordIds" parameterType="String">
        delete from kg_gift_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <!-- 查询学生的有效赠送记录 -->
    <select id="selectActiveGiftRecordsByStudentId" parameterType="Long" resultMap="KgGiftRecordResult">
        <include refid="selectKgGiftRecordVo"/>
        where student_id = #{studentId} and status = 'active' and (expire_date is null or expire_date &gt;= now())
        order by create_time desc
    </select>

    <!-- 查询学生在指定规则下的赠送次数 -->
    <select id="countGiftRecordsByStudentAndRule" resultType="int">
        select count(*) from kg_gift_record 
        where student_id = #{studentId} and rule_id = #{ruleId}
    </select>

    <!-- 查询指定规则在指定月份的赠送次数 -->
    <select id="countGiftRecordsByRuleAndMonth" resultType="int">
        select count(*) from kg_gift_record 
        where rule_id = #{ruleId} and gift_month = #{giftMonth}
    </select>

    <!-- 更新赠送记录的使用课时 -->
    <update id="updateGiftRecordUsedSessions">
        update kg_gift_record 
        set used_sessions = #{usedSessions},
            remaining_sessions = gift_sessions - #{usedSessions},
            status = case when (gift_sessions - #{usedSessions}) &lt;= 0 then 'used_up' else status end,
            update_time = now()
        where record_id = #{recordId}
    </update>

    <!-- 统计学生赠送记录的课时使用情况 -->
    <select id="getGiftStatisticsByStudentId" parameterType="Long" resultType="map">
        select 
            coalesce(sum(gift_sessions), 0) as totalGiftSessions,
            coalesce(sum(used_sessions), 0) as totalUsedSessions,
            coalesce(sum(remaining_sessions), 0) as totalRemainingSessions,
            count(*) as totalRecords,
            sum(case when status = 'active' then 1 else 0 end) as activeRecords,
            sum(case when status = 'used_up' then 1 else 0 end) as usedUpRecords,
            sum(case when status = 'expired' then 1 else 0 end) as expiredRecords
        from kg_gift_record 
        where student_id = #{studentId}
    </select>

    <!-- 根据规则ID、学生ID、月份统计赠送记录使用次数 -->
    <select id="countRuleUsageByStudentMonth" resultType="int">
        select count(*) from kg_gift_record 
        where rule_id = #{ruleId} 
        and student_id = #{studentId} 
        and gift_month = #{month}
    </select>

    <!-- 使用赠送课时 -->
    <update id="useGiftSessions">
        update kg_gift_record 
        set used_sessions = used_sessions + #{sessions},
            remaining_sessions = remaining_sessions - #{sessions},
            status = case 
                when (remaining_sessions - #{sessions}) &lt;= 0 then 'used_up' 
                else status 
            end,
            update_time = now()
        where record_id = #{recordId} 
        and remaining_sessions &gt;= #{sessions}
        and status = 'active'
    </update>

    <!-- 根据学生ID和课程ID查询有效的赠送记录 -->
    <select id="selectActiveGiftRecordsByStudentAndCourse" resultMap="KgGiftRecordResult">
        <include refid="selectKgGiftRecordVo"/>
        where student_id = #{studentId} 
        and gift_course_id = #{courseId} 
        and status = 'active' 
        and remaining_sessions &gt; 0
        and (expire_date is null or expire_date &gt;= now())
        order by create_time asc
    </select>
    
</mapper>