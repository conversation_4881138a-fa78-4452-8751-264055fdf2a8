package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.aspectj.lang.annotation.Excel;
import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 赠送记录对象 kg_gift_record
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class KgGiftRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long ruleId;

    /** 学生ID */
    @Excel(name = "学生ID")
    private Long studentId;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String studentName;

    /** 赠送课程ID */
    @Excel(name = "赠送课程ID")
    private Long giftCourseId;

    /** 赠送课程名称 */
    @Excel(name = "赠送课程名称")
    private String giftCourseName;

    /** 赠送课时数 */
    @Excel(name = "赠送课时数")
    private Long giftSessions;

    /** 触发金额 */
    @Excel(name = "触发金额")
    private BigDecimal triggerAmount;

    /** 触发的账单ID */
    @Excel(name = "触发的账单ID")
    private Long triggerBillId;

    /** 赠送月份 YYYY-MM */
    @Excel(name = "赠送月份")
    private String giftMonth;

    /** 已使用课时 */
    @Excel(name = "已使用课时")
    private Long usedSessions;

    /** 剩余课时 */
    @Excel(name = "剩余课时")
    private Long remainingSessions;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 状态：active-有效，expired-过期，used_up-用完 */
    @Excel(name = "状态")
    private String status;

    /** 机构ID */
    @Excel(name = "机构ID")
    private Long comId;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentName() 
    {
        return studentName;
    }
    public void setGiftCourseId(Long giftCourseId) 
    {
        this.giftCourseId = giftCourseId;
    }

    public Long getGiftCourseId() 
    {
        return giftCourseId;
    }
    public void setGiftCourseName(String giftCourseName) 
    {
        this.giftCourseName = giftCourseName;
    }

    public String getGiftCourseName() 
    {
        return giftCourseName;
    }
    public void setGiftSessions(Long giftSessions) 
    {
        this.giftSessions = giftSessions;
    }

    public Long getGiftSessions() 
    {
        return giftSessions;
    }
    public void setTriggerAmount(BigDecimal triggerAmount) 
    {
        this.triggerAmount = triggerAmount;
    }

    public BigDecimal getTriggerAmount() 
    {
        return triggerAmount;
    }
    public void setTriggerBillId(Long triggerBillId) 
    {
        this.triggerBillId = triggerBillId;
    }

    public Long getTriggerBillId() 
    {
        return triggerBillId;
    }
    public void setGiftMonth(String giftMonth) 
    {
        this.giftMonth = giftMonth;
    }

    public String getGiftMonth() 
    {
        return giftMonth;
    }
    public void setUsedSessions(Long usedSessions) 
    {
        this.usedSessions = usedSessions;
    }

    public Long getUsedSessions() 
    {
        return usedSessions;
    }
    public void setRemainingSessions(Long remainingSessions) 
    {
        this.remainingSessions = remainingSessions;
    }

    public Long getRemainingSessions() 
    {
        return remainingSessions;
    }
    public void setExpireDate(Date expireDate) 
    {
        this.expireDate = expireDate;
    }

    public Date getExpireDate() 
    {
        return expireDate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(Long comId) 
    {
        this.comId = comId;
    }

    public Long getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("ruleId", getRuleId())
            .append("studentId", getStudentId())
            .append("studentName", getStudentName())
            .append("giftCourseId", getGiftCourseId())
            .append("giftCourseName", getGiftCourseName())
            .append("giftSessions", getGiftSessions())
            .append("triggerAmount", getTriggerAmount())
            .append("triggerBillId", getTriggerBillId())
            .append("giftMonth", getGiftMonth())
            .append("usedSessions", getUsedSessions())
            .append("remainingSessions", getRemainingSessions())
            .append("expireDate", getExpireDate())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
}
