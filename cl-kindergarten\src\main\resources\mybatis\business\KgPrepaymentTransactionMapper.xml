<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgPrepaymentTransactionMapper">

    <resultMap type="KgPrepaymentTransaction" id="KgPrepaymentTransactionResult">
        <result property="transactionId"       column="transaction_id"       />
        <result property="accountId"           column="account_id"           />
        <result property="studentId"           column="student_id"           />
        <result property="transactionType"     column="transaction_type"     />
        <result property="amount"              column="amount"               />
        <result property="beforeBalance"       column="before_balance"       />
        <result property="afterBalance"        column="after_balance"        />
        <result property="relatedBillId"       column="related_bill_id"      />
        <result property="relatedBillType"     column="related_bill_type"    />
        <result property="paymentMethod"       column="payment_method"       />
        <result property="transactionNo"       column="transaction_no"       />
        <result property="transactionTime"     column="transaction_time"     />
        <result property="operatorId"          column="operator_id"          />
        <result property="description"         column="description"          />
        <result property="comId"               column="com_id"               />
        <result property="createBy"            column="create_by"            />
        <result property="createTime"          column="create_time"          />
        <result property="updateBy"            column="update_by"            />
        <result property="updateTime"          column="update_time"          />
        <result property="remark"              column="remark"               />
    </resultMap>

    <sql id="selectKgPrepaymentTransactionVo">
        select transaction_id, account_id, student_id, transaction_type, amount, before_balance, after_balance, related_bill_id, related_bill_type, payment_method, transaction_no, transaction_time, operator_id, description, com_id, create_by, create_time, update_by, update_time, remark from kg_prepayment_transaction
    </sql>

    <select id="selectKgPrepaymentTransactionList" parameterType="KgPrepaymentTransaction" resultMap="KgPrepaymentTransactionResult">
        <include refid="selectKgPrepaymentTransactionVo"/>
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="studentId != null"> and student_id = #{studentId}</if>
            <if test="transactionType != null and transactionType != ''"> and transaction_type = #{transactionType}</if>
            <if test="comId != null and comId != ''"> and com_id = #{comId}</if>
            <if test="transactionNo != null and transactionNo != ''"> and transaction_no = #{transactionNo}</if>
        </where>
        order by transaction_time desc
    </select>

    <select id="selectKgPrepaymentTransactionById" parameterType="Long" resultMap="KgPrepaymentTransactionResult">
        <include refid="selectKgPrepaymentTransactionVo"/>
        where transaction_id = #{transactionId}
    </select>

    <insert id="insertKgPrepaymentTransaction" parameterType="KgPrepaymentTransaction" useGeneratedKeys="true" keyProperty="transactionId">
        insert into kg_prepayment_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="transactionType != null and transactionType != ''">transaction_type,</if>
            <if test="amount != null">amount,</if>
            <if test="beforeBalance != null">before_balance,</if>
            <if test="afterBalance != null">after_balance,</if>
            <if test="relatedBillId != null">related_bill_id,</if>
            <if test="relatedBillType != null and relatedBillType != ''">related_bill_type,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="transactionNo != null and transactionNo != ''">transaction_no,</if>
            <if test="transactionTime != null">transaction_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="comId != null and comId != ''">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="transactionType != null and transactionType != ''">#{transactionType},</if>
            <if test="amount != null">#{amount},</if>
            <if test="beforeBalance != null">#{beforeBalance},</if>
            <if test="afterBalance != null">#{afterBalance},</if>
            <if test="relatedBillId != null">#{relatedBillId},</if>
            <if test="relatedBillType != null and relatedBillType != ''">#{relatedBillType},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="transactionNo != null and transactionNo != ''">#{transactionNo},</if>
            <if test="transactionTime != null">#{transactionTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="comId != null and comId != ''">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateKgPrepaymentTransaction" parameterType="KgPrepaymentTransaction">
        update kg_prepayment_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="transactionType != null and transactionType != ''">transaction_type = #{transactionType},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="beforeBalance != null">before_balance = #{beforeBalance},</if>
            <if test="afterBalance != null">after_balance = #{afterBalance},</if>
            <if test="relatedBillId != null">related_bill_id = #{relatedBillId},</if>
            <if test="relatedBillType != null and relatedBillType != ''">related_bill_type = #{relatedBillType},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="transactionNo != null and transactionNo != ''">transaction_no = #{transactionNo},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="comId != null and comId != ''">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where transaction_id = #{transactionId}
    </update>

    <delete id="deleteKgPrepaymentTransactionById" parameterType="Long">
        delete from kg_prepayment_transaction where transaction_id = #{transactionId}
    </delete>

    <delete id="deleteKgPrepaymentTransactionByIds" parameterType="String">
        delete from kg_prepayment_transaction where transaction_id in 
        <foreach item="transactionId" collection="array" open="(" separator="," close=")">
            #{transactionId}
        </foreach>
    </delete>

    <!-- 获取流水统计 -->
    <select id="getTransactionStatistics" resultType="map">
        SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'recharge' THEN amount END), 0) AS recharge_total,
            COALESCE(SUM(CASE WHEN transaction_type = 'consume' THEN amount END), 0) AS consume_total,
            COUNT(CASE WHEN transaction_type = 'recharge' THEN 1 END) AS recharge_count,
            COUNT(CASE WHEN transaction_type = 'consume' THEN 1 END) AS consume_count,
            COUNT(*) AS total_count
        FROM kg_prepayment_transaction
        WHERE com_id = #{comId}
        <if test="startTime != null and endTime != null">
            AND transaction_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>

</mapper>
