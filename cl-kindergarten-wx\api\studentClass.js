import request from '@/utils/request.js'

// 获取所有班级列表
export function getClassList(params) {
  return request({
    url: '/business/class/list',
    method: 'get',
    params
  })
}

// 获取班级详细信息
export function getClassDetail(classId) {
  return request({
    url: `/business/class/${classId}`,
    method: 'get'
  })
}

// 获取所有班级列表（不分页）
export function getAllClassList() {
  return request({
    url: '/business/class/allList',
    method: 'get'
  })
}

// 新增班级
export function addStudentClass(data) {
  return request({
    url: '/business/class',
    method: 'post',
    data
  })
}

// 修改班级
export function updateStudentClass(data) {
  return request({
    url: '/business/class',
    method: 'put',
    data
  })
}

// 删除班级
export function deleteStudentClass(classIds) {
  return request({
    url: `/business/class/${classIds}`,
    method: 'delete'
  })
}

// 获取班级学生列表
export function getClassStudents(classId) {
  return request({
    url: `/business/class/${classId}/students`,
    method: 'get'
  })
}