package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Date;
import java.util.Calendar;
import java.math.BigDecimal;
import java.util.stream.Collectors;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgCourseBillMapper;
import com.cl.project.business.mapper.KgCourseEnrollmentMapper;
import com.cl.project.business.mapper.KgCourseAttendanceMapper;
import com.cl.project.business.mapper.KgCourseMapper;
import com.cl.project.business.mapper.KgGiftRecordMapper;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.mapper.KgClassMapper;
import com.cl.project.business.domain.KgCourseBill;
import com.cl.project.business.domain.KgCourseEnrollment;
import com.cl.project.business.domain.KgCourseAttendance;
import com.cl.project.business.domain.KgCourse;
import com.cl.project.business.domain.KgGiftRecord;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgCourseBillService;

/**
 * 托管费账单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseBillServiceImpl implements IKgCourseBillService 
{
    @Autowired
    private KgCourseBillMapper kgCourseBillMapper;
    
    @Autowired
    private KgCourseEnrollmentMapper kgCourseEnrollmentMapper;
    
    @Autowired
    private KgCourseAttendanceMapper kgCourseAttendanceMapper;
    
    @Autowired
    private KgCourseMapper kgCourseMapper;
    
    @Autowired
    private KgGiftRecordMapper kgGiftRecordMapper;
    
    @Autowired
    private KgStudentMapper kgStudentMapper;
    
    @Autowired
    private KgClassMapper kgClassMapper;

    /**
     * 查询托管费账单
     * 
     * @param billId 托管费账单ID
     * @return 托管费账单
     */
    @Override
    public KgCourseBill selectKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.selectKgCourseBillById(billId);
    }

    /**
     * 查询托管费账单完整信息（包含明细）
     *
     * @param billId 托管费账单ID
     * @return 账单完整信息
     */
    @Override
    public Map<String, Object> selectKgCourseBillWithDetails(Long billId)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 获取账单基本信息
        KgCourseBill bill = kgCourseBillMapper.selectKgCourseBillById(billId);
        if (bill == null) {
            throw new RuntimeException("账单不存在");
        }
        
        // 2. 获取学生信息
        String studentName = "未知学生";
        String className = "未知班级";
        
        if (bill.getStudentId() != null) {
            KgStudent student = kgStudentMapper.selectKgStudentById(bill.getStudentId());
            if (student != null) {
                studentName = student.getStudentName();
                // 获取班级信息
                if (student.getClassId() != null) {
                    KgClass kgClass = kgClassMapper.selectKgClassById(student.getClassId());
                    if (kgClass != null) {
                        className = kgClass.getClassName();
                    }
                }
            }
        }
        
        // 3. 获取托管费明细（重新计算）
        List<Map<String, Object>> details = calculateBillDetails(bill.getStudentId(), 
                                                                  bill.getBillYear().intValue(), 
                                                                  bill.getBillMonth().intValue());
        
        // 4. 重新计算统计数据
        long totalSessions = 0;
        long attendedSessions = 0;
        for (Map<String, Object> detail : details) {
            totalSessions += (Long) detail.getOrDefault("totalSessions", 0L);
            attendedSessions += (Long) detail.getOrDefault("attendedSessions", 0L);
        }
        
        // 5. 组装返回数据（完整版，包含明细）
        result.put("billId", bill.getBillId());
        result.put("studentName", studentName);
        result.put("className", className);
        result.put("billYear", bill.getBillYear());
        result.put("billMonth", bill.getBillMonth());
        result.put("totalAmount", bill.getTotalAmount());
        result.put("totalSessions", totalSessions);  // 重新计算的总课时
        result.put("attendedSessions", attendedSessions);  // 重新计算的实际上课
        result.put("giftSessions", bill.getGiftSessions());
        result.put("giftCourseName", bill.getGiftCourseName());
        result.put("giftReason", "系统自动赠送");  // 默认赠送原因
        result.put("billStatus", bill.getBillStatus());
        result.put("sentTime", bill.getSentTime());
        result.put("paidTime", bill.getPaidTime());
        result.put("remark", bill.getRemark());
        result.put("details", details);  // 课程明细数据
        
        return result;
    }

    /**
     * 查询托管费账单列表
     * 
     * @param kgCourseBill 托管费账单
     * @return 托管费账单
     */
    @Override
    public List<KgCourseBill> selectKgCourseBillList(KgCourseBill kgCourseBill)
    {
        return kgCourseBillMapper.selectKgCourseBillList(kgCourseBill);
    }

    /**
     * 新增托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int insertKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setCreateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.insertKgCourseBill(kgCourseBill);
    }

    /**
     * 修改托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int updateKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setUpdateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.updateKgCourseBill(kgCourseBill);
    }

    /**
     * 批量删除托管费账单
     * 
     * @param billIds 需要删除的托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillByIds(Long[] billIds)
    {
        return kgCourseBillMapper.deleteKgCourseBillByIds(billIds);
    }

    /**
     * 删除托管费账单信息
     * 
     * @param billId 托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.deleteKgCourseBillById(billId);
    }

    /**
     * 生成月度托管费账单
     */
    @Override
    @Transactional
    public int generateMonthlyBills(Integer billYear, Integer billMonth, Long classId, Long studentId) {
        // 参数校验
        if (billYear == null || billMonth == null) {
            throw new RuntimeException("账单年份和月份不能为空");
        }
        
        // 查询符合条件的托管课程报名记录
        KgCourseEnrollment enrollmentQuery = new KgCourseEnrollment();
        if (classId != null) {
            enrollmentQuery.setClassId(classId);
        }
        if (studentId != null) {
            enrollmentQuery.setStudentId(studentId);
        }
        enrollmentQuery.setStatus("active"); // 只生成活跃状态的报名账单
        
        List<KgCourseEnrollment> allEnrollments = kgCourseEnrollmentMapper.selectKgCourseEnrollmentList(enrollmentQuery);
        
        // 筛选托管课程报名记录
        List<KgCourseEnrollment> enrollments = allEnrollments.stream()
            .filter(enrollment -> {
                KgCourse course = kgCourseMapper.selectKgCourseById(enrollment.getCourseId());
                return course != null && "1".equals(course.getCourseType()); // 只处理托管课程(字典值1)
            })
            .collect(Collectors.toList());
        
        // 按学生分组报名记录
        Map<Long, List<KgCourseEnrollment>> enrollmentsByStudent = enrollments.stream()
            .collect(Collectors.groupingBy(KgCourseEnrollment::getStudentId));
        
        Long yearLong = billYear.longValue();
        Long monthLong = billMonth.longValue();
        
        int generatedCount = 0;
        
        for (Map.Entry<Long, List<KgCourseEnrollment>> entry : enrollmentsByStudent.entrySet()) {
            Long currentStudentId = entry.getKey();
            List<KgCourseEnrollment> studentEnrollments = entry.getValue();
            
            // 检查该月是否已经生成账单
            KgCourseBill existBillQuery = new KgCourseBill();
            existBillQuery.setStudentId(currentStudentId);
            existBillQuery.setBillYear(yearLong);
            existBillQuery.setBillMonth(monthLong);
            
            List<KgCourseBill> existBills = kgCourseBillMapper.selectKgCourseBillList(existBillQuery);
            
            // 基于最新考勤数据生成账单
            KgCourseBill calculatedBill = generateBillBasedOnAttendance(currentStudentId, billYear, billMonth, studentEnrollments);
            
            if (calculatedBill != null && calculatedBill.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (existBills.isEmpty()) {
                    // 情况1：不存在账单，创建新账单
                    kgCourseBillMapper.insertKgCourseBill(calculatedBill);
                    generatedCount++;
                } else {
                    // 情况2：已存在账单，根据最新考勤数据更新
                    KgCourseBill existBill = existBills.get(0); // 取第一个匹配的账单
                    calculatedBill.setBillId(existBill.getBillId()); // 保持原有ID
                    calculatedBill.setCreateTime(existBill.getCreateTime()); // 保持创建时间
                    calculatedBill.setUpdateTime(new Date()); // 更新修改时间
                    
                    // 只在数据发生变化时才更新
                    if (needsUpdate(existBill, calculatedBill)) {
                        kgCourseBillMapper.updateKgCourseBill(calculatedBill);
                        generatedCount++; // 计入更新计数
                    }
                }
            } else if (!existBills.isEmpty()) {
                // 情况3：计算后无费用但存在账单，删除原有账单
                KgCourseBill existBill = existBills.get(0);
                kgCourseBillMapper.deleteKgCourseBillById(existBill.getBillId());
            }
        }
        
        return generatedCount;
    }
    
    /**
     * 检查账单是否需要更新
     */
    private boolean needsUpdate(KgCourseBill existBill, KgCourseBill calculatedBill) {
        // 比较总金额
        if (existBill.getTotalAmount().compareTo(calculatedBill.getTotalAmount()) != 0) {
            return true;
        }
        
        // 比较赠送课时
        Long existGift = existBill.getGiftSessions() != null ? existBill.getGiftSessions() : 0L;
        Long calculatedGift = calculatedBill.getGiftSessions() != null ? calculatedBill.getGiftSessions() : 0L;
        if (!existGift.equals(calculatedGift)) {
            return true;
        }
        
        // 比较赠送课程名称
        String existGiftName = existBill.getGiftCourseName() != null ? existBill.getGiftCourseName() : "";
        String calculatedGiftName = calculatedBill.getGiftCourseName() != null ? calculatedBill.getGiftCourseName() : "";
        if (!existGiftName.equals(calculatedGiftName)) {
            return true;
        }
        
        return false; // 无变化，不需要更新
    }
    
    /**
     * 基于考勤记录生成账单
     */
    private KgCourseBill generateBillBasedOnAttendance(Long studentId, Integer billYear, Integer billMonth, 
                                                      List<KgCourseEnrollment> enrollments) {
        // 构建查询时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(billYear, billMonth - 1, 1, 0, 0, 0); // 月份从0开始
        Date startDate = calendar.getTime();
        
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        Date endDate = calendar.getTime();
        
        // 创建账单主记录（只使用数据库中存在的字段）
        KgCourseBill bill = new KgCourseBill();
        bill.setStudentId(studentId);
        bill.setBillYear(billYear.longValue());
        bill.setBillMonth(billMonth.longValue());
        bill.setBillStatus("generated");
        bill.setCreateTime(new Date());
        
        // 计算总金额和赠送课时
        BigDecimal totalAmount = BigDecimal.ZERO;
        Long totalGiftSessions = 0L;
        String giftCourseNames = "";
        
        // 按课程计算费用和赠送课时（赠送课时课程只做记录，不影响金额）
        for (KgCourseEnrollment enrollment : enrollments) {
            // 查询该课程的考勤记录
            KgCourseAttendance attendanceQuery = new KgCourseAttendance();
            attendanceQuery.setStudentId(studentId);
            attendanceQuery.setCourseId(enrollment.getCourseId());
            attendanceQuery.setEnrollmentId(enrollment.getEnrollmentId());

            List<KgCourseAttendance> attendances = kgCourseAttendanceMapper.selectKgCourseAttendanceList(attendanceQuery);

            // 筛选指定月份的考勤记录并计算出勤次数
            long presentCount = attendances.stream()
                .filter(attendance -> {
                    Date attendanceDate = attendance.getAttendanceDate();
                    return attendanceDate != null &&
                           !attendanceDate.before(startDate) &&
                           attendanceDate.before(endDate) &&
                           attendance.getIsConfirmed() != null &&
                           attendance.getIsConfirmed() == 1 && // 只计算已确认的考勤
                           "present".equals(attendance.getAttendanceStatus()); // 只计算出勤记录
                })
                .count();

            // 获取课程信息
            KgCourse course = kgCourseMapper.selectKgCourseById(enrollment.getCourseId());
            if (course != null) {
                // 计算课程费用（只统计实际出勤课时）
                if (presentCount > 0) {
                    BigDecimal pricePerSession = course.getPricePerSession() != null ?
                        course.getPricePerSession() : new BigDecimal("0"); // 默认0元/节
                    BigDecimal courseAmount = pricePerSession.multiply(new BigDecimal(presentCount));
                    totalAmount = totalAmount.add(courseAmount);
                }

                // 查询赠送记录（无论是否有出勤课时都要统计）
                Long giftCount = getGiftSessionsForCourse(studentId, enrollment.getCourseId(), billYear, billMonth);
                if (giftCount > 0) {
                    totalGiftSessions += giftCount;
                    if (!giftCourseNames.isEmpty()) {
                        giftCourseNames += ",";
                    }
                    giftCourseNames += course.getCourseName() + "(" + giftCount + "节)";
                }
            }
        }
        
        // 如果没有任何费用且没有赠送课时，返回null（不生成账单）
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0 && totalGiftSessions == 0L) {
            return null;
        }
        
        // 设置账单汇总信息（只使用数据库中存在的字段）
        bill.setTotalAmount(totalAmount);
        bill.setGiftSessions(totalGiftSessions);
        bill.setGiftCourseName(giftCourseNames.isEmpty() ? null : giftCourseNames);
        
        // 设置备注信息
        bill.setRemark("基于考勤记录自动生成的托管费账单");
        
        return bill;
    }
    
    /**
     * 计算托管费明细（按课程分组）
     */
    private List<Map<String, Object>> calculateBillDetails(Long studentId, Integer billYear, Integer billMonth) {
        List<Map<String, Object>> details = new ArrayList<>();
        
        // 构建查询时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(billYear, billMonth - 1, 1, 0, 0, 0);
        Date startDate = calendar.getTime();
        
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.SECOND, -1);
        Date endDate = calendar.getTime();
        
        // 查询该学生的报名记录
        KgCourseEnrollment enrollmentQuery = new KgCourseEnrollment();
        enrollmentQuery.setStudentId(studentId);
        enrollmentQuery.setStatus("active");
        
        List<KgCourseEnrollment> allEnrollments = kgCourseEnrollmentMapper.selectKgCourseEnrollmentList(enrollmentQuery);
        
        // 筛选托管课程报名记录
        List<KgCourseEnrollment> enrollments = allEnrollments.stream()
            .filter(enrollment -> {
                KgCourse course = kgCourseMapper.selectKgCourseById(enrollment.getCourseId());
                return course != null && "1".equals(course.getCourseType()); // 只处理托管课程(字典值1)
            })
            .collect(Collectors.toList());
        
        // 按课程计算明细
        for (KgCourseEnrollment enrollment : enrollments) {
            // 查询该课程的考勤记录
            KgCourseAttendance attendanceQuery = new KgCourseAttendance();
            attendanceQuery.setStudentId(studentId);
            attendanceQuery.setCourseId(enrollment.getCourseId());
            attendanceQuery.setEnrollmentId(enrollment.getEnrollmentId());
            
            List<KgCourseAttendance> attendances = kgCourseAttendanceMapper.selectKgCourseAttendanceList(attendanceQuery);
            
            // 筛选指定月份的考勤记录并计算
            long totalSessionsCount = attendances.stream()
                .filter(attendance -> {
                    Date attendanceDate = attendance.getAttendanceDate();
                    return attendanceDate != null && 
                           !attendanceDate.before(startDate) && 
                           attendanceDate.before(endDate) &&
                           attendance.getIsConfirmed() != null && 
                           attendance.getIsConfirmed() == 1;
                })
                .count();
            
            long attendedSessionsCount = attendances.stream()
                .filter(attendance -> {
                    Date attendanceDate = attendance.getAttendanceDate();
                    return attendanceDate != null && 
                           !attendanceDate.before(startDate) && 
                           attendanceDate.before(endDate) &&
                           attendance.getIsConfirmed() != null && 
                           attendance.getIsConfirmed() == 1 &&
                           "present".equals(attendance.getAttendanceStatus());
                })
                .count();
            
            if (totalSessionsCount > 0) {
                // 获取课程信息
                KgCourse course = kgCourseMapper.selectKgCourseById(enrollment.getCourseId());
                if (course != null) {
                    BigDecimal pricePerSession = course.getPricePerSession() != null ? 
                        course.getPricePerSession() : new BigDecimal("0");
                    
                    BigDecimal totalAmount = pricePerSession.multiply(new BigDecimal(attendedSessionsCount));
                    
                    // 查询赠送课时
                    Long giftSessions = getGiftSessionsForCourse(studentId, enrollment.getCourseId(), billYear, billMonth);
                    
                    // 创建明细记录
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("courseName", course.getCourseName());
                    detail.put("courseType", course.getCourseType());
                    detail.put("totalSessions", totalSessionsCount);
                    detail.put("attendedSessions", attendedSessionsCount);
                    detail.put("pricePerSession", pricePerSession);
                    detail.put("totalAmount", totalAmount);
                    detail.put("giftSessions", giftSessions);
                    detail.put("remark", "基于考勤记录计算");
                    
                    details.add(detail);
                }
            }
        }
        
        return details;
    }
    
    /**
     * 获取课程赠送课时数
     */
    private Long getGiftSessionsForCourse(Long studentId, Long courseId, Integer billYear, Integer billMonth) {
        // 查询该学生的赠送记录
        KgGiftRecord giftQuery = new KgGiftRecord();
        giftQuery.setStudentId(studentId);
        giftQuery.setGiftCourseId(courseId); // 使用正确的字段名
        
        List<KgGiftRecord> giftRecords = kgGiftRecordMapper.selectKgGiftRecordList(giftQuery);
        
        // 构建查询的月份字符串 YYYY-MM
        String targetMonth = String.format("%04d-%02d", billYear, billMonth);
        
        return giftRecords.stream()
            .filter(record -> {
                String giftMonth = record.getGiftMonth();
                return giftMonth != null && 
                       giftMonth.equals(targetMonth) &&
                       "active".equals(record.getStatus());
            })
            .mapToLong(record -> record.getGiftSessions() != null ? record.getGiftSessions() : 0L)
            .sum();
    }

    /**
     * 发送托管费账单
     */
    @Override
    @Transactional
    public int sendBills(Long[] billIds) {
        int sentCount = 0;
        for (Long billId : billIds) {
            KgCourseBill bill = kgCourseBillMapper.selectKgCourseBillById(billId);
            if (bill != null && !"sent".equals(bill.getBillStatus()) && !"paid".equals(bill.getBillStatus())) {
                bill.setBillStatus("sent");
                bill.setSentTime(new Date());
                kgCourseBillMapper.updateKgCourseBill(bill);
                sentCount++;
                
                // TODO: 实际发送逻辑，如发送微信消息或短信
            }
        }
        return sentCount;
    }

    /**
     * 标记账单为已支付
     */
    @Override
    @Transactional
    public int markBillsPaid(Long[] billIds) {
        int paidCount = 0;
        for (Long billId : billIds) {
            KgCourseBill bill = kgCourseBillMapper.selectKgCourseBillById(billId);
            if (bill != null && !"paid".equals(bill.getBillStatus())) {
                bill.setBillStatus("paid");
                bill.setPaidTime(new Date());
                kgCourseBillMapper.updateKgCourseBill(bill);
                paidCount++;
            }
        }
        return paidCount;
    }

    /**
     * 获取账单统计信息
     */
    @Override
    public Map<String, Object> getBillStatistics(KgCourseBill params) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 查询所有账单
        List<KgCourseBill> allBills = kgCourseBillMapper.selectKgCourseBillList(params);
        
        long totalBills = allBills.size();
        long paidBills = allBills.stream().filter(bill -> "paid".equals(bill.getBillStatus())).count();
        long unpaidBills = totalBills - paidBills;
        
        BigDecimal totalAmount = allBills.stream()
            .map(KgCourseBill::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
            
        BigDecimal paidAmount = allBills.stream()
            .filter(bill -> "paid".equals(bill.getBillStatus()))
            .map(KgCourseBill::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        statistics.put("totalBills", totalBills);
        statistics.put("paidBills", paidBills);
        statistics.put("unpaidBills", unpaidBills);
        statistics.put("totalAmount", totalAmount);
        statistics.put("paidAmount", paidAmount);
        statistics.put("unpaidAmount", totalAmount.subtract(paidAmount));
        
        return statistics;
    }
}
