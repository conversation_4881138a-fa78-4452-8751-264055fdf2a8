package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.service.IKgTuitionCalculationService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;

/**
 * 园费计算Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/business/tuition-calculation")
public class KgTuitionCalculationController extends BaseController
{
    @Autowired
    private IKgTuitionCalculationService tuitionCalculationService;

    /**
     * 计算学生月度园费
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @PostMapping("/calculate-monthly")
    @Log(title = "计算月度园费", businessType = BusinessType.OTHER)
    public AjaxResult calculateMonthlyTuition(
            @RequestParam Long studentId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> result = tuitionCalculationService.calculateMonthlyTuition(studentId, year, month);
        return AjaxResult.success(result);
    }

    /**
     * 批量计算班级园费
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @PostMapping("/calculate-class-batch")
    @Log(title = "批量计算班级园费", businessType = BusinessType.OTHER)
    public AjaxResult calculateClassTuitionBatch(
            @RequestParam Long classId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        List<Map<String, Object>> results = tuitionCalculationService.calculateClassTuitionBatch(classId, year, month);
        return AjaxResult.success(results);
    }

    /**
     * 批量计算全园园费
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @PostMapping("/calculate-all-batch")
    @Log(title = "批量计算全园园费", businessType = BusinessType.OTHER)
    public AjaxResult calculateAllTuitionBatch(
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> result = tuitionCalculationService.calculateAllTuitionBatch(year, month);
        return AjaxResult.success(result);
    }

    /**
     * 预览园费计算结果
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping("/preview")
    public AjaxResult previewTuitionCalculation(
            @RequestParam Long studentId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> preview = tuitionCalculationService.previewTuitionCalculation(studentId, year, month);
        return AjaxResult.success(preview);
    }

    /**
     * 获取园费计算规则
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping("/rules")
    public AjaxResult getTuitionCalculationRules(@RequestParam Long classId)
    {
        Map<String, Object> rules = tuitionCalculationService.getTuitionCalculationRules(classId);
        return AjaxResult.success(rules);
    }

    /**
     * 生成园费账单
     */
    @SaCheckPermission("kg:finance:tuition:send")
    @PostMapping("/generate-bills")
    @Log(title = "生成园费账单", businessType = BusinessType.INSERT)
    public AjaxResult generateTuitionBills(@RequestBody List<Map<String, Object>> calculations)
    {
        int count = tuitionCalculationService.generateTuitionBills(calculations);
        return AjaxResult.success("成功生成 " + count + " 条账单");
    }

    /**
     * 重新计算园费
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @PostMapping("/recalculate")
    @Log(title = "重新计算园费", businessType = BusinessType.UPDATE)
    public AjaxResult recalculateTuition(
            @RequestParam Long billId,
            @RequestParam(required = false) String reason)
    {
        Map<String, Object> result = tuitionCalculationService.recalculateTuition(billId, reason);
        return AjaxResult.success(result);
    }

    /**
     * 获取费用明细
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping("/fee-details")
    public AjaxResult getFeeDetails(
            @RequestParam Long studentId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> details = tuitionCalculationService.getFeeDetails(studentId, year, month);
        return AjaxResult.success(details);
    }

    /**
     * 费用调整
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @PostMapping("/adjust-fee")
    @Log(title = "费用调整", businessType = BusinessType.UPDATE)
    public AjaxResult adjustFee(
            @RequestParam Long billId,
            @RequestParam String feeType,
            @RequestParam Double adjustAmount,
            @RequestParam String reason)
    {
        int result = tuitionCalculationService.adjustFee(billId, feeType, adjustAmount, reason);
        return toAjax(result);
    }

    /**
     * 获取费用统计
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping("/statistics")
    public AjaxResult getTuitionStatistics(
            @RequestParam Integer year,
            @RequestParam Integer month,
            @RequestParam(required = false) Long classId)
    {
        Map<String, Object> statistics = tuitionCalculationService.getTuitionStatistics(year, month, classId);
        return AjaxResult.success(statistics);
    }

    /**
     * 回显账单数据为计算结果格式
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping("/recall-results")
    public AjaxResult recallTuitionCalculationResults(
            @RequestParam String calculationType,
            @RequestParam(required = false) Long studentId,
            @RequestParam(required = false) Long classId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        List<Map<String, Object>> results = tuitionCalculationService.recallTuitionCalculationResults(
                calculationType, studentId, classId, year, month);
        return AjaxResult.success(results);
    }
}
