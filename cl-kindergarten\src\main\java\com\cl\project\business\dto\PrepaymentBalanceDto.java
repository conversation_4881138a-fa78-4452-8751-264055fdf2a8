package com.cl.project.business.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 预交款余额DTO对象
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class PrepaymentBalanceDto 
{
    /** 学生ID */
    @Excel(name = "学生ID")
    private Long studentId;

    /** 学生姓名 */
    @Excel(name = "学生姓名")
    private String studentName;

    /** 学生编号 */
    @Excel(name = "学生编号")
    private String studentCode;

    /** 班级ID */
    @Excel(name = "班级ID")
    private Long classId;

    /** 班级名称 */
    @Excel(name = "班级名称")
    private String className;

    /** 园费预交款余额 */
    @Excel(name = "园费预交款余额")
    private BigDecimal tuitionBalance;

    /** 托管费预交款余额 */
    @Excel(name = "托管费预交款余额")
    private BigDecimal courseBalance;

    /** 总余额 */
    @Excel(name = "总余额")
    private BigDecimal totalBalance;

    /** 最后充值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后充值时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastRechargeTime;

    /** 最后使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsageTime;

    /** 查询条件：学生姓名 */
    private String studentName_like;

    /** 查询条件：班级ID */
    private Long classId_eq;

    /** 查询条件：账户类型 */
    private String accountType;

    /** 查询条件：余额状态 */
    private String balanceStatus;

    /** 账户ID */
    @Excel(name = "账户ID")
    private Long accountId;

    /** 账户类型 */
    @Excel(name = "账户类型")
    private String accountTypeResult;

    /** 预交总额 */
    @Excel(name = "预交总额")
    private BigDecimal totalPrepaid;

    /** 已使用总额 */
    @Excel(name = "已使用总额")
    private BigDecimal totalUsed;

    /** 当前余额 */
    @Excel(name = "当前余额")
    private BigDecimal currentBalance;

    /** 冻结金额 */
    @Excel(name = "冻结金额")
    private BigDecimal frozenAmount;

    /** 可用余额 */
    @Excel(name = "可用余额")
    private BigDecimal availableBalance;

    /** 账户状态 */
    @Excel(name = "账户状态")
    private String accountStatus;

    /** 公司ID */
    private String comId;

    /** 最近流水记录 */
    private List<TransactionRecordDto> recentTransactions;

    public static class TransactionRecordDto {
        /** 交易时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date transactionTime;

        /** 交易类型 */
        private String transactionType;

        /** 交易金额 */
        private BigDecimal amount;

        /** 交易后余额 */
        private BigDecimal afterBalance;

        /** 描述 */
        private String description;

        // Getters and Setters
        public Date getTransactionTime() {
            return transactionTime;
        }

        public void setTransactionTime(Date transactionTime) {
            this.transactionTime = transactionTime;
        }

        public String getTransactionType() {
            return transactionType;
        }

        public void setTransactionType(String transactionType) {
            this.transactionType = transactionType;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public BigDecimal getAfterBalance() {
            return afterBalance;
        }

        public void setAfterBalance(BigDecimal afterBalance) {
            this.afterBalance = afterBalance;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentName() 
    {
        return studentName;
    }

    public void setStudentCode(String studentCode) 
    {
        this.studentCode = studentCode;
    }

    public String getStudentCode() 
    {
        return studentCode;
    }

    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }

    public void setClassName(String className) 
    {
        this.className = className;
    }

    public String getClassName() 
    {
        return className;
    }

    public void setTuitionBalance(BigDecimal tuitionBalance) 
    {
        this.tuitionBalance = tuitionBalance;
    }

    public BigDecimal getTuitionBalance() 
    {
        return tuitionBalance;
    }

    public void setCourseBalance(BigDecimal courseBalance) 
    {
        this.courseBalance = courseBalance;
    }

    public BigDecimal getCourseBalance() 
    {
        return courseBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) 
    {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getTotalBalance() 
    {
        return totalBalance;
    }

    public void setLastRechargeTime(Date lastRechargeTime) 
    {
        this.lastRechargeTime = lastRechargeTime;
    }

    public Date getLastRechargeTime() 
    {
        return lastRechargeTime;
    }

    public void setLastUsageTime(Date lastUsageTime) 
    {
        this.lastUsageTime = lastUsageTime;
    }

    public Date getLastUsageTime() 
    {
        return lastUsageTime;
    }

    public String getStudentName_like() {
        return studentName_like;
    }

    public void setStudentName_like(String studentName_like) {
        this.studentName_like = studentName_like;
    }

    public Long getClassId_eq() {
        return classId_eq;
    }

    public void setClassId_eq(Long classId_eq) {
        this.classId_eq = classId_eq;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBalanceStatus() {
        return balanceStatus;
    }

    public void setBalanceStatus(String balanceStatus) {
        this.balanceStatus = balanceStatus;
    }

    public List<TransactionRecordDto> getRecentTransactions() {
        return recentTransactions;
    }

    public void setRecentTransactions(List<TransactionRecordDto> recentTransactions) {
        this.recentTransactions = recentTransactions;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAccountTypeResult() {
        return accountTypeResult;
    }

    public void setAccountTypeResult(String accountTypeResult) {
        this.accountTypeResult = accountTypeResult;
    }

    public BigDecimal getTotalPrepaid() {
        return totalPrepaid;
    }

    public void setTotalPrepaid(BigDecimal totalPrepaid) {
        this.totalPrepaid = totalPrepaid;
    }

    public BigDecimal getTotalUsed() {
        return totalUsed;
    }

    public void setTotalUsed(BigDecimal totalUsed) {
        this.totalUsed = totalUsed;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }

    public BigDecimal getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(BigDecimal availableBalance) {
        this.availableBalance = availableBalance;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getComId() {
        return comId;
    }

    public void setComId(String comId) {
        this.comId = comId;
    }

    @Override
    public String toString() {
        return "PrepaymentBalanceDto{" +
                "studentId=" + studentId +
                ", studentName='" + studentName + '\'' +
                ", studentCode='" + studentCode + '\'' +
                ", classId=" + classId +
                ", className='" + className + '\'' +
                ", tuitionBalance=" + tuitionBalance +
                ", courseBalance=" + courseBalance +
                ", totalBalance=" + totalBalance +
                ", lastRechargeTime=" + lastRechargeTime +
                ", lastUsageTime=" + lastUsageTime +
                '}';
    }
}
