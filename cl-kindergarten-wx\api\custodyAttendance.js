import request from '@/utils/request.js'

// 查询托管考勤列表 (对应web端的listCourseAttendance)
export function listCourseAttendance(params) {
  return request.get('/business/course-attendance/list', params)
}

// 查询托管考勤详细
export function getCourseAttendance(attendanceId) {
  return request.get(`/business/course-attendance/${attendanceId}`)
}

// 新增托管考勤
export function addCourseAttendance(data) {
  return request.post('/business/course-attendance', data)
}

// 修改托管考勤
export function updateCourseAttendance(data) {
  return request.put('/business/course-attendance', data)
}

// 删除托管考勤
export function delCourseAttendance(attendanceId) {
  return request.delete(`/business/course-attendance/${attendanceId}`)
}

// 托管签到 (对应web端的courseCheckin)
export function courseCheckin(data) {
  return request.post('/business/course-attendance/checkin', data)
}

// 确认单个考勤记录
export function confirmSingleAttendance(attendanceId) {
  return request.post(`/business/course-attendance/confirm/${attendanceId}`)
}

// 批量确认考勤记录
export function confirmCourseAttendance(attendanceIds) {
  return request.post('/business/course-attendance/batchConfirm', attendanceIds)
}

// 导出托管考勤
export function exportCourseAttendance(params) {
  return request.get('/business/course-attendance/export', params)
}

// 获取课程列表
export function listAllCourse(params) {
  return request.get('/business/course/listAll', params)
}

// 获取活跃的报名记录 (对应web端的listActiveEnrollment)
export function listActiveEnrollment(params) {
  return request.get('/business/enrollment/active', params)
}

// 获取教师列表 (对应web端的listAllTeacher)
export function listAllTeacher(params) {
  return request.get('/business/teacher/allList', params)
}