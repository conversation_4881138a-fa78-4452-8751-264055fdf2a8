package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgCourse;
import com.cl.project.business.service.IKgCourseService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 托管课程Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/course")
public class KgCourseController extends BaseController
{
    @Autowired
    private IKgCourseService kgCourseService;

    /**
     * 查询托管课程列表
     */
    @SaCheckPermission("kg:course:manage:list")
    @GetMapping("/list")
    public TableDataInfo list(KgCourse kgCourse)
    {
        startPage();
        List<KgCourse> list = kgCourseService.selectKgCourseList(kgCourse);
        return getDataTable(list);
    }

    /**
     * 导出托管课程列表
     */
    @SaCheckPermission("kg:course:manage:list")
    @Log(title = "托管课程", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgCourse kgCourse)
    {
        List<KgCourse> list = kgCourseService.selectKgCourseList(kgCourse);
        ExcelUtil<KgCourse> util = new ExcelUtil<KgCourse>(KgCourse.class);
        return util.exportExcel(list, "course");
    }

    /**
     * 获取托管课程详细信息
     */
    @SaCheckPermission("kg:course:manage:list")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return AjaxResult.success(kgCourseService.selectKgCourseById(courseId));
    }

    /**
     * 新增托管课程
     */
    @SaCheckPermission("kg:course:manage:add")
    @Log(title = "托管课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgCourse kgCourse)
    {
        return toAjax(kgCourseService.insertKgCourse(kgCourse));
    }

    /**
     * 修改托管课程
     */
    @SaCheckPermission("kg:course:manage:edit")
    @Log(title = "托管课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgCourse kgCourse)
    {
        return toAjax(kgCourseService.updateKgCourse(kgCourse));
    }

    /**
     * 删除托管课程
     */
    @SaCheckPermission("kg:course:manage:remove")
    @Log(title = "托管课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(kgCourseService.deleteKgCourseByIds(courseIds));
    }
    
    /**
     * 获取所有课程列表（用于选择）
     */
    @GetMapping("/listAll")
    public AjaxResult listAll()
    {
        List<KgCourse> courseList = kgCourseService.selectKgCourseList(new KgCourse());
        return AjaxResult.success(courseList);
    }
}
