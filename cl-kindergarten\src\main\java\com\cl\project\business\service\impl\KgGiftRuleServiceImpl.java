package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgGiftRuleMapper;
import com.cl.project.business.domain.KgGiftRule;
import com.cl.project.business.service.IKgGiftRuleService;

/**
 * 赠送规则配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class KgGiftRuleServiceImpl implements IKgGiftRuleService 
{
    @Autowired
    private KgGiftRuleMapper kgGiftRuleMapper;

    /**
     * 查询赠送规则配置
     * 
     * @param ruleId 赠送规则配置主键
     * @return 赠送规则配置
     */
    @Override
    public KgGiftRule selectKgGiftRuleByRuleId(Long ruleId)
    {
        return kgGiftRuleMapper.selectKgGiftRuleByRuleId(ruleId);
    }

    /**
     * 查询赠送规则配置列表
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 赠送规则配置
     */
    @Override
    public List<KgGiftRule> selectKgGiftRuleList(KgGiftRule kgGiftRule)
    {
        return kgGiftRuleMapper.selectKgGiftRuleList(kgGiftRule);
    }

    /**
     * 新增赠送规则配置
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 结果
     */
    @Override
    public int insertKgGiftRule(KgGiftRule kgGiftRule)
    {
        kgGiftRule.setCreateTime(DateUtils.getNowDate());
        return kgGiftRuleMapper.insertKgGiftRule(kgGiftRule);
    }

    /**
     * 修改赠送规则配置
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 结果
     */
    @Override
    public int updateKgGiftRule(KgGiftRule kgGiftRule)
    {
        kgGiftRule.setUpdateTime(DateUtils.getNowDate());
        return kgGiftRuleMapper.updateKgGiftRule(kgGiftRule);
    }

    /**
     * 批量删除赠送规则配置
     * 
     * @param ruleIds 需要删除的赠送规则配置主键
     * @return 结果
     */
    @Override
    public int deleteKgGiftRuleByRuleIds(Long[] ruleIds)
    {
        return kgGiftRuleMapper.deleteKgGiftRuleByRuleIds(ruleIds);
    }

    /**
     * 删除赠送规则配置信息
     * 
     * @param ruleId 赠送规则配置主键
     * @return 结果
     */
    @Override
    public int deleteKgGiftRuleByRuleId(Long ruleId)
    {
        return kgGiftRuleMapper.deleteKgGiftRuleByRuleId(ruleId);
    }

    /**
     * 查询启用的赠送规则列表
     * 
     * @return 启用的赠送规则集合
     */
    @Override
    public List<KgGiftRule> selectActiveGiftRules()
    {
        return kgGiftRuleMapper.selectActiveGiftRules();
    }

    /**
     * 根据规则类型查询启用的规则
     * 
     * @param ruleType 规则类型
     * @return 启用的赠送规则集合
     */
    @Override
    public List<KgGiftRule> selectActiveGiftRulesByType(String ruleType)
    {
        return kgGiftRuleMapper.selectActiveGiftRulesByType(ruleType);
    }
}
