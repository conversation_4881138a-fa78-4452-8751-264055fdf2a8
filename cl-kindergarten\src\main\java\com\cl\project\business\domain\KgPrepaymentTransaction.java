package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 预交款流水对象 kg_prepayment_transaction
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class KgPrepaymentTransaction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 交易ID */
    private Long transactionId;

    /** 账户ID，关联kg_prepayment_account.account_id */
    @Excel(name = "账户ID，关联kg_prepayment_account.account_id")
    private Long accountId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    private String studentName;

    /** 班级名称（冗余字段，便于流水展示） */
    private String className;

    /** 交易类型（recharge充值、consume消费、refund退款、transfer转账、freeze冻结、unfreeze解冻） */
    @Excel(name = "交易类型", readConverterExp = "r=echarge充值、consume消费、refund退款、transfer转账、freeze冻结、unfreeze解冻")
    private String transactionType;

    /** 交易金额 */
    @Excel(name = "交易金额")
    private BigDecimal amount;

    /** 交易前余额 */
    @Excel(name = "交易前余额")
    private BigDecimal beforeBalance;

    /** 交易后余额 */
    @Excel(name = "交易后余额")
    private BigDecimal afterBalance;

    /** 关联账单ID（园费账单或托管费账单） */
    @Excel(name = "关联账单ID", readConverterExp = "园=费账单或托管费账单")
    private Long relatedBillId;

    /** 关联账单类型 */
    @Excel(name = "关联账单类型")
    private String relatedBillType;

    /** 支付方式（wechat微信、alipay支付宝、cash现金、bank银行转账） */
    @Excel(name = "支付方式", readConverterExp = "w=echat微信、alipay支付宝、cash现金、bank银行转账")
    private String paymentMethod;

    /** 交易流水号 */
    @Excel(name = "交易流水号")
    private String transactionNo;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transactionTime;

    /** 操作员ID，关联kg_teacher.teacher_id */
    @Excel(name = "操作员ID，关联kg_teacher.teacher_id")
    private Long operatorId;

    /** 交易描述 */
    @Excel(name = "交易描述")
    private String description;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setStudentName(String studentName)
    {
        this.studentName = studentName;
    }
    public String getStudentName()
    {
        return studentName;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassName() {
        return className;
    }
    public Long getTransactionId() 
    {
        return transactionId;
    }
    public void setAccountId(Long accountId) 
    {
        this.accountId = accountId;
    }

    public Long getAccountId() 
    {
        return accountId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setTransactionType(String transactionType) 
    {
        this.transactionType = transactionType;
    }

    public String getTransactionType() 
    {
        return transactionType;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setBeforeBalance(BigDecimal beforeBalance) 
    {
        this.beforeBalance = beforeBalance;
    }

    public BigDecimal getBeforeBalance() 
    {
        return beforeBalance;
    }
    public void setAfterBalance(BigDecimal afterBalance) 
    {
        this.afterBalance = afterBalance;
    }

    public BigDecimal getAfterBalance() 
    {
        return afterBalance;
    }
    public void setRelatedBillId(Long relatedBillId) 
    {
        this.relatedBillId = relatedBillId;
    }

    public Long getRelatedBillId() 
    {
        return relatedBillId;
    }
    public void setRelatedBillType(String relatedBillType) 
    {
        this.relatedBillType = relatedBillType;
    }

    public String getRelatedBillType() 
    {
        return relatedBillType;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setTransactionNo(String transactionNo) 
    {
        this.transactionNo = transactionNo;
    }

    public String getTransactionNo() 
    {
        return transactionNo;
    }
    public void setTransactionTime(Date transactionTime) 
    {
        this.transactionTime = transactionTime;
    }

    public Date getTransactionTime() 
    {
        return transactionTime;
    }
    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("transactionId", getTransactionId())
            .append("accountId", getAccountId())
            .append("studentId", getStudentId())
            .append("transactionType", getTransactionType())
            .append("amount", getAmount())
            .append("beforeBalance", getBeforeBalance())
            .append("afterBalance", getAfterBalance())
            .append("relatedBillId", getRelatedBillId())
            .append("relatedBillType", getRelatedBillType())
            .append("paymentMethod", getPaymentMethod())
            .append("transactionNo", getTransactionNo())
            .append("transactionTime", getTransactionTime())
            .append("operatorId", getOperatorId())
            .append("description", getDescription())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
