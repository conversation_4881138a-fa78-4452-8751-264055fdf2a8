package com.cl.project.business.dto;

import java.math.BigDecimal;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 预交款统计DTO对象
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class PrepaymentStatisticsDto 
{
    /** 园费预交款总额 */
    @Excel(name = "园费预交款总额")
    private BigDecimal tuitionTotal;

    /** 园费账户学生数 */
    @Excel(name = "园费账户学生数")
    private Integer tuitionStudentCount;

    /** 托管费预交款总额 */
    @Excel(name = "托管费预交款总额")
    private BigDecimal courseTotal;

    /** 托管费账户学生数 */
    @Excel(name = "托管费账户学生数")
    private Integer courseStudentCount;

    /** 余额不足学生数 */
    @Excel(name = "余额不足学生数")
    private Integer insufficientCount;

    /** 负余额学生数 */
    @Excel(name = "负余额学生数")
    private Integer negativeCount;

    /** 总余额 */
    @Excel(name = "总余额")
    private BigDecimal totalBalance;

    /** 总学生数 */
    @Excel(name = "总学生数")
    private Integer totalStudentCount;

    public void setTuitionTotal(BigDecimal tuitionTotal) 
    {
        this.tuitionTotal = tuitionTotal;
    }

    public BigDecimal getTuitionTotal() 
    {
        return tuitionTotal;
    }

    public void setTuitionStudentCount(Integer tuitionStudentCount) 
    {
        this.tuitionStudentCount = tuitionStudentCount;
    }

    public Integer getTuitionStudentCount() 
    {
        return tuitionStudentCount;
    }

    public void setCourseTotal(BigDecimal courseTotal) 
    {
        this.courseTotal = courseTotal;
    }

    public BigDecimal getCourseTotal() 
    {
        return courseTotal;
    }

    public void setCourseStudentCount(Integer courseStudentCount) 
    {
        this.courseStudentCount = courseStudentCount;
    }

    public Integer getCourseStudentCount() 
    {
        return courseStudentCount;
    }

    public void setInsufficientCount(Integer insufficientCount) 
    {
        this.insufficientCount = insufficientCount;
    }

    public Integer getInsufficientCount() 
    {
        return insufficientCount;
    }

    public void setNegativeCount(Integer negativeCount) 
    {
        this.negativeCount = negativeCount;
    }

    public Integer getNegativeCount() 
    {
        return negativeCount;
    }

    public void setTotalBalance(BigDecimal totalBalance) 
    {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getTotalBalance() 
    {
        return totalBalance;
    }

    public void setTotalStudentCount(Integer totalStudentCount) 
    {
        this.totalStudentCount = totalStudentCount;
    }

    public Integer getTotalStudentCount() 
    {
        return totalStudentCount;
    }

    @Override
    public String toString() {
        return "PrepaymentStatisticsDto{" +
                "tuitionTotal=" + tuitionTotal +
                ", tuitionStudentCount=" + tuitionStudentCount +
                ", courseTotal=" + courseTotal +
                ", courseStudentCount=" + courseStudentCount +
                ", insufficientCount=" + insufficientCount +
                ", negativeCount=" + negativeCount +
                ", totalBalance=" + totalBalance +
                ", totalStudentCount=" + totalStudentCount +
                '}';
    }
}
