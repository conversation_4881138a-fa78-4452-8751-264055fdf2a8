package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgPrepaymentAccount;
import com.cl.project.business.service.IKgPrepaymentAccountService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 预交款账户Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/business/account")
public class KgPrepaymentAccountController extends BaseController
{
    @Autowired
    private IKgPrepaymentAccountService kgPrepaymentAccountService;

    /**
     * 查询预交款账户列表
     */
    @SaCheckPermission("business:account:list")
    @GetMapping("/list")
    public TableDataInfo list(KgPrepaymentAccount kgPrepaymentAccount)
    {
        startPage();
        List<KgPrepaymentAccount> list = kgPrepaymentAccountService.selectKgPrepaymentAccountList(kgPrepaymentAccount);
        return getDataTable(list);
    }

    /**
     * 导出预交款账户列表
     */
    @SaCheckPermission("business:account:export")
    @Log(title = "预交款账户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgPrepaymentAccount kgPrepaymentAccount)
    {
        List<KgPrepaymentAccount> list = kgPrepaymentAccountService.selectKgPrepaymentAccountList(kgPrepaymentAccount);
        ExcelUtil<KgPrepaymentAccount> util = new ExcelUtil<KgPrepaymentAccount>(KgPrepaymentAccount.class);
        return util.exportExcel(list, "account");
    }

    /**
     * 获取预交款账户详细信息
     */
    @SaCheckPermission("business:account:query")
    @GetMapping(value = "/{accountId}")
    public AjaxResult getInfo(@PathVariable("accountId") Long accountId)
    {
        return AjaxResult.success(kgPrepaymentAccountService.selectKgPrepaymentAccountById(accountId));
    }

    /**
     * 新增预交款账户
     */
    @SaCheckPermission("business:account:add")
    @Log(title = "预交款账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgPrepaymentAccount kgPrepaymentAccount)
    {
        return toAjax(kgPrepaymentAccountService.insertKgPrepaymentAccount(kgPrepaymentAccount));
    }

    /**
     * 修改预交款账户
     */
    @SaCheckPermission("business:account:edit")
    @Log(title = "预交款账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgPrepaymentAccount kgPrepaymentAccount)
    {
        return toAjax(kgPrepaymentAccountService.updateKgPrepaymentAccount(kgPrepaymentAccount));
    }

    /**
     * 删除预交款账户
     */
    @SaCheckPermission("business:account:remove")
    @Log(title = "预交款账户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{accountIds}")
    public AjaxResult remove(@PathVariable Long[] accountIds)
    {
        return toAjax(kgPrepaymentAccountService.deleteKgPrepaymentAccountByIds(accountIds));
    }
}
