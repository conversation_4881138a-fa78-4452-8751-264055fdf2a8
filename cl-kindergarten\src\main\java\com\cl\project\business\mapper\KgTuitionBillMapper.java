package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgTuitionBill;

/**
 * 园费账单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgTuitionBillMapper 
{
    /**
     * 查询园费账单
     * 
     * @param billId 园费账单ID
     * @return 园费账单
     */
    public KgTuitionBill selectKgTuitionBillById(Long billId);

    /**
     * 查询园费账单列表
     * 
     * @param kgTuitionBill 园费账单
     * @return 园费账单集合
     */
    public List<KgTuitionBill> selectKgTuitionBillList(KgTuitionBill kgTuitionBill);

    /**
     * 新增园费账单
     * 
     * @param kgTuitionBill 园费账单
     * @return 结果
     */
    public int insertKgTuitionBill(KgTuitionBill kgTuitionBill);

    /**
     * 修改园费账单
     * 
     * @param kgTuitionBill 园费账单
     * @return 结果
     */
    public int updateKgTuitionBill(KgTuitionBill kgTuitionBill);

    /**
     * 删除园费账单
     * 
     * @param billId 园费账单ID
     * @return 结果
     */
    public int deleteKgTuitionBillById(Long billId);

    /**
     * 批量删除园费账单
     * 
     * @param billIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgTuitionBillByIds(Long[] billIds);

    KgTuitionBill selectByStudentAndMonth(Long studentId, Long billYear, Long billMonth);

    void deleteByStudentAndMonth(Long studentId, Long billYear, Long billMonth);
}
