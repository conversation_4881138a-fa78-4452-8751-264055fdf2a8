package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTuitionConfig;
import com.cl.project.business.service.IKgTuitionConfigService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 园费配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/tuition-config")
public class KgTuitionConfigController extends BaseController
{
    @Autowired
    private IKgTuitionConfigService kgTuitionConfigService;

    /**
     * 查询园费配置列表
     */
    @SaCheckPermission("business:config:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTuitionConfig kgTuitionConfig)
    {
        startPage();
        List<KgTuitionConfig> list = kgTuitionConfigService.selectKgTuitionConfigList(kgTuitionConfig);
        return getDataTable(list);
    }

    /**
     * 导出园费配置列表
     */
    @SaCheckPermission("business:config:export")
    @Log(title = "园费配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTuitionConfig kgTuitionConfig)
    {
        List<KgTuitionConfig> list = kgTuitionConfigService.selectKgTuitionConfigList(kgTuitionConfig);
        ExcelUtil<KgTuitionConfig> util = new ExcelUtil<KgTuitionConfig>(KgTuitionConfig.class);
        return util.exportExcel(list, "config");
    }

    /**
     * 获取园费配置详细信息
     */
    @SaCheckPermission("business:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(kgTuitionConfigService.selectKgTuitionConfigById(configId));
    }

    /**
     * 新增园费配置
     */
    @SaCheckPermission("business:config:add")
    @Log(title = "园费配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTuitionConfig kgTuitionConfig)
    {
        return toAjax(kgTuitionConfigService.insertKgTuitionConfig(kgTuitionConfig));
    }

    /**
     * 修改园费配置
     */
    @SaCheckPermission("business:config:edit")
    @Log(title = "园费配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTuitionConfig kgTuitionConfig)
    {
        return toAjax(kgTuitionConfigService.updateKgTuitionConfig(kgTuitionConfig));
    }

    /**
     * 删除园费配置
     */
    @SaCheckPermission("business:config:remove")
    @Log(title = "园费配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(kgTuitionConfigService.deleteKgTuitionConfigByIds(configIds));
    }
    
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody KgTuitionConfig config) {
        return toAjax(kgTuitionConfigService.changeStatus(config.getConfigId(), config.getStatus()));
    }

}
