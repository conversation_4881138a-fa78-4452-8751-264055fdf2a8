<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTuitionBillMapper">
    
    <resultMap type="KgTuitionBill" id="KgTuitionBillResult">
        <result property="billId"    column="bill_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="classId"    column="class_id"    />
        <result property="billYear"    column="bill_year"    />
        <result property="billMonth"    column="bill_month"    />
        <result property="totalDays"    column="total_days"    />
        <result property="attendanceDays"    column="attendance_days"    />
        <result property="attendanceRate"    column="attendance_rate"    />
        <result property="mealFeeTotal"    column="meal_fee_total"    />
        <result property="mealFeeUsed"    column="meal_fee_used"    />
        <result property="mealFeeBalance"    column="meal_fee_balance"    />
        <result property="educationFeeTotal"    column="education_fee_total"    />
        <result property="educationFeeUsed"    column="education_fee_used"    />
        <result property="educationFeeBalance"    column="education_fee_balance"    />
        <result property="totalBalance"    column="total_balance"    />
        <result property="nextMonthPrepay"    column="next_month_prepay"    />
        <result property="actualPayment"    column="actual_payment"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="sentTime"    column="sent_time"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgTuitionBillVo">
        select bill_id, student_id, class_id, bill_year, bill_month, total_days, attendance_days, attendance_rate, meal_fee_total, meal_fee_used, meal_fee_balance, education_fee_total, education_fee_used, education_fee_balance, total_balance, next_month_prepay, actual_payment, bill_status, sent_time, paid_time, com_id, create_by, create_time, update_by, update_time, remark from kg_tuition_bill
    </sql>

    <select id="selectKgTuitionBillList" parameterType="KgTuitionBill" resultMap="KgTuitionBillResult">
        <include refid="selectKgTuitionBillVo"/>
        <where>  
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="billYear != null "> and bill_year = #{billYear}</if>
            <if test="billMonth != null "> and bill_month = #{billMonth}</if>
            <if test="totalDays != null "> and total_days = #{totalDays}</if>
            <if test="attendanceDays != null "> and attendance_days = #{attendanceDays}</if>
            <if test="attendanceRate != null "> and attendance_rate = #{attendanceRate}</if>
            <if test="mealFeeTotal != null "> and meal_fee_total = #{mealFeeTotal}</if>
            <if test="mealFeeUsed != null "> and meal_fee_used = #{mealFeeUsed}</if>
            <if test="mealFeeBalance != null "> and meal_fee_balance = #{mealFeeBalance}</if>
            <if test="educationFeeTotal != null "> and education_fee_total = #{educationFeeTotal}</if>
            <if test="educationFeeUsed != null "> and education_fee_used = #{educationFeeUsed}</if>
            <if test="educationFeeBalance != null "> and education_fee_balance = #{educationFeeBalance}</if>
            <if test="totalBalance != null "> and total_balance = #{totalBalance}</if>
            <if test="nextMonthPrepay != null "> and next_month_prepay = #{nextMonthPrepay}</if>
            <if test="actualPayment != null "> and actual_payment = #{actualPayment}</if>
            <if test="billStatus != null  and billStatus != ''"> and bill_status = #{billStatus}</if>
            <if test="sentTime != null "> and sent_time = #{sentTime}</if>
            <if test="paidTime != null "> and paid_time = #{paidTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTuitionBillById" parameterType="Long" resultMap="KgTuitionBillResult">
        <include refid="selectKgTuitionBillVo"/>
        where bill_id = #{billId}
    </select>
    <select id="selectByStudentAndMonth" resultMap="KgTuitionBillResult">
        select bill_id, student_id, class_id, bill_year, bill_month, total_days, attendance_days, attendance_rate, meal_fee_total, meal_fee_used, meal_fee_balance, education_fee_total, education_fee_used, education_fee_balance, total_balance, next_month_prepay, actual_payment, bill_status, sent_time, paid_time, com_id, create_by, create_time, update_by, update_time, remark
        from kg_tuition_bill
        where student_id = #{studentId} and bill_year = #{billYear} and bill_month = #{billMonth}
    </select>

    <insert id="insertKgTuitionBill" parameterType="KgTuitionBill" useGeneratedKeys="true" keyProperty="billId">
        insert into kg_tuition_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="billYear != null">bill_year,</if>
            <if test="billMonth != null">bill_month,</if>
            <if test="totalDays != null">total_days,</if>
            <if test="attendanceDays != null">attendance_days,</if>
            <if test="attendanceRate != null">attendance_rate,</if>
            <if test="mealFeeTotal != null">meal_fee_total,</if>
            <if test="mealFeeUsed != null">meal_fee_used,</if>
            <if test="mealFeeBalance != null">meal_fee_balance,</if>
            <if test="educationFeeTotal != null">education_fee_total,</if>
            <if test="educationFeeUsed != null">education_fee_used,</if>
            <if test="educationFeeBalance != null">education_fee_balance,</if>
            <if test="totalBalance != null">total_balance,</if>
            <if test="nextMonthPrepay != null">next_month_prepay,</if>
            <if test="actualPayment != null">actual_payment,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="sentTime != null">sent_time,</if>
            <if test="paidTime != null">paid_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="billYear != null">#{billYear},</if>
            <if test="billMonth != null">#{billMonth},</if>
            <if test="totalDays != null">#{totalDays},</if>
            <if test="attendanceDays != null">#{attendanceDays},</if>
            <if test="attendanceRate != null">#{attendanceRate},</if>
            <if test="mealFeeTotal != null">#{mealFeeTotal},</if>
            <if test="mealFeeUsed != null">#{mealFeeUsed},</if>
            <if test="mealFeeBalance != null">#{mealFeeBalance},</if>
            <if test="educationFeeTotal != null">#{educationFeeTotal},</if>
            <if test="educationFeeUsed != null">#{educationFeeUsed},</if>
            <if test="educationFeeBalance != null">#{educationFeeBalance},</if>
            <if test="totalBalance != null">#{totalBalance},</if>
            <if test="nextMonthPrepay != null">#{nextMonthPrepay},</if>
            <if test="actualPayment != null">#{actualPayment},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="sentTime != null">#{sentTime},</if>
            <if test="paidTime != null">#{paidTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTuitionBill" parameterType="KgTuitionBill">
        update kg_tuition_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="billYear != null">bill_year = #{billYear},</if>
            <if test="billMonth != null">bill_month = #{billMonth},</if>
            <if test="totalDays != null">total_days = #{totalDays},</if>
            <if test="attendanceDays != null">attendance_days = #{attendanceDays},</if>
            <if test="attendanceRate != null">attendance_rate = #{attendanceRate},</if>
            <if test="mealFeeTotal != null">meal_fee_total = #{mealFeeTotal},</if>
            <if test="mealFeeUsed != null">meal_fee_used = #{mealFeeUsed},</if>
            <if test="mealFeeBalance != null">meal_fee_balance = #{mealFeeBalance},</if>
            <if test="educationFeeTotal != null">education_fee_total = #{educationFeeTotal},</if>
            <if test="educationFeeUsed != null">education_fee_used = #{educationFeeUsed},</if>
            <if test="educationFeeBalance != null">education_fee_balance = #{educationFeeBalance},</if>
            <if test="totalBalance != null">total_balance = #{totalBalance},</if>
            <if test="nextMonthPrepay != null">next_month_prepay = #{nextMonthPrepay},</if>
            <if test="actualPayment != null">actual_payment = #{actualPayment},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="sentTime != null">sent_time = #{sentTime},</if>
            <if test="paidTime != null">paid_time = #{paidTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where bill_id = #{billId}
    </update>

    <delete id="deleteKgTuitionBillById" parameterType="Long">
        delete from kg_tuition_bill where bill_id = #{billId}
    </delete>

    <delete id="deleteKgTuitionBillByIds" parameterType="String">
        delete from kg_tuition_bill where bill_id in 
        <foreach item="billId" collection="array" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </delete>
    <delete id="deleteByStudentAndMonth">
        delete from kg_tuition_bill where student_id = #{studentId} and bill_month = #{billMonth} and bill_year = #{billYear}
    </delete>

</mapper>