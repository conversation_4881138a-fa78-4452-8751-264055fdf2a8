<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师课时统计</text>
				</view>
				<view class="nav-right" @click="handleCalculate">
					<u-icon name="reload" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 查询条件 -->
		<view class="search-section">
			<view class="search-row">
				<view class="search-item">
					<text class="search-label">统计月份</text>
					<view class="search-input" @click="showDatePicker = true">
						<text class="search-value">{{ currentDateText }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<view class="search-row">
				<view class="search-item">
					<text class="search-label">教师筛选</text>
					<view class="search-input" @click="showTeacherPicker = true">
						<text class="search-value">{{ selectedTeacherName || '全部教师' }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<view class="search-row">
				<view class="search-item">
					<text class="search-label">课程筛选</text>
					<view class="search-input" @click="showCoursePicker = true">
						<text class="search-value">{{ selectedCourseName || '全部课程' }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="stats-overview">
			<view class="stats-grid">
				<view class="stat-card">
					<view class="stat-icon teacher">👨‍🏫</view>
					<view class="stat-content">
						<text class="stat-number">{{ summary.totalTeachers }}</text>
						<text class="stat-label">授课教师数</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon sessions">⏰</view>
					<view class="stat-content">
						<text class="stat-number">{{ summary.totalSessions }}</text>
						<text class="stat-label">总授课节数</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon fee">💰</view>
					<view class="stat-content">
						<text class="stat-number">¥{{ summary.totalCourseFee }}</text>
						<text class="stat-label">总课时费</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon average">📊</view>
					<view class="stat-content">
						<text class="stat-number">¥{{ summary.avgFeePerSession }}</text>
						<text class="stat-label">平均单节费用</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 教师列表 -->
		<view class="teacher-list">
			<view v-if="loading" class="loading-container">
				<u-loading-icon mode="circle"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>

			<view v-else-if="teacherStatsList.length === 0" class="empty-state">
				<view class="empty-icon">📋</view>
				<text class="empty-title">暂无数据</text>
				<text class="empty-desc">当前条件下没有找到教师课时统计数据</text>
			</view>

			<view v-else>
				<view
					v-for="(teacher, index) in teacherStatsList"
					:key="teacher.teacherId || index"
					class="teacher-card"
					@click="handleViewDetail(teacher)"
				>
					<view class="teacher-header">
						<view class="teacher-info">
							<text class="teacher-name">{{ teacher.teacherName }}</text>
							<text class="teacher-code">编号: {{ teacher.teacherCode }}</text>
						</view>
						<view class="teacher-summary">
							<text class="total-sessions">{{ teacher.totalSessions }}节</text>
							<text class="total-fee">¥{{ teacher.totalCourseFee }}</text>
						</view>
					</view>

					<view class="course-details" v-if="teacher.courseDetails && teacher.courseDetails.length > 0">
						<view
							v-for="course in teacher.courseDetails"
							:key="course.courseId"
							class="course-item"
						>
							<view class="course-name">{{ course.courseName }}</view>
							<view class="course-stats">
								<text class="course-sessions">{{ course.sessions }}节</text>
								<text class="course-price">¥{{ course.pricePerSession }}/节</text>
								<text class="course-subtotal">¥{{ course.subtotal }}</text>
							</view>
						</view>
					</view>

					<view v-else class="no-course-data">
						<text>暂无授课记录</text>
					</view>

					<view class="teacher-actions">
						<view class="action-btn detail-btn" @click.stop="handleViewDetail(teacher)">
							<u-icon name="eye" size="14"></u-icon>
							<text>详细记录</text>
						</view>
						<view class="action-btn recalc-btn" @click.stop="handleRecalculate(teacher)">
							<u-icon name="reload" size="14"></u-icon>
							<text>重新计算</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器 -->
		<u-popup v-model="showDatePicker" mode="bottom">
			<u-datetime-picker
				mode="year-month"
				@confirm="onDateConfirm"
				@cancel="showDatePicker = false"
			></u-datetime-picker>
		</u-popup>

		<!-- 教师选择器 -->
		<u-popup v-model="showTeacherPicker" mode="bottom">
			<u-picker
				:range="teacherPickerOptions"
				range-key="label"
				@confirm="onTeacherConfirm"
				@cancel="showTeacherPicker = false"
			></u-picker>
		</u-popup>

		<!-- 课程选择器 -->
		<u-popup v-model="showCoursePicker" mode="bottom">
			<u-picker
				:range="coursePickerOptions"
				range-key="label"
				@confirm="onCourseConfirm"
				@cancel="showCoursePicker = false"
			></u-picker>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import {
	getTeacherCourseStatsList,
	getTeacherCourseStatsSummary,
	calculateTeacherCourseStats,
	recalculateTeacherCourseStats,
	getTeacherCourseDetails
} from '@/api/api.js'
import { listAllTeacher, listAllCourse } from '@/api/api.js'

export default {
	data() {
		return {
			loading: false,

			// 查询参数
			queryParams: {
				statYear: new Date().getFullYear(),
				statMonth: new Date().getMonth() + 1,
				teacherId: null,
				courseId: null
			},

			// 统计数据
			teacherStatsList: [],
			summary: {
				totalTeachers: 0,
				totalSessions: 0,
				totalCourseFee: 0,
				avgFeePerSession: 0
			},

			// 选择器相关
			showDatePicker: false,
			showTeacherPicker: false,
			showCoursePicker: false,

			// 选项数据
			teacherOptions: [],
			courseOptions: [],

			// 当前选择
			selectedTeacherId: null,
			selectedTeacherName: '',
			selectedCourseId: null,
			selectedCourseName: '',

			// 详情相关
			currentTeacher: {},
			attendanceDetails: [],

			// 计算相关
			calculateLoading: false
		}
	},

	computed: {
		// 当前日期文本
		currentDateText() {
			return `${this.queryParams.statYear}年${this.queryParams.statMonth}月`
		},

		// 教师选择器选项
		teacherPickerOptions() {
			const options = [{ label: '全部教师', value: null }]
			this.teacherOptions.forEach(teacher => {
				options.push({
					label: teacher.teacherName,
					value: teacher.teacherId
				})
			})
			return options
		},

		// 课程选择器选项
		coursePickerOptions() {
			const options = [{ label: '全部课程', value: null }]
			this.courseOptions.forEach(course => {
				options.push({
					label: course.courseName,
					value: course.courseId
				})
			})
			return options
		}
	},

	onLoad() {
		this.initData()
	},

	onShow() {
		this.getList()
	},

	onPullDownRefresh() {
		this.getList().then(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		/** 初始化数据 */
		async initData() {
			try {
				// 获取教师列表
				await this.loadTeachers()
				// 获取课程列表
				await this.loadCourses()
				// 获取统计数据
				await this.getList()
			} catch (error) {
				console.error('初始化数据失败:', error)
				toast('数据加载失败，请重试')
			}
		},

		/** 加载教师列表 */
		async loadTeachers() {
			try {
				const response = await listAllTeacher()
				if (response.code === 200) {
					this.teacherOptions = response.data || []
				}
			} catch (error) {
				console.error('加载教师列表失败:', error)
			}
		},

		/** 加载课程列表 */
		async loadCourses() {
			try {
				const response = await listAllCourse()
				if (response.code === 200) {
					this.courseOptions = response.data || []
				}
			} catch (error) {
				console.error('加载课程列表失败:', error)
			}
		},

		/** 获取统计列表 */
		async getList() {
			this.loading = true
			try {
				// 获取统计列表
				const listResponse = await getTeacherCourseStatsList(this.queryParams)
				if (listResponse.code === 200) {
					this.teacherStatsList = listResponse.data || []
				}

				// 获取汇总数据
				const summaryResponse = await getTeacherCourseStatsSummary(this.queryParams)
				if (summaryResponse.code === 200) {
					this.summary = summaryResponse.data || {
						totalTeachers: 0,
						totalSessions: 0,
						totalCourseFee: 0,
						avgFeePerSession: 0
					}
				}
			} catch (error) {
				console.error('获取统计数据失败:', error)
				toast('获取数据失败，请重试')
			} finally {
				this.loading = false
			}
		},

		/** 返回上一页 */
		goBack() {
			uni.navigateBack()
		},

		/** 日期选择确认 */
		onDateConfirm(e) {
			const date = new Date(e.value)
			this.queryParams.statYear = date.getFullYear()
			this.queryParams.statMonth = date.getMonth() + 1
			this.showDatePicker = false
			this.getList()
		},

		/** 教师选择确认 */
		onTeacherConfirm(e) {
			const selected = this.teacherPickerOptions[e.index]
			this.selectedTeacherId = selected.value
			this.selectedTeacherName = selected.value ? selected.label : ''
			this.queryParams.teacherId = selected.value
			this.showTeacherPicker = false
			this.getList()
		},

		/** 课程选择确认 */
		onCourseConfirm(e) {
			const selected = this.coursePickerOptions[e.index]
			this.selectedCourseId = selected.value
			this.selectedCourseName = selected.value ? selected.label : ''
			this.queryParams.courseId = selected.value
			this.showCoursePicker = false
			this.getList()
		},

		/** 查看详细记录 */
		async handleViewDetail(teacher) {
			try {
				this.currentTeacher = teacher

				// 获取详细记录
				const response = await getTeacherCourseDetails(teacher.teacherId, {
					statYear: this.queryParams.statYear,
					statMonth: this.queryParams.statMonth
				})

				if (response.code === 200) {
					this.attendanceDetails = response.data || []

					// 跳转到详情页面
					uni.navigateTo({
						url: `/pages/admin/custody/statistics/detail?teacherId=${teacher.teacherId}&teacherName=${encodeURIComponent(teacher.teacherName)}&year=${this.queryParams.statYear}&month=${this.queryParams.statMonth}`
					})
				} else {
					toast('获取详细记录失败')
				}
			} catch (error) {
				console.error('获取详细记录失败:', error)
				toast('获取详细记录失败，请重试')
			}
		},

		/** 重新计算 */
		async handleRecalculate(teacher) {
			uni.showModal({
				title: '确认重新计算',
				content: `确定要重新计算 ${teacher.teacherName} 的课时费吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '计算中...' })

							const response = await recalculateTeacherCourseStats({
								statYear: this.queryParams.statYear,
								statMonth: this.queryParams.statMonth,
								teacherId: teacher.teacherId
							})

							if (response.code === 200) {
								toast('重新计算成功')
								this.getList()
							} else {
								toast(response.msg || '重新计算失败')
							}
						} catch (error) {
							console.error('重新计算失败:', error)
							toast('重新计算失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},

		/** 计算课时费 */
		async handleCalculate() {
			uni.showModal({
				title: '计算课时费',
				content: `确定要计算 ${this.currentDateText} 的课时费吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							this.calculateLoading = true
							uni.showLoading({ title: '计算中...' })

							const response = await calculateTeacherCourseStats({
								statYear: this.queryParams.statYear,
								statMonth: this.queryParams.statMonth,
								calculateType: 'all',
								confirmedOnly: true
							})

							if (response.code === 200) {
								const count = response.data?.processedCount || 0
								toast(`成功计算 ${count} 位教师的课时费`)
								this.getList()
							} else {
								toast(response.msg || '计算失败')
							}
						} catch (error) {
							console.error('计算课时费失败:', error)
							toast('计算失败，请重试')
						} finally {
							this.calculateLoading = false
							uni.hideLoading()
						}
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left, .nav-right {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.nav-right {
	margin-left: auto;
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 查询条件 */
.search-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-row {
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.search-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.search-label {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	min-width: 140rpx;
}

.search-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-left: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;

	&:active {
		border-color: #FF9800;
		background: #fff5e6;
	}
}

.search-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

/* 统计概览 */
.stats-overview {
	margin: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.stat-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.stat-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	margin-right: 20rpx;

	&.teacher {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	&.sessions {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	}

	&.fee {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	}

	&.average {
		background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
	}
}

.stat-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.stat-number {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

/* 教师列表 */
.teacher-list {
	margin: 20rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
	margin-top: 20rpx;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 26rpx;
	color: #666666;
	display: block;
	line-height: 1.5;
}

/* 教师卡片 */
.teacher-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	}

	&:last-child {
		margin-bottom: 0;
	}
}

.teacher-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.teacher-info {
	flex: 1;
}

.teacher-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.teacher-code {
	font-size: 24rpx;
	color: #666666;
	display: block;
}

.teacher-summary {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.total-sessions {
	font-size: 28rpx;
	color: #FF9800;
	font-weight: 600;
	margin-bottom: 4rpx;
}

.total-fee {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
}

/* 课程详情 */
.course-details {
	margin-bottom: 24rpx;
}

.course-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 12rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.course-name {
	font-size: 26rpx;
	color: #333333;
	font-weight: 500;
	flex: 1;
}

.course-stats {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.course-sessions {
	font-size: 24rpx;
	color: #666666;
}

.course-price {
	font-size: 24rpx;
	color: #999999;
}

.course-subtotal {
	font-size: 26rpx;
	color: #FF9800;
	font-weight: 600;
}

.no-course-data {
	text-align: center;
	padding: 32rpx;
	color: #999999;
	font-size: 26rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 24rpx;
}

/* 操作按钮 */
.teacher-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;

	text {
		margin-left: 8rpx;
	}

	&:active {
		transform: scale(0.95);
	}
}

.detail-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
}

.recalc-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: #ffffff;
}
</style>
