<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师课时统计</text>
				</view>
				<view class="nav-right" @click="handleCalculate">
					<u-icon name="reload" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 查询条件 -->
		<view class="search-section">
			<view class="search-row">
				<view class="search-item">
					<text class="search-label">统计月份</text>
					<view class="search-input" @click="openDatePicker">
						<text class="search-value">{{ currentDateText }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<view class="search-row">
				<view class="search-item">
					<text class="search-label">教师筛选</text>
					<view class="search-input" @click="openTeacherPicker">
						<text class="search-value">{{ selectedTeacherName || '全部教师' }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<view class="search-row">
				<view class="search-item">
					<text class="search-label">课程筛选</text>
					<view class="search-input" @click="openCoursePicker">
						<text class="search-value">{{ selectedCourseName || '全部课程' }}</text>
						<u-icon name="arrow-down" size="14" color="#999"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="stats-overview">
			<view class="stats-grid">
				<view class="stat-card">
					<view class="stat-icon teacher">👨‍🏫</view>
					<view class="stat-content">
						<text class="stat-number">{{ summary.totalTeachers }}</text>
						<text class="stat-label">授课教师数</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon sessions">⏰</view>
					<view class="stat-content">
						<text class="stat-number">{{ summary.totalSessions }}</text>
						<text class="stat-label">总授课节数</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon fee">💰</view>
					<view class="stat-content">
						<text class="stat-number">¥{{ summary.totalCourseFee }}</text>
						<text class="stat-label">总课时费</text>
					</view>
				</view>
				<view class="stat-card">
					<view class="stat-icon average">📊</view>
					<view class="stat-content">
						<text class="stat-number">¥{{ summary.avgFeePerSession }}</text>
						<text class="stat-label">平均单节费用</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 教师列表 -->
		<view class="teacher-list">
			<view v-if="loading" class="loading-container">
				<u-loading-icon mode="circle"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>

			<view v-else-if="teacherStatsList.length === 0" class="empty-state">
				<view class="empty-icon">📋</view>
				<text class="empty-title">暂无数据</text>
				<text class="empty-desc">当前条件下没有找到教师课时统计数据</text>
			</view>

			<view v-else>
				<view
					v-for="(teacher, index) in teacherStatsList"
					:key="teacher.teacherId || index"
					class="teacher-card"
					@click="handleViewDetail(teacher)"
				>
					<view class="teacher-header">
						<view class="teacher-info">
							<text class="teacher-name">{{ teacher.teacherName }}</text>
							<text class="teacher-code">编号: {{ teacher.teacherCode }}</text>
						</view>
						<view class="teacher-summary">
							<text class="total-sessions">{{ teacher.totalSessions }}节</text>
							<text class="total-fee">¥{{ teacher.totalCourseFee }}</text>
						</view>
					</view>

					<view class="course-details" v-if="teacher.courseDetails && teacher.courseDetails.length > 0">
						<view
							v-for="course in teacher.courseDetails"
							:key="course.courseId"
							class="course-item"
						>
							<view class="course-name">{{ course.courseName }}</view>
							<view class="course-stats">
								<text class="course-sessions">{{ course.sessions }}节</text>
								<text class="course-price">¥{{ course.pricePerSession }}/节</text>
								<text class="course-subtotal">¥{{ course.subtotal }}</text>
							</view>
						</view>
					</view>

					<view v-else class="no-course-data">
						<text>暂无授课记录</text>
					</view>

					<view class="teacher-actions">
						<view class="action-btn detail-btn" @click.stop="handleViewDetail(teacher)">
							<u-icon name="eye" size="14"></u-icon>
							<text>详细记录</text>
						</view>
						<view class="action-btn recalc-btn" @click.stop="handleRecalculate(teacher)">
							<u-icon name="reload" size="14"></u-icon>
							<text>重新计算</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器弹窗 -->
		<u-popup v-model="showDatePicker" mode="bottom" border-radius="20">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showDatePicker = false">取消</text>
					<text class="picker-title">选择年月</text>
					<text class="picker-confirm" @click="confirmDatePicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="datePickerValue" @change="onDatePickerChange">
					<picker-view-column>
						<view v-for="(year, index) in yearOptions" :key="index" class="picker-item">
							{{ year }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(month, index) in monthOptions" :key="index" class="picker-item">
							{{ month }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 教师选择器弹窗 -->
		<u-popup v-model="showTeacherPicker" mode="bottom" border-radius="20">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showTeacherPicker = false">取消</text>
					<text class="picker-title">选择教师</text>
					<text class="picker-confirm" @click="confirmTeacherPicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="[teacherPickerValue]" @change="onTeacherPickerChange">
					<picker-view-column>
						<view v-for="(teacher, index) in teacherPickerOptions" :key="index" class="picker-item">
							{{ teacher.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 课程选择器弹窗 -->
		<u-popup v-model="showCoursePicker" mode="bottom" border-radius="20">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showCoursePicker = false">取消</text>
					<text class="picker-title">选择课程</text>
					<text class="picker-confirm" @click="confirmCoursePicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="[coursePickerValue]" @change="onCoursePickerChange">
					<picker-view-column>
						<view v-for="(course, index) in coursePickerOptions" :key="index" class="picker-item">
							{{ course.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import {
	getTeacherCourseStatsList as listTeacherCourseStats,
	getTeacherCourseStatsSummary as getTeacherCourseSummary,
	calculateTeacherCourseStats as calculateTeacherCourseFee,
	recalculateTeacherCourseStats,
	getTeacherCourseDetails as getTeacherAttendanceDetails
} from '@/api/api.js'
import { listAllTeacher, listAllCourse } from '@/api/api.js'

export default {
	data() {
		return {
			// 与web端保持一致的数据结构
			teacherStatsList: [],
			teacherOptions: [],
			courseOptions: [],
			attendanceDetails: [],
			loading: true,
			showSearch: true,
			queryDate: new Date(),
			detailDialogVisible: false,
			calculateDialogVisible: false,
			calculateLoading: false,
			currentTeacher: {},

			// 统计汇总数据
			summary: {
				totalTeachers: 0,
				totalSessions: 0,
				totalCourseFee: 0,
				avgFeePerSession: 0
			},

			// 查询参数
			queryParams: {
				statYear: new Date().getFullYear(),
				statMonth: new Date().getMonth() + 1,
				teacherId: null,
				courseId: null
			},

			// 计算表单
			calculateForm: {
				calculateMonth: new Date(),
				calculateType: 'all',
				teacherId: null,
				confirmedOnly: true
			},

			// 选择器相关
			showDatePicker: false,
			showTeacherPicker: false,
			showCoursePicker: false,

			// 选择器值
			datePickerValue: [0, 0], // 年份和月份的索引
			teacherPickerValue: 0, // 教师索引
			coursePickerValue: 0, // 课程索引

			// 临时选择值（确认前的值）
			tempDateValue: [0, 0],
			tempTeacherValue: 0,
			tempCourseValue: 0,

			// 选项数据
			teacherOptions: [],
			courseOptions: [],

			// 当前选择
			selectedTeacherId: null,
			selectedTeacherName: '',
			selectedCourseId: null,
			selectedCourseName: '',

			// 详情相关
			currentTeacher: {},
			attendanceDetails: [],

			// 计算相关
			calculateLoading: false
		}
	},

	computed: {
		// 当前日期文本
		currentDateText() {
			return `${this.queryParams.statYear}年${this.queryParams.statMonth}月`
		},

		// 年份选项
		yearOptions() {
			const currentYear = new Date().getFullYear()
			const years = []
			// 生成年份范围（当前年份前后3年）
			for (let i = currentYear - 3; i <= currentYear + 1; i++) {
				years.push(`${i}年`)
			}
			return years
		},

		// 月份选项
		monthOptions() {
			const months = []
			for (let i = 1; i <= 12; i++) {
				months.push(`${i}月`)
			}
			return months
		},

		// 教师选择器选项
		teacherPickerOptions() {
			const options = [{ label: '全部教师', value: null }]
			this.teacherOptions.forEach(teacher => {
				options.push({
					label: teacher.teacherName,
					value: teacher.teacherId
				})
			})
			return options
		},

		// 课程选择器选项
		coursePickerOptions() {
			const options = [{ label: '全部课程', value: null }]
			this.courseOptions.forEach(course => {
				options.push({
					label: course.courseName,
					value: course.courseId
				})
			})
			return options
		}
	},

	onLoad() {
		// 与web端mounted逻辑保持一致
		try {
			// 默认设置为当前月份
			const currentDate = new Date()
			this.queryDate = new Date(currentDate)
			this.calculateForm.calculateMonth = new Date(currentDate)

			// 初始化日期选择器
			this.initDatePicker()

			// 确保日期是有效的
			const dateStr = currentDate.toISOString().slice(0, 7)
			this.handleDateChange(dateStr)

			// 加载基础数据
			this.loadTeacherOptions()
			this.loadCourseOptions()
			this.getList()
		} catch (error) {
			console.error('Initialization error:', error)
			// 如果初始化失败，确保有默认值
			const currentDate = new Date()
			this.queryParams.statYear = currentDate.getFullYear()
			this.queryParams.statMonth = currentDate.getMonth() + 1
			this.queryDate = new Date(currentDate)
			this.getList() // 确保数据能正常加载
		}
	},

	onShow() {
		// 页面显示时不重复加载，避免重复请求
	},

	onPullDownRefresh() {
		this.getList().then(() => {
			uni.stopPullDownRefresh()
		})
	},

	methods: {
		/** 初始化日期选择器 */
		initDatePicker() {
			const currentYear = new Date().getFullYear()
			const currentMonth = new Date().getMonth() + 1

			// 设置当前年月为默认值
			this.queryParams.statYear = currentYear
			this.queryParams.statMonth = currentMonth

			// 计算在选择器中的索引位置
			const yearIndex = 3 // 当前年份在范围中的位置（前面有3年）
			const monthIndex = currentMonth - 1 // 月份索引从0开始

			this.datePickerValue = [yearIndex, monthIndex]
			this.tempDateValue = [yearIndex, monthIndex]
		},

		/** 加载教师选项 - 与web端loadTeacherOptions方法保持一致 */
		loadTeacherOptions() {
			listAllTeacher().then(response => {
				this.teacherOptions = response.data || []
			}).catch(error => {
				console.error('加载教师列表失败:', error)
			})
		},

		/** 加载课程选项 - 与web端loadCourseOptions方法保持一致 */
		loadCourseOptions() {
			listAllCourse().then(response => {
				this.courseOptions = response.data || []
			}).catch(error => {
				console.error('加载课程列表失败:', error)
			})
		},

		/** 查询统计列表 - 与web端getList方法保持一致 */
		getList() {
			console.log('=== getList 被调用 ===')
			console.log('发送到后端的查询参数:', this.queryParams)

			this.loading = true
			listTeacherCourseStats(this.queryParams).then(response => {
				this.teacherStatsList = response.data || []
				this.loading = false
			}).catch(() => {
				this.loading = false
				toast('获取统计列表失败，请重试')
			})

			// 获取汇总数据
			getTeacherCourseSummary(this.queryParams).then(response => {
				this.summary = response.data || {
					totalTeachers: 0,
					totalSessions: 0,
					totalCourseFee: 0,
					avgFeePerSession: 0
				}
			}).catch(() => {
				// 如果获取汇总数据失败，保持默认值
				console.warn('Failed to load teacher course summary')
			})
		},

		/** 搜索按钮操作 - 与web端handleQuery方法保持一致 */
		handleQuery() {
			this.getList()
		},

		/** 日期变化处理 - 与web端handleDateChange方法保持一致 */
		handleDateChange(value) {
			console.log('=== handleDateChange 被调用 ===')
			console.log('传入的 value:', value)
			console.log('value 类型:', typeof value)
			console.log('当前 queryDate:', this.queryDate)
			console.log('当前 queryParams:', this.queryParams)

			if (value) {
				try {
					let yearNum, monthNum

					if (value instanceof Date) {
						// 直接使用Date对象的方法，注意月份需要+1
						yearNum = value.getFullYear()
						monthNum = value.getMonth() + 1 // ❗ JavaScript月份从0开始，需要+1
						console.log('从 Date 对象解析:', { 原始月份: value.getMonth(), 修正后月份: monthNum })
					} else if (typeof value === 'string') {
						// 如果是字符串，验证格式后解析
						if (!/^\d{4}-\d{2}$/.test(value)) {
							console.warn('Invalid date format:', value)
							return
						}
						const [year, month] = value.split('-')
						yearNum = parseInt(year, 10)
						monthNum = parseInt(month, 10)
					} else {
						console.warn('Invalid date value:', value)
						return
					}

					// 验证解析结果
					if (isNaN(yearNum) || isNaN(monthNum) || yearNum < 2000 || yearNum > 3000 || monthNum < 1 || monthNum > 12) {
						console.warn('Invalid year or month:', { year: yearNum, month: monthNum })
						return
					}

					console.log('最终解析结果:', { year: yearNum, month: monthNum })
					this.queryParams.statYear = yearNum
					this.queryParams.statMonth = monthNum
					// 日期改变后自动查询
					console.log('更新后的 queryParams:', this.queryParams)
					this.getList()
				} catch (error) {
					console.error('Error processing date:', error)
					this.queryParams.statYear = null
					this.queryParams.statMonth = null
				}
			} else {
				console.log('日期被清空，使用当前月份作为默认值')
				// 当用户清空日期时，自动设置为当前月份，确保始终有值
				const currentDate = new Date()
				this.queryParams.statYear = currentDate.getFullYear()
				this.queryParams.statMonth = currentDate.getMonth() + 1
				// 同时更新日期选择器的值
				this.queryDate = new Date(currentDate)
				console.log('恢复默认值后的 queryParams:', this.queryParams)
				this.getList()
			}

			console.log('=== handleDateChange 结束 ===')
		},

		/** 返回上一页 */
		goBack() {
			uni.navigateBack()
		},

		/** 打开日期选择器 */
		openDatePicker() {
			this.tempDateValue = [...this.datePickerValue]
			this.showDatePicker = true
		},

		/** 打开教师选择器 */
		openTeacherPicker() {
			this.tempTeacherValue = this.teacherPickerValue
			this.showTeacherPicker = true
		},

		/** 打开课程选择器 */
		openCoursePicker() {
			this.tempCourseValue = this.coursePickerValue
			this.showCoursePicker = true
		},

		/** 日期选择器变化 */
		onDatePickerChange(e) {
			this.tempDateValue = e.detail.value
		},

		/** 确认日期选择 */
		confirmDatePicker() {
			const [yearIndex, monthIndex] = this.tempDateValue
			const currentYear = new Date().getFullYear()

			// 计算实际年份和月份
			const year = currentYear - 3 + yearIndex
			const month = monthIndex + 1

			// 更新选择器的值
			this.datePickerValue = [yearIndex, monthIndex]

			// 构造日期字符串并调用handleDateChange，保持与web端一致
			const dateStr = `${year}-${month.toString().padStart(2, '0')}`
			this.handleDateChange(dateStr)

			this.showDatePicker = false
		},

		/** 教师选择器变化 */
		onTeacherPickerChange(e) {
			this.tempTeacherValue = e.detail.value[0]
		},

		/** 确认教师选择 */
		confirmTeacherPicker() {
			const selected = this.teacherPickerOptions[this.tempTeacherValue]
			this.selectedTeacherId = selected.value
			this.selectedTeacherName = selected.value ? selected.label : ''
			this.queryParams.teacherId = selected.value
			this.teacherPickerValue = this.tempTeacherValue
			this.showTeacherPicker = false
			this.handleQuery() // 调用handleQuery保持与web端一致
		},

		/** 课程选择器变化 */
		onCoursePickerChange(e) {
			this.tempCourseValue = e.detail.value[0]
		},

		/** 确认课程选择 */
		confirmCoursePicker() {
			const selected = this.coursePickerOptions[this.tempCourseValue]
			this.selectedCourseId = selected.value
			this.selectedCourseName = selected.value ? selected.label : ''
			this.queryParams.courseId = selected.value
			this.coursePickerValue = this.tempCourseValue
			this.showCoursePicker = false
			this.handleQuery() // 调用handleQuery保持与web端一致
		},

		/** 查看详细记录 - 与web端handleViewDetail方法保持一致 */
		handleViewDetail(row) {
			console.log('查看详细记录 - 教师信息:', row)
			console.log('查询参数:', this.queryParams)

			if (!this.queryParams.statYear || !this.queryParams.statMonth) {
				toast("请先选择统计月份")
				return
			}

			this.currentTeacher = row
			const params = {
				statYear: this.queryParams.statYear,
				statMonth: this.queryParams.statMonth
			}

			console.log('调用API参数:', { teacherId: row.teacherId, params })

			getTeacherAttendanceDetails(row.teacherId, params).then(response => {
				console.log('API返回数据:', response)
				this.attendanceDetails = response.data || []

				// 跳转到详情页面
				uni.navigateTo({
					url: `/pages/admin/custody/statistics/detail?teacherId=${row.teacherId}&teacherName=${encodeURIComponent(row.teacherName)}&year=${this.queryParams.statYear}&month=${this.queryParams.statMonth}`
				})

				console.log('详细数据:', this.attendanceDetails)
			}).catch(error => {
				console.error('获取详细记录失败:', error)
				toast('获取详细记录失败: ' + (error.message || '未知错误'))
			})
		},

		/** 重新计算单个教师 - 与web端handleRecalculate方法保持一致 */
		handleRecalculate(row) {
			if (!this.queryParams.statYear || !this.queryParams.statMonth) {
				toast("请先选择统计月份")
				return
			}

			const params = {
				statYear: this.queryParams.statYear,
				statMonth: this.queryParams.statMonth,
				teacherId: row.teacherId
			}

			uni.showModal({
				title: '确认',
				content: `确认重新计算教师"${row.teacherName}"的课时费吗？`,
				success: (res) => {
					if (res.confirm) {
						recalculateTeacherStats(params).then(() => {
							toast("重新计算成功")
							this.getList()
						}).catch(error => {
							console.error('重新计算失败:', error)
							toast('重新计算失败，请重试')
						})
					}
				}
			})
		},

		/** 计算课时费 - 与web端handleCalculate方法保持一致 */
		handleCalculate() {
			// 初始化计算表单
			this.calculateForm = {
				calculateMonth: this.queryDate || new Date(),
				calculateType: 'all',
				teacherId: null,
				confirmedOnly: true
			}
			this.calculateDialogVisible = true

			// 直接执行计算，简化小程序操作
			this.confirmCalculate()
		},

		/** 确认计算 - 与web端confirmCalculate方法保持一致 */
		confirmCalculate() {
			if (!this.calculateForm.calculateMonth) {
				toast("请选择计算月份")
				return
			}

			this.calculateLoading = true
			// 处理Date对象或字符串
			let dateStr
			let params // 在此声明 params 变量

			try {
				if (this.calculateForm.calculateMonth instanceof Date) {
					dateStr = this.calculateForm.calculateMonth.toISOString().slice(0, 7)
				} else if (typeof this.calculateForm.calculateMonth === 'string') {
					dateStr = this.calculateForm.calculateMonth
				} else {
					toast('日期格式错误，请重新选择')
					this.calculateLoading = false
					return
				}

				// 验证日期格式
				if (!/^\d{4}-\d{2}$/.test(dateStr)) {
					toast('日期格式错误，请选择正确的年月')
					this.calculateLoading = false
					return
				}

				const [year, month] = dateStr.split('-')
				const yearNum = parseInt(year, 10)
				const monthNum = parseInt(month, 10)

				// 验证解析结果
				if (isNaN(yearNum) || isNaN(monthNum) || yearNum < 2000 || yearNum > 3000 || monthNum < 1 || monthNum > 12) {
					toast('日期解析错误，请选择有效的年月')
					this.calculateLoading = false
					return
				}

				params = {
					statYear: yearNum,
					statMonth: monthNum,
					calculateType: this.calculateForm.calculateType,
					teacherId: this.calculateForm.teacherId,
					confirmedOnly: this.calculateForm.confirmedOnly
				}
			} catch (error) {
				console.error('Date processing error:', error)
				toast('日期处理错误，请重试')
				this.calculateLoading = false
				return
			}

			uni.showLoading({ title: '计算中...' })
			calculateTeacherCourseFee(params).then(response => {
				toast(`成功计算 ${response.data.processedCount} 位教师的课时费`)
				this.calculateDialogVisible = false
				this.calculateLoading = false
				this.getList()
			}).catch(() => {
				this.calculateLoading = false
			}).finally(() => {
				uni.hideLoading()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left, .nav-right {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.nav-right {
	margin-left: auto;
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 查询条件 */
.search-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.search-row {
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.search-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.search-label {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	min-width: 140rpx;
}

.search-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-left: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;

	&:active {
		border-color: #FF9800;
		background: #fff5e6;
	}
}

.search-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

/* 统计概览 */
.stats-overview {
	margin: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.stat-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.stat-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	margin-right: 20rpx;

	&.teacher {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	&.sessions {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	}

	&.fee {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	}

	&.average {
		background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
	}
}

.stat-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.stat-number {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

/* 教师列表 */
.teacher-list {
	margin: 20rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
	margin-top: 20rpx;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 26rpx;
	color: #666666;
	display: block;
	line-height: 1.5;
}

/* 教师卡片 */
.teacher-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	}

	&:last-child {
		margin-bottom: 0;
	}
}

.teacher-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.teacher-info {
	flex: 1;
}

.teacher-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.teacher-code {
	font-size: 24rpx;
	color: #666666;
	display: block;
}

.teacher-summary {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.total-sessions {
	font-size: 28rpx;
	color: #FF9800;
	font-weight: 600;
	margin-bottom: 4rpx;
}

.total-fee {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
}

/* 课程详情 */
.course-details {
	margin-bottom: 24rpx;
}

.course-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 12rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.course-name {
	font-size: 26rpx;
	color: #333333;
	font-weight: 500;
	flex: 1;
}

.course-stats {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.course-sessions {
	font-size: 24rpx;
	color: #666666;
}

.course-price {
	font-size: 24rpx;
	color: #999999;
}

.course-subtotal {
	font-size: 26rpx;
	color: #FF9800;
	font-weight: 600;
}

.no-course-data {
	text-align: center;
	padding: 32rpx;
	color: #999999;
	font-size: 26rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 24rpx;
}

/* 操作按钮 */
.teacher-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	transition: all 0.3s ease;

	text {
		margin-left: 8rpx;
	}

	&:active {
		transform: scale(0.95);
	}
}

.detail-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
}

.recalc-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: #ffffff;
}

/* 选择器弹窗样式 */
.picker-popup {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.picker-cancel, .picker-confirm {
	font-size: 28rpx;
	color: #FF9800;
	font-weight: 500;
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.picker-view {
	height: 400rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
}
</style>
