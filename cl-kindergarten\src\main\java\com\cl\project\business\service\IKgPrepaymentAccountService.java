package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgPrepaymentAccount;
import com.cl.project.business.dto.PrepaymentRechargeDto;
import com.cl.project.business.dto.PrepaymentBalanceDto;
import com.cl.project.business.dto.PrepaymentStatisticsDto;

/**
 * 预交款账户Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface IKgPrepaymentAccountService 
{
    /**
     * 查询预交款账户
     * 
     * @param accountId 预交款账户ID
     * @return 预交款账户
     */
    public KgPrepaymentAccount selectKgPrepaymentAccountById(Long accountId);

    /**
     * 查询预交款账户列表
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 预交款账户集合
     */
    public List<KgPrepaymentAccount> selectKgPrepaymentAccountList(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 新增预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    public int insertKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 修改预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    public int updateKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 批量删除预交款账户
     * 
     * @param accountIds 需要删除的预交款账户ID
     * @return 结果
     */
    public int deleteKgPrepaymentAccountByIds(Long[] accountIds);

    /**
     * 删除预交款账户信息
     * 
     * @param accountId 预交款账户ID
     * @return 结果
     */
    public int deleteKgPrepaymentAccountById(Long accountId);

    // ========== 业务方法 ==========

    /**
     * 查询预交款余额列表
     * 
     * @param queryParams 查询参数
     * @return 预交款余额列表
     */
    public List<PrepaymentBalanceDto> selectBalanceList(PrepaymentBalanceDto queryParams);

    /**
     * 获取预交款余额统计
     * 
     * @return 统计数据
     */
    public PrepaymentStatisticsDto getBalanceStatistics();

    /**
     * 获取学生预交款余额详情
     * 
     * @param studentId 学生ID
     * @return 余额详情
     */
    public PrepaymentBalanceDto getStudentBalanceDetail(Long studentId);

    /**
     * 预交款充值
     * 
     * @param rechargeDto 充值信息
     * @return 结果
     */
    public int rechargeBalance(PrepaymentRechargeDto rechargeDto);

    /**
     * 批量扣费
     * 
     * @param studentIds 学生ID列表
     * @param accountType 账户类型
     * @param amount 扣费金额
     * @param description 描述
     * @return 扣费成功的账户数
     */
    public int batchDeduct(List<Long> studentIds, String accountType, Double amount, String description);

    /**
     * 余额转移
     * 
     * @param fromStudentId 转出学生ID
     * @param toStudentId 转入学生ID
     * @param accountType 账户类型
     * @param amount 转移金额
     * @param description 描述
     * @return 结果
     */
    public int transferBalance(Long fromStudentId, Long toStudentId, String accountType, Double amount, String description);

    /**
     * 冻结/解冻账户
     * 
     * @param accountId 账户ID
     * @param freeze 是否冻结
     * @return 结果
     */
    public int freezeAccount(Long accountId, Boolean freeze);

    /**
     * 发送余额不足提醒
     * 
     * @return 发送成功的提醒数
     */
    public int notifyInsufficientBalance();

    /**
     * 扣除预交款余额
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @param amount 扣费金额
     * @param billId 关联账单ID
     * @param billType 账单类型
     * @param description 描述
     * @return 扣费后的余额
     */
    public Double deductBalance(Long studentId, String accountType, Double amount, Long billId, String billType, String description);

    /**
     * 检查账户余额是否充足
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @param amount 需要扣费的金额
     * @return 是否充足
     */
    public boolean checkBalanceSufficient(Long studentId, String accountType, Double amount);

    /**
     * 获取或创建学生预交款账户
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @return 账户信息
     */
    public KgPrepaymentAccount getOrCreateAccount(Long studentId, String accountType);
}
