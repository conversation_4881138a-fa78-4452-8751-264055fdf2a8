<template>
	<u-popup 
		v-model="visible" 
		mode="center" 
		border-radius="20"
		:mask-close-able="true"
		@close="handleCancel"
	>
		<view class="popup-content">
			<view class="popup-body">
				<view class="function-grid">
					<view class="function-card time-config-card" @click="handleTimeConfig">
						<view class="card-icon">⏰</view>
						<text class="card-title">时间段配置</text>
					</view>

					<view class="function-card fee-config-card" @click="handleFeeConfig">
						<view class="card-icon">💰</view>
						<text class="card-title">入园费用配置</text>
					</view>
				</view>
			</view>
			
			<view class="popup-footer">
				<view class="cancel-btn" @click="handleCancel">
					<text class="cancel-text">取消</text>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'BasicConfigPopup',
	data() {
		return {
			visible: false
		}
	},
	methods: {
		show() {
			this.visible = true;
		},
		hide() {
			this.visible = false;
		},
		handleTimeConfig() {
			this.$emit('select', 'time-config');
			this.hide();
		},
		handleFeeConfig() {
			this.$emit('select', 'fee-config');
			this.hide();
		},
		handleCancel() {
			this.$emit('cancel');
			this.hide();
		}
	}
}
</script>

<style lang="scss" scoped>
.popup-content {
	width: 480rpx;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}

.popup-body {
	padding: 40rpx 30rpx 20rpx;
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 32rpx 16rpx;
	background: #ffffff;
	border-radius: 16rpx;
	border: 2rpx solid #f0f2f7;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	min-height: 120rpx;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}
	
	&:active {
		transform: scale(0.95);
		
		&::before {
			opacity: 1;
		}
	}
}

.time-config-card {
	background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
	border-color: #4CAF50;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.15);
	
	&:active {
		background: linear-gradient(135deg, #c8e6c9 0%, #e8f5e8 100%);
		box-shadow: 0 12rpx 32rpx rgba(76, 175, 80, 0.25);
	}
}

.fee-config-card {
	background: linear-gradient(135deg, #fff3e0 0%, #fffbf0 100%);
	border-color: #FF9800;
	box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.15);

	&:active {
		background: linear-gradient(135deg, #ffe0b2 0%, #fff3e0 100%);
		box-shadow: 0 12rpx 32rpx rgba(255, 152, 0, 0.25);
	}
}

.card-icon {
	font-size: 48rpx;
	margin-bottom: 12rpx;
	display: block;
	filter: brightness(1.1);
}

.card-title {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
	text-align: center;
	line-height: 1.2;
}

.popup-footer {
	padding: 0 30rpx 30rpx;
	text-align: center;
}

.cancel-btn {
	padding: 20rpx 50rpx;
	background: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
	display: inline-block;
	
	&:active {
		background: #e9ecef;
		transform: scale(0.96);
	}
}

.cancel-text {
	font-size: 26rpx;
	color: #6c757d;
	font-weight: 500;
}

/* 动画效果 */
@keyframes slideIn {
	from {
		transform: translateY(20rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

.popup-content {
	animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 卡片悬浮效果 */
.function-card {
	&:active {
		.card-icon {
			transform: scale(1.1);
		}
		
		.card-title {
			color: #333333;
		}
	}
}

.time-config-card:active .card-title {
	color: #388E3C;
}

.fee-config-card:active .card-title {
	color: #F57C00;
}
</style>
