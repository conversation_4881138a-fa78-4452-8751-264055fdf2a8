<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgGiftRuleMapper">
    
    <resultMap type="KgGiftRule" id="KgGiftRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="description"    column="description"    />
        <result property="triggerAmount"    column="trigger_amount"    />
        <result property="triggerAttendanceRate"    column="trigger_attendance_rate"    />
        <result property="triggerCourseType"    column="trigger_course_type"    />
        <result property="giftCourseId"    column="gift_course_id"    />
        <result property="giftCourseName"    column="gift_course_name"    />
        <result property="giftSessions"    column="gift_sessions"    />
        <result property="applicableMonths"    column="applicable_months"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="expiryDate"    column="expiry_date"    />
        <result property="maxTimesPerStudent"    column="max_times_per_student"    />
        <result property="maxTimesPerMonth"    column="max_times_per_month"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgGiftRuleVo">
        select rule_id, rule_name, rule_type, description, trigger_amount, trigger_attendance_rate, trigger_course_type, gift_course_id, gift_course_name, gift_sessions, applicable_months, effective_date, expiry_date, max_times_per_student, max_times_per_month, status, com_id, create_by, create_time, update_by, update_time, remark from kg_gift_rule
    </sql>

    <select id="selectKgGiftRuleList" parameterType="KgGiftRule" resultMap="KgGiftRuleResult">
        <include refid="selectKgGiftRuleVo"/>
        <where>  
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="triggerAmount != null "> and trigger_amount = #{triggerAmount}</if>
            <if test="triggerAttendanceRate != null "> and trigger_attendance_rate = #{triggerAttendanceRate}</if>
            <if test="triggerCourseType != null  and triggerCourseType != ''"> and trigger_course_type = #{triggerCourseType}</if>
            <if test="giftCourseId != null "> and gift_course_id = #{giftCourseId}</if>
            <if test="giftCourseName != null  and giftCourseName != ''"> and gift_course_name like concat('%', #{giftCourseName}, '%')</if>
            <if test="giftSessions != null "> and gift_sessions = #{giftSessions}</if>
            <if test="applicableMonths != null  and applicableMonths != ''"> and applicable_months = #{applicableMonths}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            <if test="maxTimesPerStudent != null "> and max_times_per_student = #{maxTimesPerStudent}</if>
            <if test="maxTimesPerMonth != null "> and max_times_per_month = #{maxTimesPerMonth}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null "> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgGiftRuleByRuleId" parameterType="Long" resultMap="KgGiftRuleResult">
        <include refid="selectKgGiftRuleVo"/>
        where rule_id = #{ruleId}
    </select>
        
    <insert id="insertKgGiftRule" parameterType="KgGiftRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into kg_gift_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="description != null">description,</if>
            <if test="triggerAmount != null">trigger_amount,</if>
            <if test="triggerAttendanceRate != null">trigger_attendance_rate,</if>
            <if test="triggerCourseType != null">trigger_course_type,</if>
            <if test="giftCourseId != null">gift_course_id,</if>
            <if test="giftCourseName != null">gift_course_name,</if>
            <if test="giftSessions != null">gift_sessions,</if>
            <if test="applicableMonths != null">applicable_months,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="maxTimesPerStudent != null">max_times_per_student,</if>
            <if test="maxTimesPerMonth != null">max_times_per_month,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="description != null">#{description},</if>
            <if test="triggerAmount != null">#{triggerAmount},</if>
            <if test="triggerAttendanceRate != null">#{triggerAttendanceRate},</if>
            <if test="triggerCourseType != null">#{triggerCourseType},</if>
            <if test="giftCourseId != null">#{giftCourseId},</if>
            <if test="giftCourseName != null">#{giftCourseName},</if>
            <if test="giftSessions != null">#{giftSessions},</if>
            <if test="applicableMonths != null">#{applicableMonths},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="maxTimesPerStudent != null">#{maxTimesPerStudent},</if>
            <if test="maxTimesPerMonth != null">#{maxTimesPerMonth},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgGiftRule" parameterType="KgGiftRule">
        update kg_gift_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleType != null and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="triggerAmount != null">trigger_amount = #{triggerAmount},</if>
            <if test="triggerAttendanceRate != null">trigger_attendance_rate = #{triggerAttendanceRate},</if>
            <if test="triggerCourseType != null">trigger_course_type = #{triggerCourseType},</if>
            <if test="giftCourseId != null">gift_course_id = #{giftCourseId},</if>
            <if test="giftCourseName != null">gift_course_name = #{giftCourseName},</if>
            <if test="giftSessions != null">gift_sessions = #{giftSessions},</if>
            <if test="applicableMonths != null">applicable_months = #{applicableMonths},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="maxTimesPerStudent != null">max_times_per_student = #{maxTimesPerStudent},</if>
            <if test="maxTimesPerMonth != null">max_times_per_month = #{maxTimesPerMonth},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteKgGiftRuleByRuleId" parameterType="Long">
        delete from kg_gift_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteKgGiftRuleByRuleIds" parameterType="String">
        delete from kg_gift_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <!-- 查询启用的赠送规则列表 -->
    <select id="selectActiveGiftRules" resultMap="KgGiftRuleResult">
        <include refid="selectKgGiftRuleVo"/>
        where status = '1' and (effective_date is null or effective_date &lt;= now()) 
        and (expire_date is null or expire_date &gt;= now())
        order by create_time desc
    </select>

    <!-- 根据规则类型查询启用的规则 -->
    <select id="selectActiveGiftRulesByType" parameterType="String" resultMap="KgGiftRuleResult">
        <include refid="selectKgGiftRuleVo"/>
        where status = '1' and rule_type = #{ruleType} 
        and (effective_date is null or effective_date &lt;= now()) 
        and (expire_date is null or expire_date &gt;= now())
        order by create_time desc
    </select>
    
</mapper>