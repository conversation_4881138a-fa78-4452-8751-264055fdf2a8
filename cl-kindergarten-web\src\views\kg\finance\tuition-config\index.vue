<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" inline>
      <el-form-item label="班级类型">
        <el-select v-model="queryParams.classType" clearable>
          <el-option label="托班" value="托班"/>
          <el-option label="小班" value="小班"/>
          <el-option label="中班" value="中班"/>
          <el-option label="大班" value="大班"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleAdd">新增配置</el-button>
        <el-button type="info" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 配置列表 -->
    <el-table :data="configList" style="width: 100%">
      <el-table-column prop="classType" label="班级类型"/>
      <el-table-column prop="mealFeePerDay" label="每日餐费"/>
      <el-table-column prop="educationFeePerMonth" label="每月保教费"/>
      <el-table-column prop="attendanceThreshold" label="出勤率阈值"/>
      <el-table-column prop="halfEducationFee" label="半额保教费"/>
      <el-table-column prop="effectiveDate" label="生效日期"/>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status==='0'?'success':'info'">
            {{ scope.row.status==='0'?'正常':'停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注"/>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="warning" @click="handleToggleStatus(scope.row)">
            {{ scope.row.status==='0'?'停用':'启用' }}
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="form" label-width="100px">
        <el-form-item label="班级类型">
          <el-select v-model="form.classType" required>
            <el-option label="托班" value="托班"/>
            <el-option label="小班" value="小班"/>
            <el-option label="中班" value="中班"/>
            <el-option label="大班" value="大班"/>
          </el-select>
        </el-form-item>
        <el-form-item label="每日餐费">
          <el-input-number v-model="form.mealFeePerDay" :min="0"/>
        </el-form-item>
        <el-form-item label="每月保教费">
          <el-input-number v-model="form.educationFeePerMonth" :min="0"/>
        </el-form-item>
        <el-form-item label="出勤率阈值">
          <el-input-number v-model="form.attendanceThreshold" :min="0" :max="1" :step="0.01"/>
        </el-form-item>
        <el-form-item label="半额保教费">
          <el-input-number v-model="form.halfEducationFee" :min="0"/>
        </el-form-item>
        <el-form-item label="生效日期">
          <el-date-picker v-model="form.effectiveDate" type="date"     value-format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea"/>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTuitionConfig,
  addTuitionConfig,
  updateTuitionConfig,
  deleteTuitionConfig,
  exportTuitionConfig,
  changeStatus
} from '@/api/kg/finance/tuitionConfig'

export default {
  data() {
    return {
      queryParams: { classType: '' },
      configList: [],
      dialogVisible: false,
      dialogTitle: '新增配置',
      form: {},
      isEdit: false
    }
  },
  methods: {
    getList() {
      listTuitionConfig(this.queryParams).then(res => {
        this.configList = res.rows
      })
    },
    resetQuery() {
      this.queryParams = { classType: '' }
      this.getList()
    },
    handleAdd() {
      this.dialogTitle = '新增配置'
      this.form = { status: '0' }
      this.isEdit = false
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑配置'
      this.form = Object.assign({}, row)
      this.isEdit = true
      this.dialogVisible = true
    },
    handleSubmit() {
      const api = this.isEdit ? updateTuitionConfig : addTuitionConfig
      api(this.form).then(() => {
        this.dialogVisible = false
        this.getList()
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除该配置吗？').then(() => {
        deleteTuitionConfig(row.configId).then(() => this.getList())
      })
    },
    handleToggleStatus(row) {
      changeStatus(row.configId, row.status === '0' ? '1' : '0').then(() => this.getList())
    },
    handleExport() {
      exportTuitionConfig(this.queryParams)
    }
  },
  mounted() {
    this.getList()
  }
}
</script>
