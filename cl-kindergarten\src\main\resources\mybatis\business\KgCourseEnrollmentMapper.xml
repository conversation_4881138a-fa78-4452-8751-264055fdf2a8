<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgCourseEnrollmentMapper">
    
    <resultMap type="KgCourseEnrollment" id="KgCourseEnrollmentResult">
        <result property="enrollmentId"    column="enrollment_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="enrollmentDate"    column="enrollment_date"    />
        <result property="totalSessions"    column="total_sessions"    />
        <result property="usedSessions"    column="used_sessions"    />
        <result property="remainingSessions"    column="remaining_sessions"    />
        <result property="giftSessions"    column="gift_sessions"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="paidAmount"    column="paid_amount"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <!-- 显示字段映射 -->
        <result property="studentName"    column="student_name"    />
        <result property="className"    column="class_name"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseType"    column="course_type"    />
    </resultMap>

    <sql id="selectKgCourseEnrollmentVo">
        select 
            e.enrollment_id, e.student_id, e.course_id, e.class_id, e.enrollment_date, 
            e.total_sessions, e.used_sessions, e.remaining_sessions, e.gift_sessions, 
            e.total_amount, e.paid_amount, e.status, e.com_id, 
            e.create_by, e.create_time, e.update_by, e.update_time, e.remark,
            s.student_name,
            c.class_name,
            co.course_name,
            co.course_type,
            s.student_id as student_id,
            s.student_name as student_name,
            c.class_name as class_name
        from kg_course_enrollment e
        left join kg_student s on e.student_id = s.student_id
        left join kg_class c on e.class_id = c.class_id
        left join kg_course co on e.course_id = co.course_id
    </sql>

    <select id="selectKgCourseEnrollmentList" parameterType="KgCourseEnrollment" resultMap="KgCourseEnrollmentResult">
        <include refid="selectKgCourseEnrollmentVo"/>
        <where>  
            <if test="studentId != null "> and e.student_id = #{studentId}</if>
            <if test="courseId != null "> and e.course_id = #{courseId}</if>
            <if test="classId != null "> and e.class_id = #{classId}</if>
            <if test="enrollmentDate != null "> and e.enrollment_date = #{enrollmentDate}</if>
            <if test="totalSessions != null "> and e.total_sessions = #{totalSessions}</if>
            <if test="usedSessions != null "> and e.used_sessions = #{usedSessions}</if>
            <if test="remainingSessions != null "> and e.remaining_sessions = #{remainingSessions}</if>
            <if test="giftSessions != null "> and e.gift_sessions = #{giftSessions}</if>
            <if test="totalAmount != null "> and e.total_amount = #{totalAmount}</if>
            <if test="paidAmount != null "> and e.paid_amount = #{paidAmount}</if>
            <if test="status != null  and status != ''"> and e.status = #{status}</if>
            <if test="comId != null  and comId != ''"> and e.com_id = #{comId}</if>
            <!-- 支持按学生姓名搜索 -->
            <if test="studentName != null and studentName != ''"> and s.student_name like concat('%', #{studentName}, '%')</if>
        </where>
    </select>
    
    <select id="selectKgCourseEnrollmentById" parameterType="Long" resultMap="KgCourseEnrollmentResult">
        <include refid="selectKgCourseEnrollmentVo"/>
        where e.enrollment_id = #{enrollmentId}
    </select>
        
    <insert id="insertKgCourseEnrollment" parameterType="KgCourseEnrollment" useGeneratedKeys="true" keyProperty="enrollmentId">
        insert into kg_course_enrollment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="enrollmentDate != null">enrollment_date,</if>
            <if test="totalSessions != null">total_sessions,</if>
            <if test="usedSessions != null">used_sessions,</if>
            <if test="remainingSessions != null">remaining_sessions,</if>
            <if test="giftSessions != null">gift_sessions,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="paidAmount != null">paid_amount,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="enrollmentDate != null">#{enrollmentDate},</if>
            <if test="totalSessions != null">#{totalSessions},</if>
            <if test="usedSessions != null">#{usedSessions},</if>
            <if test="remainingSessions != null">#{remainingSessions},</if>
            <if test="giftSessions != null">#{giftSessions},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="paidAmount != null">#{paidAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgCourseEnrollment" parameterType="KgCourseEnrollment">
        update kg_course_enrollment
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="enrollmentDate != null">enrollment_date = #{enrollmentDate},</if>
            <if test="totalSessions != null">total_sessions = #{totalSessions},</if>
            <if test="usedSessions != null">used_sessions = #{usedSessions},</if>
            <if test="remainingSessions != null">remaining_sessions = #{remainingSessions},</if>
            <if test="giftSessions != null">gift_sessions = #{giftSessions},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="paidAmount != null">paid_amount = #{paidAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where enrollment_id = #{enrollmentId}
    </update>

    <delete id="deleteKgCourseEnrollmentById" parameterType="Long">
        delete from kg_course_enrollment where enrollment_id = #{enrollmentId}
    </delete>

    <delete id="deleteKgCourseEnrollmentByIds" parameterType="String">
        delete from kg_course_enrollment where enrollment_id in 
        <foreach item="enrollmentId" collection="array" open="(" separator="," close=")">
            #{enrollmentId}
        </foreach>
    </delete>
    
</mapper>