import request from '@/utils/request'

// 查询赠送规则列表
export function listGiftRule(query) {
  return request({
    url: '/business/gift/rule/list',
    method: 'get',
    params: query
  })
}

// 查询赠送规则详细
export function getGiftRule(ruleId) {
  return request({
    url: '/business/gift/rule/' + ruleId,
    method: 'get'
  })
}

// 新增赠送规则
export function addGiftRule(data) {
  return request({
    url: '/business/gift/rule',
    method: 'post',
    data: data
  })
}

// 修改赠送规则
export function updateGiftRule(data) {
  return request({
    url: '/business/gift/rule',
    method: 'put',
    data: data
  })
}

// 删除赠送规则
export function delGiftRule(ruleId) {
  return request({
    url: '/business/gift/rule/' + ruleId,
    method: 'delete'
  })
}

// 导出赠送规则
export function exportGiftRule(query) {
  return request({
    url: '/business/gift/rule/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 查询启用的赠送规则
export function listActiveGiftRule() {
  return request({
    url: '/business/gift/rule/active',
    method: 'get'
  })
}

// 修改赠送规则状态
export function changeGiftRuleStatus(ruleId, status) {
  return request({
    url: '/business/gift/rule/changeStatus',
    method: 'put',
    data: {
      ruleId: ruleId,
      status: status
    }
  })
}
