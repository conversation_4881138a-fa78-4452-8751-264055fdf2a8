<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="queryParams.ruleName"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <el-select v-model="queryParams.ruleType" placeholder="请选择规则类型" clearable>
          <el-option label="金额型" value="amount_based" />
          <el-option label="出勤型" value="attendance_based" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增规则</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="giftRuleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="规则名称" prop="ruleName" width="150" />
      <el-table-column label="规则类型" prop="ruleType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.ruleType === 'amount_based'" type="success">金额型</el-tag>
          <el-tag v-else-if="scope.row.ruleType === 'attendance_based'" type="warning">出勤型</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="触发条件" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.ruleType === 'amount_based' && scope.row.triggerAmount">
            消费满 <span class="price-text">¥{{ scope.row.triggerAmount }}</span>
          </div>
          <div v-if="scope.row.triggerAttendanceRate">
            出勤率≥{{ scope.row.triggerAttendanceRate }}%
          </div>
          <div v-if="scope.row.triggerCourseType && scope.row.triggerCourseType !== 'all'" class="text-muted">
            {{ getCourseTypeText(scope.row.triggerCourseType) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="赠送内容" width="150">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.giftCourseName }}</el-tag>
          <br/>
          <span class="text-muted">{{ scope.row.giftSessions }}节课时</span>
        </template>
      </el-table-column>
      <el-table-column label="适用月份" prop="applicableMonths" width="120">
        <template slot-scope="scope">
          <span v-if="!scope.row.applicableMonths || scope.row.applicableMonths === 'all'">全年</span>
          <span v-else>{{ scope.row.applicableMonths }}月</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleGiftRecord(scope.row)"
          >赠送记录</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改赠送规则配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则类型" prop="ruleType">
              <el-radio-group v-model="form.ruleType">
                <el-radio label="amount_based">金额型</el-radio>
                <el-radio label="attendance_based">出勤型</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 触发条件 -->
        <el-divider content-position="left">触发条件</el-divider>
        <el-row>
          <el-col :span="12" v-if="form.ruleType === 'amount_based'">
            <el-form-item label="触发金额" prop="triggerAmount">
              <el-input-number v-model="form.triggerAmount" :min="0" :precision="2" style="width: 100%" placeholder="消费满此金额触发" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.ruleType === 'attendance_based'">
            <el-form-item label="触发出勤率" prop="triggerAttendanceRate">
              <el-input-number v-model="form.triggerAttendanceRate" :min="0" :max="100" :precision="2" style="width: 100%" placeholder="触发出勤率阈值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触发课程类型">
              <el-select v-model="form.triggerCourseType" placeholder="请选择课程类型">
                <el-option label="所有课程" value="all" />
                <el-option label="托管课程" value="1" />
                <el-option label="兴趣班" value="2" />
                <el-option label="特色课程" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 赠送内容 -->
        <el-divider content-position="left">赠送内容</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="赠送课程" prop="giftCourseId">
              <el-select v-model="form.giftCourseId" placeholder="请选择要赠送的课程" @change="handleCourseChange">
                <el-option 
                  v-for="course in courseOptions" 
                  :key="course.courseId" 
                  :label="course.courseName" 
                  :value="course.courseId" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送课时数" prop="giftSessions">
              <el-input-number v-model="form.giftSessions" :min="1" :max="50" style="width: 100%" placeholder="赠送课时数" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 限制条件 -->
        <el-divider content-position="left">限制条件</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="每人最大次数">
              <el-input-number v-model="form.maxTimesPerStudent" :min="1" style="width: 100%" placeholder="每个学生最多享受次数" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每月最大次数">
              <el-input-number v-model="form.maxTimesPerMonth" :min="1" style="width: 100%" placeholder="每月最多触发次数" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="适用月份">
              <el-checkbox-group v-model="form.applyMonths">
                <el-checkbox v-for="month in monthOptions" :key="month.value" :label="month.value">
                  {{ month.label }}
                </el-checkbox>
              </el-checkbox-group>
              <div class="form-tip">不选择表示全年适用</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGiftRule, getGiftRule, delGiftRule, addGiftRule, updateGiftRule, changeGiftRuleStatus } from "@/api/kg/gift/rule";
import { listAllCourse } from "@/api/kg/course/manage";

export default {
  name: "GiftRule",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 赠送规则配置表格数据
      giftRuleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        ruleType: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" }
        ],
        ruleType: [
          { required: true, message: "规则类型不能为空", trigger: "change" }
        ],
        giftCourseId: [
          { required: true, message: "赠送课程不能为空", trigger: "change" }
        ],
        giftSessions: [
          { required: true, message: "赠送课时数不能为空", trigger: "blur" }
        ]
      },
      // 课程选项
      courseOptions: [],
      // 月份选项
      monthOptions: [
        { value: "1", label: "1月" },
        { value: "2", label: "2月" },
        { value: "3", label: "3月" },
        { value: "4", label: "4月" },
        { value: "5", label: "5月" },
        { value: "6", label: "6月" },
        { value: "7", label: "7月" },
        { value: "8", label: "8月" },
        { value: "9", label: "9月" },
        { value: "10", label: "10月" },
        { value: "11", label: "11月" },
        { value: "12", label: "12月" }
      ]
    };
  },
  created() {
    this.getList();
    this.getCourseList();
  },
  methods: {
    /** 查询赠送规则配置列表 */
    getList() {
      this.loading = true;
      listGiftRule(this.queryParams).then(response => {
        this.giftRuleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询课程列表 */
    getCourseList() {
      listAllCourse().then(response => {
        this.courseOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ruleId: null,
        ruleName: null,
        ruleType: "amount_based",
        status: "1",
        triggerAmount: null,
        triggerAttendanceRate: null,
        triggerCourseType: "all",
        giftCourseId: null,
        giftCourseName: null,
        giftSessions: null,
        applicableMonths: null,
        applyMonths: [],
        effectiveDate: null,
        expiryDate: null,
        maxTimesPerStudent: 1,
        maxTimesPerMonth: 1,
        comId: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ruleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加赠送规则配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const ruleId = row.ruleId || this.ids
      getGiftRule(ruleId).then(response => {
        this.form = response.data;
        // 处理适用月份
        if (this.form.applicableMonths && this.form.applicableMonths !== 'all') {
          this.form.applyMonths = this.form.applicableMonths.split(',');
        }
        this.open = true;
        this.title = "修改赠送规则配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理适用月份
          if (this.form.applyMonths && this.form.applyMonths.length > 0) {
            this.form.applicableMonths = this.form.applyMonths.join(',');
          } else {
            this.form.applicableMonths = 'all';
          }
          
          if (this.form.ruleId != null) {
            updateGiftRule(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGiftRule(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ruleIds = row.ruleId || this.ids;
      this.$confirm('是否确认删除赠送规则配置编号为"' + ruleIds + '"的数据项？', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delGiftRule(ruleIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/gift/rule/export', {
        ...this.queryParams
      }, `gift_rule_${new Date().getTime()}.xlsx`)
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "启用" : "禁用";
      this.$confirm('确认要"' + text + '""' + row.ruleName + '"规则吗？', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return changeGiftRuleStatus(row);
        }).then(() => {
          this.msgSuccess(text + "成功");
        }).catch(function() {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    /** 课程选择事件 */
    handleCourseChange(courseId) {
      const course = this.courseOptions.find(item => item.courseId === courseId);
      if (course) {
        this.form.giftCourseName = course.courseName;
      }
    },
    /** 查看赠送记录 */
    handleGiftRecord(row) {
      this.$router.push({
        path: '/kg/gift/record',
        query: { ruleId: row.ruleId, ruleName: row.ruleName }
      });
    },
    /** 获取课程类型文本 */
    getCourseTypeText(courseType) {
      const typeMap = {
        'all': '所有课程',
        '1': '托管课程',
        '2': '兴趣班',
        '3': '特色课程'
      };
      return typeMap[courseType] || courseType;
    }
  }
};
</script>

<style scoped>
.price-text {
  color: #E6A23C;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}
</style>
