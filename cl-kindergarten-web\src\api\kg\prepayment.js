import request from '@/utils/request'

// 查询预交款余额列表
export function getBalanceList(query) {
  return request({
    url: '/business/prepayment/balance/list',
    method: 'get',
    params: query
  })
}

// 获取预交款余额统计
export function getBalanceStatistics() {
  return request({
    url: '/business/prepayment/stats/balance',
    method: 'get'
  })
}

// 查询学生预交款余额详情
export function getStudentBalance(studentId) {
  return request({
    url: '/business/prepayment/student/' + studentId + '/balance',
    method: 'get'
  })
}

// 预交款充值
export function rechargeBalance(data) {
  return request({
    url: '/business/prepayment/recharge',
    method: 'post',
    data: data
  })
}

// 批量扣费
export function batchDeductBalance(data) {
  return request({
    url: '/business/prepayment/deduct/batch',
    method: 'post',
    data: data
  })
}

// 余额转移
export function transferBalance(data) {
  return request({
    url: '/business/prepayment/transfer',
    method: 'post',
    data: data
  })
}

// 冻结/解冻账户
export function freezeAccount(accountId, freeze) {
  return request({
    url: '/business/prepayment/account/' + accountId + '/freeze',
    method: 'put',
    data: { freeze: freeze }
  })
}

// 查询预交款流水列表
export function getTransactionList(query) {
  return request({
    url: '/business/prepayment/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询预交款流水详情
export function getTransaction(transactionId) {
  return request({
    url: '/business/prepayment/transaction/' + transactionId,
    method: 'get'
  })
}

// 查询预交款流水统计
export function getTransactionStatistics() {
  return request({
    url: '/business/prepayment/transaction/statistics',
    method: 'get'
  })
}

// 批量发送余额不足提醒
export function batchNotifyRecharge() {
  return request({
    url: '/business/prepayment/notify/insufficient',
    method: 'post'
  })
}

// 导出预交款余额
export function exportBalance(query) {
  return request({
    url: '/business/prepayment/balance/export',
    method: 'get',
    params: query
  })
}

// 导出预交款流水
export function exportTransaction(query) {
  return request({
    url: '/business/prepayment/transaction/export',
    method: 'get',
    params: query
  })
}

// 查询预交款账户列表
export function getAccountList(query) {
  return request({
    url: '/business/prepayment/account/list',
    method: 'get',
    params: query
  })
}

// 新增预交款账户
export function addAccount(data) {
  return request({
    url: '/business/prepayment/account',
    method: 'post',
    data: data
  })
}

// 修改预交款账户
export function updateAccount(data) {
  return request({
    url: '/business/prepayment/account',
    method: 'put',
    data: data
  })
}

// 删除预交款账户
export function delAccount(accountIds) {
  return request({
    url: '/business/prepayment/account/' + accountIds,
    method: 'delete'
  })
}

// 查询预交款账户详情
export function getAccount(accountId) {
  return request({
    url: '/business/prepayment/account/' + accountId,
    method: 'get'
  })
}
