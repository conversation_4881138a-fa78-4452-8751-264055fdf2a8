package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgTuitionConfig;

/**
 * 园费配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgTuitionConfigService 
{
    /**
     * 查询园费配置
     * 
     * @param configId 园费配置ID
     * @return 园费配置
     */
    public KgTuitionConfig selectKgTuitionConfigById(Long configId);

    /**
     * 查询园费配置列表
     * 
     * @param kgTuitionConfig 园费配置
     * @return 园费配置集合
     */
    public List<KgTuitionConfig> selectKgTuitionConfigList(KgTuitionConfig kgTuitionConfig);

    /**
     * 新增园费配置
     * 
     * @param kgTuitionConfig 园费配置
     * @return 结果
     */
    public int insertKgTuitionConfig(KgTuitionConfig kgTuitionConfig);

    /**
     * 修改园费配置
     * 
     * @param kgTuitionConfig 园费配置
     * @return 结果
     */
    public int updateKgTuitionConfig(KgTuitionConfig kgTuitionConfig);

    /**
     * 批量删除园费配置
     * 
     * @param configIds 需要删除的园费配置ID
     * @return 结果
     */
    public int deleteKgTuitionConfigByIds(Long[] configIds);

    /**
     * 删除园费配置信息
     * 
     * @param configId 园费配置ID
     * @return 结果
     */
    public int deleteKgTuitionConfigById(Long configId);
    /**
     * 启用/停用园费配置
     * @param configId 配置ID
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    int changeStatus(Long configId, String status);
}
