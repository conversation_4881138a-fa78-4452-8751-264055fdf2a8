package com.cl.project.business.controller;

import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgClassService;

import com.cl.project.business.domain.dto.DingtalkUserCreateResult;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgStudentService;
import com.cl.project.business.service.IDingtalkApiService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.cl.common.utils.StringUtils;

/**
 * 幼儿信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/student")
public class KgStudentController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(KgStudentController.class);
    
    @Autowired
    private IKgStudentService kgStudentService;
    
    @Autowired
    private IDingtalkApiService dingtalkApiService;
    
    @Autowired
    private IKgClassService kgClassService;

    /**
     * 查询幼儿信息列表
     */
    @SaCheckPermission("kg:student:info:list")
    @GetMapping("/list")
    public TableDataInfo list(KgStudent kgStudent)
    {
        startPage();
        List<KgStudent> list = kgStudentService.selectKgStudentList(kgStudent);
        return getDataTable(list);
    }

    /**
     * 导出幼儿信息列表
     */
    @SaCheckPermission("kg:student:info:list")
    @Log(title = "幼儿信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgStudent kgStudent)
    {
        List<KgStudent> list = kgStudentService.selectKgStudentList(kgStudent);
        ExcelUtil<KgStudent> util = new ExcelUtil<KgStudent>(KgStudent.class);
        return util.exportExcel(list, "student");
    }

    /**
     * 获取幼儿信息详细信息
     */
    @SaCheckPermission("kg:student:info:list")
    @GetMapping(value = "/{studentId}")
    public AjaxResult getInfo(@PathVariable("studentId") Long studentId)
    {
        return AjaxResult.success(kgStudentService.selectKgStudentById(studentId));
    }

    /**
     * 获取所有学生列表
     * 用于考勤等功能的学生选择
     */
    @GetMapping("/allList")
    public AjaxResult getAllStudents()
    {
        KgStudent queryParam = new KgStudent();
        List<KgStudent> list = kgStudentService.selectKgStudentList(queryParam);
        return AjaxResult.success(list);
    }

    /**
     * 新增幼儿信息
     */
    @SaCheckPermission("kg:student:info:add")
    @Log(title = "幼儿信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgStudent kgStudent)
    {
        // 先新增本地学生信息
        int result = kgStudentService.insertKgStudent(kgStudent);
        
        // 如果新增成功且有必要信息，则同步创建钉钉用户
        if (result > 0 && StringUtils.isNotEmpty(kgStudent.getStudentName())) {
            try {
                // 生成钉钉用户ID（使用学生 ID）
                String dingtalkUserId = "student_" + kgStudent.getStudentId();
                
                // 根据classId查班级表，获取dingtalkDeptId
                Long deptId = 1L; // 默认根部门
                if (kgStudent.getClassId() != null) {
                    KgClass kgClass = kgClassService.selectKgClassById(kgStudent.getClassId());
                    if (kgClass != null && kgClass.getDingtalkDeptId() != null) {
                        deptId = kgClass.getDingtalkDeptId();
                    }
                }
                DingtalkUserCreateResult createResult = dingtalkApiService.createUser(
                    dingtalkUserId,
                    kgStudent.getStudentName(),
                    kgStudent.getParentPhone(),
                    null, // 学生一般没有邮箱
                    "学生", // 默认职位
                    deptId
                );
                
                boolean needSaveDingtalkUserId = false;
                if (createResult != null && createResult.isSuccess()) {
                    needSaveDingtalkUserId = true;
                    logger.info("同步创建钉钉用户成功: studentId={}, dingtalkUserId={}", 
                               kgStudent.getStudentId(), dingtalkUserId);
                } else if (createResult != null && createResult.getErrCode() != null && createResult.getErrCode() == 40103) {
                    // 钉钉返回“已发出邀请”，也保存ID
                    needSaveDingtalkUserId = true;
                    logger.warn("钉钉用户已发出邀请: studentId={}, studentName={}, dingtalkUserId={}, errMsg={}",
                               kgStudent.getStudentId(), kgStudent.getStudentName(), dingtalkUserId, createResult.getErrMsg());
                } else {
                    logger.warn("同步创建钉钉用户失败: studentId={}, studentName={}, errMsg={}, errCode={}", 
                               kgStudent.getStudentId(), kgStudent.getStudentName(),
                               createResult != null ? createResult.getErrMsg() : "未知错误",
                               createResult != null ? createResult.getErrCode() : null);
                }
                if (needSaveDingtalkUserId) {
                    kgStudent.setDingtalkUserId(dingtalkUserId);
                    kgStudentService.updateKgStudent(kgStudent);
                }
            } catch (Exception e) {
                logger.error("同步创建钉钉用户异常: studentId={}, studentName={}", 
                            kgStudent.getStudentId(), kgStudent.getStudentName(), e);
            }
        }
        
        return toAjax(result);
    }

    /**
     * 修改幼儿信息
     */
    @SaCheckPermission("kg:student:info:edit")
    @Log(title = "幼儿信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgStudent kgStudent)
    {
        // 先更新本地学生信息
        int result = kgStudentService.updateKgStudent(kgStudent);
        
        // 如果更新成功且有钉钉用户ID，则同步更新钉钉用户信息
        if (result > 0 && StringUtils.isNotEmpty(kgStudent.getDingtalkUserId())) {
            try {
                boolean updateSuccess = dingtalkApiService.updateUser(
                    kgStudent.getDingtalkUserId(),
                    kgStudent.getStudentName(),
                    kgStudent.getPhone(),
                    null, // 学生一般没有邮箱
                    null  // 学生没有职位
                );
                
                if (updateSuccess) {
                    logger.info("同步更新钉钉用户成功: studentId={}, dingtalkUserId={}", 
                               kgStudent.getStudentId(), kgStudent.getDingtalkUserId());
                } else {
                    logger.warn("同步更新钉钉用户失败: studentId={}, dingtalkUserId={}", 
                               kgStudent.getStudentId(), kgStudent.getDingtalkUserId());
                }
            } catch (Exception e) {
                logger.error("同步更新钉钉用户异常: studentId={}, dingtalkUserId={}", 
                            kgStudent.getStudentId(), kgStudent.getDingtalkUserId(), e);
            }
        }
        
        return toAjax(result);
    }

    /**
     * 删除幼儿信息
     */
    @SaCheckPermission("kg:student:info:remove")
    @Log(title = "幼儿信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{studentIds}")
    public AjaxResult remove(@PathVariable Long[] studentIds)
    {
        try 
        {
            // 先获取要删除的学生信息，以便删除钉钉用户
            for (Long studentId : studentIds) 
            {
                KgStudent student = kgStudentService.selectKgStudentById(studentId);
                if (student != null && StringUtils.isNotEmpty(student.getDingtalkUserId())) 
                {
                    try 
                    {
                        boolean success = dingtalkApiService.deleteUser(student.getDingtalkUserId());
                        if (success) 
                        {
                            logger.info("删除钉钉用户成功: {}, dingtalkUserId: {}", student.getStudentName(), student.getDingtalkUserId());
                        } 
                        else 
                        {
                            logger.warn("删除钉钉用户失败，但会继续删除本地记录: {}", student.getStudentName());
                        }
                    } 
                    catch (Exception e) 
                    {
                        logger.error("删除钉钉用户异常，但会继续删除本地记录: {}", student.getStudentName(), e);
                    }
                }
            }
            
            // 删除本地学生记录
            int result = kgStudentService.deleteKgStudentByIds(studentIds);
            if (result > 0) 
            {
                return AjaxResult.success("删除成功");
            } 
            else 
            {
                return AjaxResult.error("删除失败");
            }
        } 
        catch (Exception e) 
        {
            logger.error("删除学生异常", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

}
