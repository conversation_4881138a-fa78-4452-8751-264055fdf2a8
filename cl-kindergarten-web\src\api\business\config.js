import request from '@/utils/request'

// 查询时间段配置列表
export function listConfig(query) {
  return request({
    url: '/business/time-config/list',
    method: 'get',
    params: query
  })
}

// 查询时间段配置详细
export function getConfig(configId) {
  return request({
    url: '/business/time-config/' + configId,
    method: 'get'
  })
}

// 新增时间段配置
export function addConfig(data) {
  return request({
    url: '/business/time-config',
    method: 'post',
    data: data
  })
}

// 修改时间段配置
export function updateConfig(data) {
  return request({
    url: '/business/time-config',
    method: 'put',
    data: data
  })
}

// 删除时间段配置
export function delConfig(configId) {
  return request({
    url: '/business/time-config/' + configId,
    method: 'delete'
  })
}

// 导出时间段配置
export function exportConfig(query) {
  return request({
    url: '/business/time-config/export',
    method: 'get',
    params: query
  })
}