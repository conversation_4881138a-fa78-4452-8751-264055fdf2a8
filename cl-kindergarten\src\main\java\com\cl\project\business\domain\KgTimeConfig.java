package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 时间段配置对象 kg_time_config
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTimeConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 配置名称 */
    @Excel(name = "配置名称")
    private String configName;

    /** 时间类型（tuition园费、course托管费） */
    @Excel(name = "时间类型", readConverterExp = "t=uition园费、course托管费")
    private String timeType;

    /** 开始时间（时分秒） */
    @Excel(name = "开始时间", width = 20)
    private String startTime;

    /** 结束时间（时分秒） */
    @Excel(name = "结束时间", width = 20)
    private String endTime;

    /** 是否启用（0否 1是） */
    @Excel(name = "是否启用", readConverterExp = "0=否,1=是")
    private Long isActive;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setConfigId(Long configId)
    {
        this.configId = configId;
    }
    public Long getConfigId()
    {
        return configId;
    }
    public void setStartTime(String startTime)
    {
        this.startTime = startTime;
    }

    public String getStartTime()
    {
        return startTime;
    }

    public void setEndTime(String endTime)
    {
        this.endTime = endTime;
    }

    public String getEndTime()
    {
        return endTime;
    }
    public void setConfigName(String configName) 
    {
        this.configName = configName;
    }

    public String getConfigName() 
    {
        return configName;
    }
    public void setTimeType(String timeType) 
    {
        this.timeType = timeType;
    }

    public String getTimeType() 
    {
        return timeType;
    }

    public void setIsActive(Long isActive) 
    {
        this.isActive = isActive;
    }

    public Long getIsActive() 
    {
        return isActive;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configName", getConfigName())
            .append("timeType", getTimeType())
            .append("isActive", getIsActive())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
