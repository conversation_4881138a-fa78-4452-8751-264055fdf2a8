package com.cl.project.business.mapper;

import java.util.Date;
import java.util.List;
import com.cl.project.business.domain.KgDingtalkAttendance;

/**
 * 钉钉打卡记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgDingtalkAttendanceMapper 
{
    /**
     * 查询钉钉打卡记录
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 钉钉打卡记录
     */
    public KgDingtalkAttendance selectKgDingtalkAttendanceById(Long recordId);

    /**
     * 查询钉钉打卡记录列表
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 钉钉打卡记录集合
     */
    public List<KgDingtalkAttendance> selectKgDingtalkAttendanceList(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 新增钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    public int insertKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 修改钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    public int updateKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 删除钉钉打卡记录
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 结果
     */
    public int deleteKgDingtalkAttendanceById(Long recordId);

    /**
     * 批量删除钉钉打卡记录
     * 
     * @param recordIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgDingtalkAttendanceByIds(Long[] recordIds);
    /**
     * 查询指定员工某天的所有钉钉打卡记录
     * @param employeeId 教师ID
     * @param dateFrom 开始日期（含）
     * @param dateTo 结束日期（含）
     * @return 钉钉打卡记录列表
     */
    List<KgDingtalkAttendance> selectByEmployeeIdAndDate(Long employeeId, java.util.Date dateFrom, java.util.Date dateTo);

    List<KgDingtalkAttendance> selectByStudentIdAndDate(Long studentId, Date dateFrom, Date dateTo);
}
