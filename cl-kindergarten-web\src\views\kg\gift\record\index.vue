<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="赠送课程" prop="giftCourseName">
        <el-input
          v-model="queryParams.giftCourseName"
          placeholder="请输入赠送课程名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="赠送月份" prop="giftMonth">
        <el-input
          v-model="queryParams.giftMonth"
          placeholder="请输入赠送月份，如2025-01"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="有效" value="active" />
          <el-option label="已用完" value="used_up" />
          <el-option label="已过期" value="expired" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:gift:record:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:gift:record:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:gift:record:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:gift:record:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="giftRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录编号" align="center" prop="recordId" width="80" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="100" />
      <el-table-column label="赠送课程" align="center" prop="giftCourseName" width="120" />
      <el-table-column label="赠送课时" align="center" prop="giftSessions" width="80">
        <template slot-scope="scope">
          <el-tag type="info">{{ scope.row.giftSessions }}节</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="已用课时" align="center" prop="usedSessions" width="80">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.usedSessions || 0 }}节</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="剩余课时" align="center" prop="remainingSessions" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.remainingSessions > 0 ? 'success' : 'danger'">
            {{ scope.row.remainingSessions || 0 }}节
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="触发金额" align="center" prop="triggerAmount" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.triggerAmount">¥{{ scope.row.triggerAmount }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="赠送月份" align="center" prop="giftMonth" width="100" />
      <el-table-column label="过期日期" align="center" prop="expireDate" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.expireDate">{{ scope.row.expireDate }}</span>
          <span v-else class="text-muted">永不过期</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'active'" type="success">有效</el-tag>
          <el-tag v-else-if="scope.row.status === 'used_up'" type="warning">已用完</el-tag>
          <el-tag v-else-if="scope.row.status === 'expired'" type="danger">已过期</el-tag>
          <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:gift:record:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:gift:record:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改赠送记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="学生姓名" prop="studentName">
              <el-input v-model="form.studentName" placeholder="请输入学生姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送课程" prop="giftCourseName">
              <el-input v-model="form.giftCourseName" placeholder="请输入赠送课程名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="赠送课时" prop="giftSessions">
              <el-input-number v-model="form.giftSessions" :min="1" :max="999" placeholder="请输入赠送课时" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触发金额" prop="triggerAmount">
              <el-input-number v-model="form.triggerAmount" :precision="2" :min="0" placeholder="请输入触发金额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="赠送月份" prop="giftMonth">
              <el-input v-model="form.giftMonth" placeholder="如：2025-01" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期日期" prop="expireDate">
              <el-date-picker
                v-model="form.expireDate"
                type="date"
                placeholder="选择过期日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="active">有效</el-radio>
                <el-radio label="used_up">已用完</el-radio>
                <el-radio label="expired">已过期</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGiftRecord, getGiftRecord, delGiftRecord, addGiftRecord, updateGiftRecord, exportGiftRecord } from "@/api/kg/gift/record";

export default {
  name: "GiftRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 赠送记录表格数据
      giftRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        giftCourseName: null,
        giftMonth: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        studentName: [
          { required: true, message: "学生姓名不能为空", trigger: "blur" }
        ],
        giftCourseName: [
          { required: true, message: "赠送课程名称不能为空", trigger: "blur" }
        ],
        giftSessions: [
          { required: true, message: "赠送课时不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询赠送记录列表 */
    getList() {
      this.loading = true;
      listGiftRecord(this.queryParams).then(response => {
        this.giftRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        recordId: null,
        ruleId: null,
        studentId: null,
        studentName: null,
        giftCourseId: null,
        giftCourseName: null,
        giftSessions: null,
        triggerAmount: null,
        triggerBillId: null,
        giftMonth: null,
        usedSessions: 0,
        remainingSessions: null,
        expireDate: null,
        status: "active",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加赠送记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const recordId = row.recordId || this.ids
      getGiftRecord(recordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改赠送记录";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$alert(`
        <div style="text-align: left;">
          <p><strong>记录编号：</strong>${row.recordId}</p>
          <p><strong>学生姓名：</strong>${row.studentName}</p>
          <p><strong>赠送课程：</strong>${row.giftCourseName}</p>
          <p><strong>赠送课时：</strong>${row.giftSessions}节</p>
          <p><strong>已用课时：</strong>${row.usedSessions || 0}节</p>
          <p><strong>剩余课时：</strong>${row.remainingSessions || 0}节</p>
          <p><strong>触发金额：</strong>${row.triggerAmount ? '¥' + row.triggerAmount : '-'}</p>
          <p><strong>赠送月份：</strong>${row.giftMonth}</p>
          <p><strong>过期日期：</strong>${row.expireDate || '永不过期'}</p>
          <p><strong>状态：</strong>${this.getStatusText(row.status)}</p>
          <p><strong>创建时间：</strong>${this.parseTime(row.createTime)}</p>
          <p><strong>备注：</strong>${row.remark || '-'}</p>
        </div>
      `, '赠送记录详情', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭'
      });
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '有效',
        'used_up': '已用完', 
        'expired': '已过期'
      };
      return statusMap[status] || status;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 计算剩余课时
          this.form.remainingSessions = this.form.giftSessions - (this.form.usedSessions || 0);
          
          if (this.form.recordId != null) {
            updateGiftRecord(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGiftRecord(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$confirm('是否确认删除赠送记录编号为"' + recordIds + '"的数据项？', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delGiftRecord(recordIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有赠送记录数据项？', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportGiftRecord(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    }
  }
};
</script>
