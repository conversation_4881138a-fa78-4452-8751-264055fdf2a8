package com.cl.project.business.controller;

import java.util.List;
import java.util.Date;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cl.project.business.domain.dto.BatchConfirmAttendanceDto;
import com.cl.project.business.domain.dto.StudentCheckinAndConfirmDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.domain.dto.StudentAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchStudentCheckinDto;
import com.cl.project.business.service.IKgStudentAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 学生考勤记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/student-attendance")
public class KgStudentAttendanceController extends BaseController
{
    @Autowired
    private IKgStudentAttendanceService kgStudentAttendanceService;

    /**
     * 查询学生考勤记录列表
     */
    @SaCheckPermission("kg:attendance:student:list")
    @GetMapping("/list")
    public TableDataInfo list(KgStudentAttendance kgStudentAttendance)
    {
        startPage();
        List<KgStudentAttendance> list = kgStudentAttendanceService.selectKgStudentAttendanceList(kgStudentAttendance);
        return getDataTable(list);
    }

    /**
     * 查询学生考勤概览列表
     * 展示所有学生及其当日考勤状态
     */
    @SaCheckPermission("kg:attendance:student:list")
    @GetMapping("/overview")
    public TableDataInfo overview(
            @RequestParam(value = "attendanceDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") Date attendanceDate,
            @RequestParam(value = "studentName", required = false) String studentName,
            @RequestParam(value = "classId", required = false) Long classId,
            @RequestParam(value = "attendanceStatus", required = false) String attendanceStatus,
            @RequestParam(value = "dataSource", required = false) String dataSource)
    {
        startPage();
        List<StudentAttendanceOverviewDto> list = kgStudentAttendanceService.selectStudentAttendanceOverview(attendanceDate, studentName, classId, attendanceStatus, dataSource);
        return getDataTable(list);
    }

    /**
     * 导出学生考勤记录列表
     */
    @SaCheckPermission("kg:attendance:student:query")
    @Log(title = "学生考勤记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgStudentAttendance kgStudentAttendance)
    {
        List<KgStudentAttendance> list = kgStudentAttendanceService.selectKgStudentAttendanceList(kgStudentAttendance);
        ExcelUtil<KgStudentAttendance> util = new ExcelUtil<KgStudentAttendance>(KgStudentAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取学生考勤记录详细信息
     */
    @SaCheckPermission("kg:attendance:student:query")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(kgStudentAttendanceService.selectKgStudentAttendanceById(attendanceId));
    }

    /**
     * 新增学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.insertKgStudentAttendance(kgStudentAttendance));
    }

    /**
     * 修改学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "学生考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.updateKgStudentAttendance(kgStudentAttendance));
    }

    /**
     * 删除学生考勤记录
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(kgStudentAttendanceService.deleteKgStudentAttendanceByIds(attendanceIds));
    }

    /**
     * 学生签到
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生签到", businessType = BusinessType.INSERT)
    @PostMapping("/checkin")
    public AjaxResult checkin(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.studentCheckin(kgStudentAttendance));
    }

    /**
     * 批量学生签到
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "批量学生签到", businessType = BusinessType.INSERT)
    @PostMapping("/batchCheckin")
    public AjaxResult batchCheckin(@RequestBody BatchStudentCheckinDto batchDto)
    {
        int successCount = kgStudentAttendanceService.batchStudentCheckin(batchDto);
        return AjaxResult.success("批量签到成功，共处理 " + successCount + " 条记录", successCount);
    }

    /**
     * 单个确认学生考勤
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "确认学生考勤", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{attendanceId}")
    public AjaxResult confirm(@PathVariable Long attendanceId)
    {
        return toAjax(kgStudentAttendanceService.confirmStudentAttendance(attendanceId));
    }

    /**
     * 批量确认学生考勤（自动获取当前用户ID）
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "批量确认学生考勤", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public AjaxResult batchConfirm(@RequestBody BatchConfirmAttendanceDto dto) {
        dto.setConfirmedBy(cn.dev33.satoken.stp.StpUtil.getLoginIdAsLong());
        return toAjax(kgStudentAttendanceService.batchConfirmAttendance(dto));
    }

    /**
     * 批量确认学生考勤（兼容旧接口）
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "批量确认学生考勤", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirmBatch(@RequestBody BatchConfirmAttendanceDto dto) {
        dto.setConfirmedBy(cn.dev33.satoken.stp.StpUtil.getLoginIdAsLong());
        return toAjax(kgStudentAttendanceService.batchConfirmAttendance(dto));
    }

    /**
     * 学生签到并确认
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "学生签到确认", businessType = BusinessType.INSERT)
    @PostMapping("/checkinAndConfirm")
    public AjaxResult checkinAndConfirm(@RequestBody StudentCheckinAndConfirmDto dto)
    {
        try {
            Long attendanceId = kgStudentAttendanceService.checkinAndConfirm(dto);
            if (attendanceId != null) {
                return AjaxResult.success("签到确认成功", attendanceId);
            } else {
                return AjaxResult.error("签到确认失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("签到确认异常：" + e.getMessage());
        }
    }

    /**
     * 缺勤登记
     */
    @SaCheckPermission("kg:attendance:student:absence")
    @Log(title = "学生缺勤登记", businessType = BusinessType.INSERT)
    @PostMapping("/absence")
    public AjaxResult registerAbsence(@RequestBody KgStudentAttendance kgStudentAttendance)
    {
        return toAjax(kgStudentAttendanceService.registerAbsence(kgStudentAttendance));
    }
}
