<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班级" prop="classId">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable>
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账户类型" prop="accountType">
        <el-select v-model="queryParams.accountType" placeholder="请选择账户类型" clearable>
          <el-option label="园费账户" value="tuition" />
          <el-option label="托管费账户" value="course" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易类型" prop="transactionType">
        <el-select v-model="queryParams.transactionType" placeholder="请选择交易类型" clearable>
          <el-option label="充值" value="recharge" />
          <el-option label="消费" value="consume" />
          <el-option label="退款" value="refund" />
          <el-option label="转入" value="transfer_in" />
          <el-option label="转出" value="transfer_out" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card recharge-stats">
          <div class="stats-icon">
            <i class="el-icon-plus"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">今日充值金额</div>
            <div class="stats-value">{{ formatMoney(statistics.todayRecharge) }}</div>
            <div class="stats-desc">{{ statistics.todayRechargeCount }}笔交易</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card consume-stats">
          <div class="stats-icon">
            <i class="el-icon-minus"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">今日消费金额</div>
            <div class="stats-value">{{ formatMoney(statistics.todayConsume) }}</div>
            <div class="stats-desc">{{ statistics.todayConsumeCount }}笔交易</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card net-stats">
          <div class="stats-icon">
            <i class="el-icon-data-line"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">今日净收入</div>
            <div class="stats-value">{{ formatMoney(statistics.todayNet) }}</div>
            <div class="stats-desc">充值减消费</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card total-stats">
          <div class="stats-icon">
            <i class="el-icon-coin"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">总交易笔数</div>
            <div class="stats-value">{{ statistics.totalTransactions }}</div>
            <div class="stats-desc">历史累计</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:prepayment:export']"
        >导出流水</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 流水记录列表 -->
    <el-table v-loading="loading" :data="transactionList">
      <el-table-column label="交易时间" align="center" prop="transactionTime" width="155">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.transactionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="学生信息" align="left" width="160">
        <template slot-scope="scope">
          <div class="student-info">
            <div class="student-name">{{ scope.row.studentName }}</div>
            <div class="student-meta">
              <el-tag size="mini" type="info">{{ scope.row.className }}</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="账户类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.relatedBillType === 'tuition' ? 'primary' : 'success'" size="mini">
            {{ scope.row.relatedBillType === 'tuition' ? '园费' : '托管费' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="交易类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getTransactionTypeTag(scope.row.transactionType)" size="mini">
            {{ getTransactionTypeName(scope.row.transactionType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="交易金额" align="center" width="120">
        <template slot-scope="scope">
          <span :class="getAmountClass(scope.row.transactionType)">
            {{ getAmountPrefix(scope.row.transactionType) }}{{ formatMoney(scope.row.amount) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="交易前余额" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ formatMoney(scope.row.beforeBalance) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="交易后余额" align="center" width="120">
        <template slot-scope="scope">
          <span :class="getBalanceClass(scope.row.afterBalance)">
            {{ formatMoney(scope.row.afterBalance) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="支付方式" align="center" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.paymentMethod">
            <i :class="getPaymentIcon(scope.row.paymentMethod)"></i>
            {{ getPaymentMethodName(scope.row.paymentMethod) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="关联账单" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.relatedBillId"
            size="mini"
            type="text"
            @click="handleViewBill(scope.row)"
          >
            查看账单
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作员" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.operatorName || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="描述" align="left" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.description || scope.row.remark || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['kg:prepayment:transaction:view']"
          >详情</el-button>
          <el-dropdown 
            size="mini" 
            @command="(command) => handleCommand(command, scope.row)" 
            v-hasPermi="['kg:prepayment:transaction:manage']"
          >
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="print" icon="el-icon-printer">打印凭证</el-dropdown-item>
              <el-dropdown-item 
                v-if="scope.row.transactionType === 'recharge'" 
                command="refund" 
                icon="el-icon-refresh-left"
              >申请退款</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 交易详情对话框 -->
    <el-dialog title="交易详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="流水号">{{ detailData.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="交易时间">{{ parseTime(detailData.transactionTime) }}</el-descriptions-item>
        <el-descriptions-item label="学生姓名">{{ detailData.studentName }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ detailData.className }}</el-descriptions-item>
        <el-descriptions-item label="账户类型">
          {{ detailData.accountType === 'tuition' ? '园费账户' : '托管费账户' }}
        </el-descriptions-item>
        <el-descriptions-item label="交易类型">
          <el-tag :type="getTransactionTypeTag(detailData.transactionType)" size="small">
            {{ getTransactionTypeName(detailData.transactionType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易金额" :span="2">
          <span :class="getAmountClass(detailData.transactionType)" style="font-size: 18px; font-weight: bold;">
            {{ getAmountPrefix(detailData.transactionType) }}{{ formatMoney(detailData.amount) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="交易前余额">{{ formatMoney(detailData.beforeBalance) }}</el-descriptions-item>
        <el-descriptions-item label="交易后余额">
          <span :class="getBalanceClass(detailData.afterBalance)">
            {{ formatMoney(detailData.afterBalance) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式" v-if="detailData.paymentMethod">
          <i :class="getPaymentIcon(detailData.paymentMethod)"></i>
          {{ getPaymentMethodName(detailData.paymentMethod) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作员">{{ detailData.operatorName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailData.description || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { getTransactionList, getTransactionStatistics } from "@/api/kg/prepayment";
import { listClass } from "@/api/kg/student/class";
import { listAllClass } from "@/api/kg/class/manage" 

export default {
  name: "PrepaymentTransaction",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 交易流水表格数据
      transactionList: [],
      // 班级列表
      classList: [],
      // 统计数据
      statistics: {
        todayRecharge: 0,
        todayRechargeCount: 0,
        todayConsume: 0,
        todayConsumeCount: 0,
        todayNet: 0,
        totalTransactions: 0
      },
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        classId: null,
        accountType: null,
        transactionType: null,
        beginTime: null,
        endTime: null
      },
      // 详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {}
    };
  },
  created() {
    // 如果有传入的学生ID，自动筛选
    if (this.$route.query.studentId) {
      this.queryParams.studentId = this.$route.query.studentId;
    }
    this.getList();
    this.getClassList();
    this.getStatistics();
  },
  methods: {
    /** 查询交易流水列表 */
    getList() {
      this.loading = true;
      this.queryParams.beginTime = null;
      this.queryParams.endTime = null;
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      
      getTransactionList(this.queryParams).then(response => {
        this.transactionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询班级列表 */
    getClassList() {
      listAllClass().then(response => {
        this.classList = response.data;
      });
    },
    /** 查询统计数据 */
    getStatistics() {
      getTransactionStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增充值 */
    handleRecharge() {
      this.$router.push('/finance/prepayment/recharge');
    },
    /** 导出流水 */
    handleExport() {
      // TODO: 实现导出功能
      this.$modal.msgSuccess("导出功能开发中");
    },
    /** 查看详情 */
    handleViewDetail(row) {
      this.detailData = row;
      this.detailOpen = true;
    },
    /** 查看账单 */
    handleViewBill(row) {
      if (row.relatedBillType === 'tuition') {
        this.$router.push(`/finance/tuition/bill/${row.relatedBillId}`);
      } else if (row.relatedBillType === 'course') {
        this.$router.push(`/finance/course-fee/bill/${row.relatedBillId}`);
      }
    },
    /** 下拉菜单操作 */
    handleCommand(command, row) {
      switch(command) {
        case 'print':
          this.$modal.msgSuccess("打印功能开发中");
          break;
        case 'refund':
          this.$modal.msgSuccess("退款功能开发中");
          break;
      }
    },
    /** 获取交易类型标签 */
    getTransactionTypeTag(type) {
      const map = {
        'recharge': 'success',
        'consume': 'info',
        'refund': 'warning',
        'transfer_in': 'primary',
        'transfer_out': 'danger'
      };
      return map[type] || 'info';
    },
    /** 获取交易类型名称 */
    getTransactionTypeName(type) {
      const map = {
        'recharge': '充值',
        'consume': '消费',
        'refund': '退款',
        'transfer_in': '转入',
        'transfer_out': '转出'
      };
      return map[type] || type;
    },
    /** 获取金额样式类 */
    getAmountClass(type) {
      if (['recharge', 'transfer_in', 'refund'].includes(type)) {
        return 'text-success';
      } else {
        return 'text-danger';
      }
    },
    /** 获取金额前缀 */
    getAmountPrefix(type) {
      if (['recharge', 'transfer_in', 'refund'].includes(type)) {
        return '+';
      } else {
        return '-';
      }
    },
    /** 获取余额状态类名 */
    getBalanceClass(balance) {
      if (balance <= 0) return 'balance-negative';
      if (balance < 100) return 'balance-insufficient';
      return 'balance-sufficient';
    },
    /** 获取支付方式图标 */
    getPaymentIcon(method) {
      const map = {
        'wechat': 'el-icon-chat-round',
        'alipay': 'el-icon-coin',
        'cash': 'el-icon-money',
        'bank': 'el-icon-bank-card'
      };
      return map[method] || 'el-icon-wallet';
    },
    /** 获取支付方式名称 */
    getPaymentMethodName(method) {
      const map = {
        'wechat': '微信',
        'alipay': '支付宝',
        'cash': '现金',
        'bank': '银行转账'
      };
      return map[method] || method;
    },
    /** 格式化金额 */
    formatMoney(amount) {
      return amount ? '¥' + parseFloat(amount).toFixed(2) : '¥0.00';
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.stats-card {
  display: flex;
  padding: 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 24px;
  color: #fff;
  margin-right: 16px;
}

.recharge-stats .stats-icon {
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
}

.consume-stats .stats-icon {
  background: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);
}

.net-stats .stats-icon {
  background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
}

.total-stats .stats-icon {
  background: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);
}

.stats-content {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stats-desc {
  font-size: 12px;
  color: #909399;
}

.student-info {
  text-align: left;
}

.student-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.student-meta {
  font-size: 12px;
  color: #909399;
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}

.balance-sufficient {
  color: #67C23A;
  font-weight: bold;
}

.balance-insufficient {
  color: #E6A23C;
  font-weight: bold;
}

.balance-negative {
  color: #F56C6C;
  font-weight: bold;
}
</style>
