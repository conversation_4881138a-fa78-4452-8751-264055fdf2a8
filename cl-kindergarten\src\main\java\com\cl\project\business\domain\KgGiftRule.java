package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.aspectj.lang.annotation.Excel;
import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 赠送规则配置对象 kg_gift_rule
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class KgGiftRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long ruleId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 规则类型：amount_based-金额型，attendance_based-出勤型 */
    @Excel(name = "规则类型")
    private String ruleType;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String description;

    /** 状态：0-禁用，1-启用 */
    @Excel(name = "状态")
    private String status;

    /** 触发金额（满足此金额才赠送） */
    @Excel(name = "触发金额")
    private BigDecimal triggerAmount;

    /** 触发出勤率阈值（百分比） */
    @Excel(name = "触发出勤率阈值")
    private BigDecimal triggerAttendanceRate;

    /** 触发课程类型（哪类课程消费触发） */
    @Excel(name = "触发课程类型")
    private String triggerCourseType;

    /** 赠送课程ID */
    @Excel(name = "赠送课程ID")
    private Long giftCourseId;

    /** 赠送课程名称 */
    @Excel(name = "赠送课程名称")
    private String giftCourseName;

    /** 赠送课时数 */
    @Excel(name = "赠送课时数")
    private Long giftSessions;

    /** 适用月份，如：1,2,3或all */
    @Excel(name = "适用月份")
    private String applicableMonths;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiryDate;

    /** 每个学生最大享受次数 */
    @Excel(name = "每个学生最大享受次数")
    private Long maxTimesPerStudent;

    /** 每人每月最大次数 */
    @Excel(name = "每人每月最大次数")
    private Long maxTimesPerMonth;

    /** 机构ID */
    @Excel(name = "机构ID")
    private Long comId;

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setRuleName(String ruleName) 
    {
        this.ruleName = ruleName;
    }

    public String getRuleName() 
    {
        return ruleName;
    }
    public void setRuleType(String ruleType) 
    {
        this.ruleType = ruleType;
    }

    public String getRuleType() 
    {
        return ruleType;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setTriggerAmount(BigDecimal triggerAmount) 
    {
        this.triggerAmount = triggerAmount;
    }

    public BigDecimal getTriggerAmount() 
    {
        return triggerAmount;
    }
    public void setTriggerAttendanceRate(BigDecimal triggerAttendanceRate) 
    {
        this.triggerAttendanceRate = triggerAttendanceRate;
    }

    public BigDecimal getTriggerAttendanceRate() 
    {
        return triggerAttendanceRate;
    }
    public void setTriggerCourseType(String triggerCourseType) 
    {
        this.triggerCourseType = triggerCourseType;
    }

    public String getTriggerCourseType() 
    {
        return triggerCourseType;
    }
    public void setGiftCourseId(Long giftCourseId) 
    {
        this.giftCourseId = giftCourseId;
    }

    public Long getGiftCourseId() 
    {
        return giftCourseId;
    }
    public void setGiftCourseName(String giftCourseName) 
    {
        this.giftCourseName = giftCourseName;
    }

    public String getGiftCourseName() 
    {
        return giftCourseName;
    }
    public void setGiftSessions(Long giftSessions) 
    {
        this.giftSessions = giftSessions;
    }

    public Long getGiftSessions() 
    {
        return giftSessions;
    }
    public void setApplicableMonths(String applicableMonths) 
    {
        this.applicableMonths = applicableMonths;
    }

    public String getApplicableMonths() 
    {
        return applicableMonths;
    }
    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }
    public void setExpiryDate(Date expiryDate) 
    {
        this.expiryDate = expiryDate;
    }

    public Date getExpiryDate() 
    {
        return expiryDate;
    }
    public void setMaxTimesPerStudent(Long maxTimesPerStudent) 
    {
        this.maxTimesPerStudent = maxTimesPerStudent;
    }

    public Long getMaxTimesPerStudent() 
    {
        return maxTimesPerStudent;
    }
    public void setMaxTimesPerMonth(Long maxTimesPerMonth) 
    {
        this.maxTimesPerMonth = maxTimesPerMonth;
    }

    public Long getMaxTimesPerMonth() 
    {
        return maxTimesPerMonth;
    }
    public void setComId(Long comId) 
    {
        this.comId = comId;
    }

    public Long getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("ruleName", getRuleName())
            .append("ruleType", getRuleType())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("triggerAmount", getTriggerAmount())
            .append("triggerAttendanceRate", getTriggerAttendanceRate())
            .append("triggerCourseType", getTriggerCourseType())
            .append("giftCourseId", getGiftCourseId())
            .append("giftCourseName", getGiftCourseName())
            .append("giftSessions", getGiftSessions())
            .append("applicableMonths", getApplicableMonths())
            .append("effectiveDate", getEffectiveDate())
            .append("expiryDate", getExpiryDate())
            .append("maxTimesPerStudent", getMaxTimesPerStudent())
            .append("maxTimesPerMonth", getMaxTimesPerMonth())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
