package com.cl.project.business.service.impl;

import com.cl.project.business.domain.KgCourse;
import com.cl.project.business.domain.KgCourseAttendance;
import com.cl.project.business.domain.KgTeacher;
import com.cl.project.business.domain.KgTeacherSalary;
import com.cl.project.business.domain.dto.TeacherCourseStatsDto;
import com.cl.project.business.mapper.KgCourseAttendanceMapper;
import com.cl.project.business.mapper.KgCourseMapper;
import com.cl.project.business.mapper.KgTeacherMapper;
import com.cl.project.business.mapper.KgTeacherSalaryMapper;
import com.cl.project.business.service.IKgTeacherCourseStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教师课时统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class KgTeacherCourseStatsServiceImpl implements IKgTeacherCourseStatsService {
    
    @Autowired
    private KgCourseAttendanceMapper kgCourseAttendanceMapper;
    
    @Autowired
    private KgCourseMapper kgCourseMapper;
    
    @Autowired
    private KgTeacherMapper kgTeacherMapper;
    
    @Autowired
    private KgTeacherSalaryMapper kgTeacherSalaryMapper;
    
    /**
     * 计算教师课时统计
     */
    @Override
    @Transactional
    public Map<String, Object> calculateTeacherCourseStats(Map<String, Object> params) {
        Integer year = (Integer) params.get("statYear");
        Integer month = (Integer) params.get("statMonth");
        String calculateType = (String) params.get("calculateType");
        Long teacherId = (Long) params.get("teacherId");
        Boolean confirmedOnly = (Boolean) params.get("confirmedOnly");
        
        if (year == null || month == null) {
            throw new RuntimeException("年份和月份不能为空");
        }
        
        List<Long> teacherIds = new ArrayList<>();
        
        if ("single".equals(calculateType) && teacherId != null) {
            // 计算单个教师
            teacherIds.add(teacherId);
        } else {
            // 计算所有教师
            KgTeacher queryTeacher = new KgTeacher();
            queryTeacher.setStatus("0"); // 在职教师
            List<KgTeacher> teachers = kgTeacherMapper.selectKgTeacherList(queryTeacher);
            teacherIds = teachers.stream().map(KgTeacher::getTeacherId).collect(Collectors.toList());
        }
        
        int processedCount = 0;
        
        for (Long tid : teacherIds) {
            try {
                Map<String, Object> result = calculateSingleTeacherStats(tid, year, month, confirmedOnly);
                if ((Boolean) result.get("success")) {
                    processedCount++;
                }
            } catch (Exception e) {
                // 记录日志，但不影响其他教师的计算
                System.err.println("计算教师 " + tid + " 课时统计失败: " + e.getMessage());
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("processedCount", processedCount);
        result.put("message", "成功计算 " + processedCount + " 位教师的课时费");
        
        return result;
    }
    
    /**
     * 计算单个教师的课时统计
     */
    private Map<String, Object> calculateSingleTeacherStats(Long teacherId, Integer year, Integer month, Boolean confirmedOnly) {
        // 查询教师基本信息
        KgTeacher teacher = kgTeacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        // 构建查询条件
        KgCourseAttendance queryAttendance = new KgCourseAttendance();
        queryAttendance.setTeacherId(teacherId);
        
        // 设置时间范围（当月）
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1, 0, 0, 0);
        Date startDate = calendar.getTime();
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date endDate = calendar.getTime();
        
        // 查询考勤记录（这里需要在Mapper中添加时间范围查询）
        List<KgCourseAttendance> attendanceList = kgCourseAttendanceMapper.selectKgCourseAttendanceList(queryAttendance);
        
        // 过滤时间范围和确认状态
        List<KgCourseAttendance> filteredList = attendanceList.stream()
            .filter(attendance -> {
                if (attendance.getAttendanceDate() == null) return false;
                boolean inDateRange = !attendance.getAttendanceDate().before(startDate) && 
                                    !attendance.getAttendanceDate().after(endDate);
                if (!inDateRange) return false;
                
                // 如果只计算已确认的记录
                if (confirmedOnly != null && confirmedOnly) {
                    return attendance.getIsConfirmed() != null && attendance.getIsConfirmed() == 1;
                }
                return true;
            })
            .collect(Collectors.toList());
        
        // 按课程分组统计
        Map<Long, List<KgCourseAttendance>> groupedByCourse = filteredList.stream()
            .collect(Collectors.groupingBy(KgCourseAttendance::getCourseId));
        
        BigDecimal totalCourseFee = BigDecimal.ZERO;
        int totalSessions = 0;
        
        for (Map.Entry<Long, List<KgCourseAttendance>> entry : groupedByCourse.entrySet()) {
            Long courseId = entry.getKey();
            List<KgCourseAttendance> courseAttendances = entry.getValue();
            
            // 获取课程信息
            KgCourse course = kgCourseMapper.selectKgCourseById(courseId);
            if (course == null) continue;
            
            BigDecimal pricePerSession = course.getPricePerSession() != null ? 
                course.getPricePerSession() : BigDecimal.ZERO;
            
            int sessions = courseAttendances.size();
            BigDecimal courseFee = pricePerSession.multiply(new BigDecimal(sessions));
            
            totalSessions += sessions;
            totalCourseFee = totalCourseFee.add(courseFee);
        }
        
        // 更新或创建工资表中的课时费记录
        updateTeacherSalaryCourse(teacherId, year, month, totalCourseFee, totalSessions);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("teacherId", teacherId);
        result.put("teacherName", teacher.getTeacherName());
        result.put("totalSessions", totalSessions);
        result.put("totalCourseFee", totalCourseFee);
        
        return result;
    }
    
    /**
     * 更新教师工资表中的课时费
     */
    private void updateTeacherSalaryCourse(Long teacherId, Integer year, Integer month, 
                                         BigDecimal courseFee, Integer sessions) {
        // 查找当月工资记录
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setTeacherId(teacherId);
        queryParams.setSalaryYear(year.longValue());
        queryParams.setSalaryMonth(month.longValue());
        
        List<KgTeacherSalary> salaryList = kgTeacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        KgTeacherSalary salary;
        if (salaryList.isEmpty()) {
            // 创建新的工资记录
            salary = new KgTeacherSalary();
            salary.setTeacherId(teacherId);
            salary.setSalaryYear(year.longValue());
            salary.setSalaryMonth(month.longValue());
            salary.setCourseBonus(courseFee);
            salary.setSalaryStatus("calculated");
            salary.setCreateTime(new Date());
            
            // 计算应发和实发工资（这里简化处理）
            BigDecimal baseSalary = getTeacherBaseSalary(teacherId);
            salary.setBaseSalary(baseSalary);
            salary.setGrossSalary(baseSalary.add(courseFee));
            salary.setNetSalary(baseSalary.add(courseFee));
            
            kgTeacherSalaryMapper.insertKgTeacherSalary(salary);
        } else {
            // 更新现有记录
            salary = salaryList.get(0);
            salary.setCourseBonus(courseFee);
            salary.setUpdateTime(new Date());
            
            // 重新计算应发和实发工资
            BigDecimal baseSalary = salary.getBaseSalary() != null ? 
                salary.getBaseSalary() : BigDecimal.ZERO;
            BigDecimal otherBonus = salary.getOtherBonus() != null ? 
                salary.getOtherBonus() : BigDecimal.ZERO;
            BigDecimal deductions = (salary.getSocialInsurance() != null ? 
                salary.getSocialInsurance() : BigDecimal.ZERO)
                .add(salary.getOtherDeduction() != null ? 
                     salary.getOtherDeduction() : BigDecimal.ZERO);
            
            salary.setGrossSalary(baseSalary.add(courseFee).add(otherBonus));
            salary.setNetSalary(salary.getGrossSalary().subtract(deductions));
            
            kgTeacherSalaryMapper.updateKgTeacherSalary(salary);
        }
    }
    
    /**
     * 获取教师基本工资
     */
    private BigDecimal getTeacherBaseSalary(Long teacherId) {
        KgTeacher teacher = kgTeacherMapper.selectKgTeacherById(teacherId);
        return teacher != null && teacher.getBaseSalary() != null ? 
            teacher.getBaseSalary() : new BigDecimal("3000"); // 默认基本工资
    }
    
    @Override
    public List<TeacherCourseStatsDto> getTeacherCourseStatsList(Map<String, Object> params) {
        Integer year = (Integer) params.get("statYear");
        Integer month = (Integer) params.get("statMonth");
        Long teacherId = (Long) params.get("teacherId");
        
        if (year == null || month == null) {
            return new ArrayList<>();
        }
        
        // 查询工资表中的课时费记录
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setSalaryYear(year.longValue());
        queryParams.setSalaryMonth(month.longValue());
        if (teacherId != null) {
            queryParams.setTeacherId(teacherId);
        }
        
        List<KgTeacherSalary> salaryList = kgTeacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        return salaryList.stream().map(this::convertToStatsDto).collect(Collectors.toList());
    }
    
    /**
     * 转换为统计DTO
     */
    private TeacherCourseStatsDto convertToStatsDto(KgTeacherSalary salary) {
        TeacherCourseStatsDto dto = new TeacherCourseStatsDto();
        dto.setTeacherId(salary.getTeacherId());
        dto.setStatYear(salary.getSalaryYear() != null ? salary.getSalaryYear().intValue() : null);
        dto.setStatMonth(salary.getSalaryMonth() != null ? salary.getSalaryMonth().intValue() : null);
        dto.setTotalCourseFee(salary.getCourseBonus() != null ? salary.getCourseBonus() : BigDecimal.ZERO);
        dto.setUpdateTime(salary.getUpdateTime());
        
        // 获取教师信息
        KgTeacher teacher = kgTeacherMapper.selectKgTeacherById(salary.getTeacherId());
        if (teacher != null) {
            dto.setTeacherCode(teacher.getTeacherCode());
            dto.setTeacherName(teacher.getTeacherName());
        }
        
        // 获取课程详细信息（这里简化处理，实际应该从考勤记录中统计）
        dto.setCourseDetails(getCourseDetailsForTeacher(salary.getTeacherId(), 
            salary.getSalaryYear() != null ? salary.getSalaryYear().intValue() : null, 
            salary.getSalaryMonth() != null ? salary.getSalaryMonth().intValue() : null));
        
        // 计算总课时数
        int totalSessions = dto.getCourseDetails().stream()
            .mapToInt(TeacherCourseStatsDto.CourseDetailDto::getSessions)
            .sum();
        dto.setTotalSessions(totalSessions);
        
        return dto;
    }
    
    /**
     * 获取教师课程详细信息
     */
    private List<TeacherCourseStatsDto.CourseDetailDto> getCourseDetailsForTeacher(
            Long teacherId, Integer year, Integer month) {
        
        // 构建时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        Date startDate = calendar.getTime();
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date endDate = calendar.getTime();
        
        // 查询考勤记录
        KgCourseAttendance queryAttendance = new KgCourseAttendance();
        queryAttendance.setTeacherId(teacherId);
        List<KgCourseAttendance> attendanceList = kgCourseAttendanceMapper.selectKgCourseAttendanceList(queryAttendance);
        
        // 过滤时间范围和确认状态
        List<KgCourseAttendance> filteredList = attendanceList.stream()
            .filter(attendance -> {
                if (attendance.getAttendanceDate() == null) return false;
                return !attendance.getAttendanceDate().before(startDate) && 
                       !attendance.getAttendanceDate().after(endDate) &&
                       attendance.getIsConfirmed() != null && attendance.getIsConfirmed() == 1;
            })
            .collect(Collectors.toList());
        
        // 按课程分组统计
        Map<Long, List<KgCourseAttendance>> groupedByCourse = filteredList.stream()
            .collect(Collectors.groupingBy(KgCourseAttendance::getCourseId));
        
        List<TeacherCourseStatsDto.CourseDetailDto> details = new ArrayList<>();
        
        for (Map.Entry<Long, List<KgCourseAttendance>> entry : groupedByCourse.entrySet()) {
            Long courseId = entry.getKey();
            List<KgCourseAttendance> courseAttendances = entry.getValue();
            
            KgCourse course = kgCourseMapper.selectKgCourseById(courseId);
            if (course == null) continue;
            
            TeacherCourseStatsDto.CourseDetailDto detail = new TeacherCourseStatsDto.CourseDetailDto(
                courseId,
                course.getCourseName(),
                course.getCourseType(),
                courseAttendances.size(),
                course.getPricePerSession() != null ? course.getPricePerSession() : BigDecimal.ZERO
            );
            
            details.add(detail);
        }
        
        return details;
    }
    
    @Override
    public Map<String, Object> getTeacherCourseStatsSummary(Map<String, Object> params) {
        List<TeacherCourseStatsDto> statsList = getTeacherCourseStatsList(params);
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalTeachers", statsList.size());
        
        int totalSessions = statsList.stream()
            .mapToInt(stats -> stats.getTotalSessions() != null ? stats.getTotalSessions() : 0)
            .sum();
        summary.put("totalSessions", totalSessions);
        
        BigDecimal totalCourseFee = statsList.stream()
            .map(stats -> stats.getTotalCourseFee() != null ? stats.getTotalCourseFee() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        summary.put("totalCourseFee", totalCourseFee);
        
        BigDecimal avgFeePerSession = totalSessions > 0 ? 
            totalCourseFee.divide(new BigDecimal(totalSessions), 2, java.math.RoundingMode.HALF_UP) : 
            BigDecimal.ZERO;
        summary.put("avgFeePerSession", avgFeePerSession);
        
        return summary;
    }
    
    @Override
    public List<Map<String, Object>> getTeacherCourseDetails(Long teacherId, Map<String, Object> params) {
        Integer year = (Integer) params.get("statYear");
        Integer month = (Integer) params.get("statMonth");
        
        if (year == null || month == null) {
            return new ArrayList<>();
        }
        
        List<TeacherCourseStatsDto.CourseDetailDto> details = getCourseDetailsForTeacher(teacherId, year, month);
        
        return details.stream().map(detail -> {
            Map<String, Object> map = new HashMap<>();
            map.put("courseId", detail.getCourseId());
            map.put("courseName", detail.getCourseName());
            map.put("courseType", detail.getCourseType());
            map.put("sessions", detail.getSessions());
            map.put("pricePerSession", detail.getPricePerSession());
            map.put("subtotal", detail.getSubtotal());
            return map;
        }).collect(Collectors.toList());
    }
    
    @Override
    public Map<String, Object> recalculateTeacherCourseStats(Long teacherId, Integer year, Integer month) {
        return calculateSingleTeacherStats(teacherId, year, month, true);
    }
}
