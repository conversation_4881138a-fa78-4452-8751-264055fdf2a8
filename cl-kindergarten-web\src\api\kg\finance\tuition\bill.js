import request from '@/utils/request'

// 查询园费账单列表
export function listTuitionBill(query) {
  return request({
    url: '/business/tuition-bill/list',
    method: 'get',
    params: query
  })
}

// 获取园费账单详细信息
export function getTuitionBill(billId) {
  return request({
    url: '/business/tuition-bill/' + billId,
    method: 'get'
  })
}

// 新增园费账单
export function addTuitionBill(data) {
  return request({
    url: '/business/tuition-bill',
    method: 'post',
    data: data
  })
}

// 修改园费账单
export function updateTuitionBill(data) {
  return request({
    url: '/business/tuition-bill',
    method: 'put',
    data: data
  })
}

// 删除园费账单
export function delTuitionBill(billIds) {
  return request({
    url: '/business/tuition-bill/' + billIds,
    method: 'delete'
  })
}

// 导出园费账单
export function exportTuitionBill(query) {
  return request({
    url: '/business/tuition-bill/export',
    method: 'get',
    params: query
  })
}

// 生成月度园费账单
export function generateMonthlyTuitionBills(data) {
  return request({
    url: '/business/tuition-calculation/generate-monthly',
    method: 'post',
    data: data
  })
}

// 发送园费账单
export function sendTuitionBill(billId) {
  return request({
    url: '/business/tuition-bill/send/' + billId,
    method: 'post'
  })
}

// 标记园费账单已支付
export function markTuitionBillPaid(billId) {
  return request({
    url: '/business/tuition-bill/mark-paid/' + billId,
    method: 'post'
  })
}

// 获取园费账单统计
export function getTuitionBillStatistics(query) {
  return request({
    url: '/business/tuition-calculation/statistics',
    method: 'get',
    params: query
  })
}

// 计算学生月度园费
export function calculateStudentTuition(studentId, year, month) {
  return request({
    url: '/business/tuition-calculation/calculate-student',
    method: 'post',
    params: { studentId, year, month }
  })
}

// 批量计算班级园费
export function calculateClassTuition(classId, year, month) {
  return request({
    url: '/business/tuition-calculation/calculate-class',
    method: 'post',
    params: { classId, year, month }
  })
}
