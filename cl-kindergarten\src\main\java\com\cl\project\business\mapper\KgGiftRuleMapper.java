package com.cl.project.business.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.cl.project.business.domain.KgGiftRule;

/**
 * 赠送规则配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface KgGiftRuleMapper 
{
    /**
     * 查询赠送规则配置
     * 
     * @param ruleId 赠送规则配置主键
     * @return 赠送规则配置
     */
    public KgGiftRule selectKgGiftRuleByRuleId(Long ruleId);

    /**
     * 查询赠送规则配置列表
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 赠送规则配置集合
     */
    public List<KgGiftRule> selectKgGiftRuleList(KgGiftRule kgGiftRule);

    /**
     * 新增赠送规则配置
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 结果
     */
    public int insertKgGiftRule(KgGiftRule kgGiftRule);

    /**
     * 修改赠送规则配置
     * 
     * @param kgGiftRule 赠送规则配置
     * @return 结果
     */
    public int updateKgGiftRule(KgGiftRule kgGiftRule);

    /**
     * 删除赠送规则配置
     * 
     * @param ruleId 赠送规则配置主键
     * @return 结果
     */
    public int deleteKgGiftRuleByRuleId(Long ruleId);

    /**
     * 批量删除赠送规则配置
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKgGiftRuleByRuleIds(Long[] ruleIds);

    /**
     * 查询启用的赠送规则列表
     * 
     * @return 启用的赠送规则集合
     */
    public List<KgGiftRule> selectActiveGiftRules();

    /**
     * 根据规则类型查询启用的规则
     * 
     * @param ruleType 规则类型
     * @return 启用的赠送规则集合
     */
    public List<KgGiftRule> selectActiveGiftRulesByType(String ruleType);
}
