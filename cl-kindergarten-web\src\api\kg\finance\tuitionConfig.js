import request from '@/utils/request'

// 查询园费配置列表
export function listTuitionConfig(query) {
  return request({
    url: '/business/tuition-config/list',
    method: 'get',
    params: query
  })
}

// 新增园费配置
export function addTuitionConfig(data) {
  return request({
    url: '/business/tuition-config',
    method: 'post',
    data: data
  })
}

// 编辑园费配置
export function updateTuitionConfig(data) {
  return request({
    url: '/business/tuition-config',
    method: 'put',
    data: data
  })
}

// 删除园费配置
export function deleteTuitionConfig(configId) {
  return request({
    url: `/business/tuition-config/${configId}`,
    method: 'delete'
  })
}

// 导出园费配置
export function exportTuitionConfig(query) {
  return request({
    url: '/business/tuition-config/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}

// 启用/停用配置
export function changeStatus(configId, status) {
  return request({
    url: '/business/tuition-config/changeStatus',
    method: 'put',
    data: { configId, status }
  })
}
