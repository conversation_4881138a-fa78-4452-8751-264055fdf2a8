package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgGiftRecord;

/**
 * 赠送记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IKgGiftRecordService 
{
    /**
     * 查询赠送记录
     * 
     * @param recordId 赠送记录主键
     * @return 赠送记录
     */
    public KgGiftRecord selectKgGiftRecordByRecordId(Long recordId);

    /**
     * 查询赠送记录列表
     * 
     * @param kgGiftRecord 赠送记录
     * @return 赠送记录集合
     */
    public List<KgGiftRecord> selectKgGiftRecordList(KgGiftRecord kgGiftRecord);

    /**
     * 新增赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    public int insertKgGiftRecord(KgGiftRecord kgGiftRecord);

    /**
     * 修改赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    public int updateKgGiftRecord(KgGiftRecord kgGiftRecord);

    /**
     * 批量删除赠送记录
     * 
     * @param recordIds 需要删除的赠送记录主键集合
     * @return 结果
     */
    public int deleteKgGiftRecordByRecordIds(Long[] recordIds);

    /**
     * 删除赠送记录信息
     * 
     * @param recordId 赠送记录主键
     * @return 结果
     */
    public int deleteKgGiftRecordByRecordId(Long recordId);

    /**
     * 查询学生的有效赠送记录
     * 
     * @param studentId 学生ID
     * @return 有效赠送记录列表
     */
    public List<KgGiftRecord> selectActiveGiftRecordsByStudentId(Long studentId);

    /**
     * 统计学生在指定规则下的赠送次数
     * 
     * @param studentId 学生ID
     * @param ruleId 规则ID
     * @return 赠送次数
     */
    public int countGiftRecordsByStudentAndRule(Long studentId, Long ruleId);

    /**
     * 统计指定规则在指定月份的赠送次数
     * 
     * @param ruleId 规则ID
     * @param giftMonth 赠送月份
     * @return 赠送次数
     */
    public int countGiftRecordsByRuleAndMonth(Long ruleId, String giftMonth);

    /**
     * 更新赠送记录的使用课时
     * 
     * @param recordId 记录ID
     * @param usedSessions 已使用课时
     * @return 结果
     */
    public int updateGiftRecordUsedSessions(Long recordId, Integer usedSessions);
}
