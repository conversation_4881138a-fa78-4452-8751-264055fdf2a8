package com.cl.project.business.service;

import com.cl.project.business.domain.dto.TeacherCourseStatsDto;
import java.util.List;
import java.util.Map;

/**
 * 教师课时统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IKgTeacherCourseStatsService {
    
    /**
     * 计算教师课时统计
     * 
     * @param params 计算参数（年份、月份、教师ID等）
     * @return 处理结果
     */
    Map<String, Object> calculateTeacherCourseStats(Map<String, Object> params);
    
    /**
     * 获取教师课时统计列表
     * 
     * @param params 查询参数
     * @return 统计列表
     */
    List<TeacherCourseStatsDto> getTeacherCourseStatsList(Map<String, Object> params);
    
    /**
     * 获取教师课时统计汇总
     * 
     * @param params 查询参数
     * @return 汇总数据
     */
    Map<String, Object> getTeacherCourseStatsSummary(Map<String, Object> params);
    
    /**
     * 获取教师详细授课记录
     * 
     * @param teacherId 教师ID
     * @param params 查询参数
     * @return 授课记录列表
     */
    List<Map<String, Object>> getTeacherCourseDetails(Long teacherId, Map<String, Object> params);
    
    /**
     * 重新计算指定教师的课时统计
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 计算结果
     */
    Map<String, Object> recalculateTeacherCourseStats(Long teacherId, Integer year, Integer month);
}
