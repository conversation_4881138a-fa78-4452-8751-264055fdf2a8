<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ teacherName }} - 详细记录</text>
				</view>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-card">
				<view class="stats-item">
					<text class="stats-label">统计月份</text>
					<text class="stats-value">{{ year }}年{{ month }}月</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">总授课节数</text>
					<text class="stats-value highlight">{{ totalSessions }}节</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">总课时费</text>
					<text class="stats-value highlight">¥{{ totalFee }}</text>
				</view>
			</view>
		</view>

		<!-- 详细记录列表 -->
		<view class="records-section">
			<view class="section-title">
				<text>详细授课记录</text>
			</view>
			
			<view v-if="loading" class="loading-container">
				<u-loading-icon mode="circle"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>
			
			<view v-else-if="attendanceDetails.length === 0" class="empty-state">
				<view class="empty-icon">📋</view>
				<text class="empty-title">暂无记录</text>
				<text class="empty-desc">该教师在此月份暂无授课记录</text>
			</view>
			
			<view v-else class="records-list">
				<view 
					v-for="(record, index) in attendanceDetails" 
					:key="index"
					class="record-card"
				>
					<view class="record-header">
						<view class="record-date">
							<text class="date-text">{{ record.attendanceDate }}</text>
							<view class="status-tag" :class="{ confirmed: record.isConfirmed }">
								<text>{{ record.isConfirmed ? '已确认' : '待确认' }}</text>
							</view>
						</view>
						<view class="record-fee">
							<text class="fee-text">¥{{ record.pricePerSession }}</text>
						</view>
					</view>
					
					<view class="record-content">
						<view class="record-info">
							<view class="info-item">
								<text class="info-label">课程名称</text>
								<text class="info-value">{{ record.courseName }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">上课时间</text>
								<text class="info-value">{{ record.startTime }} - {{ record.endTime }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">学生数量</text>
								<text class="info-value">{{ record.studentCount }}人</text>
							</view>
						</view>
						
						<view v-if="record.remark" class="record-remark">
							<text class="remark-label">备注：</text>
							<text class="remark-text">{{ record.remark }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getTeacherCourseDetails } from '@/api/api.js'

export default {
	data() {
		return {
			loading: false,
			teacherId: '',
			teacherName: '',
			year: '',
			month: '',
			attendanceDetails: [],
			totalSessions: 0,
			totalFee: 0
		}
	},
	
	onLoad(options) {
		this.teacherId = options.teacherId || ''
		this.teacherName = decodeURIComponent(options.teacherName || '')
		this.year = options.year || ''
		this.month = options.month || ''
		
		if (this.teacherId) {
			this.loadDetails()
		}
	},
	
	methods: {
		/** 加载详细记录 */
		async loadDetails() {
			this.loading = true
			try {
				const response = await getTeacherCourseDetails(this.teacherId, {
					statYear: parseInt(this.year),
					statMonth: parseInt(this.month)
				})
				
				if (response.code === 200) {
					this.attendanceDetails = response.data || []
					this.calculateTotals()
				} else {
					toast(response.msg || '获取详细记录失败')
				}
			} catch (error) {
				console.error('获取详细记录失败:', error)
				toast('获取详细记录失败，请重试')
			} finally {
				this.loading = false
			}
		},
		
		/** 计算总计 */
		calculateTotals() {
			this.totalSessions = this.attendanceDetails.length
			this.totalFee = this.attendanceDetails.reduce((sum, record) => {
				return sum + (parseFloat(record.pricePerSession) || 0)
			}, 0).toFixed(2)
		},
		
		/** 返回上一页 */
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	max-width: 60%;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 统计信息 */
.stats-section {
	margin: 20rpx;
}

.stats-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.stats-label {
	font-size: 28rpx;
	color: #666666;
}

.stats-value {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	
	&.highlight {
		color: #FF9800;
		font-weight: 600;
		font-size: 32rpx;
	}
}

/* 记录列表 */
.records-section {
	margin: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 20rpx;
	padding: 0 8rpx;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
	margin-top: 20rpx;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 26rpx;
	color: #666666;
	display: block;
	line-height: 1.5;
}

/* 记录卡片 */
.records-list {
	.record-card {
		background: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

.record-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.record-date {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.date-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.status-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	background: #f0f0f0;
	color: #999999;
	
	&.confirmed {
		background: #e8f5e8;
		color: #52c41a;
	}
}

.record-fee {
	.fee-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #FF9800;
	}
}

.record-content {
	.record-info {
		.info-item {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
		
		.info-label {
			font-size: 26rpx;
			color: #666666;
			min-width: 140rpx;
		}
		
		.info-value {
			font-size: 26rpx;
			color: #333333;
			flex: 1;
		}
	}
	
	.record-remark {
		margin-top: 20rpx;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
		border-left: 4rpx solid #FF9800;
		
		.remark-label {
			font-size: 24rpx;
			color: #666666;
		}
		
		.remark-text {
			font-size: 24rpx;
			color: #333333;
			line-height: 1.5;
		}
	}
}
</style>
