<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="工资年月" prop="salaryMonth">
        <el-date-picker
          v-model="queryDate"
          type="month"
          placeholder="选择年月"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="教师" prop="teacherId">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable filterable>
          <el-option
            v-for="teacher in teacherOptions"
            :key="teacher.teacherId"
            :label="teacher.teacherName"
            :value="teacher.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="工资状态" prop="salaryStatus">
        <el-select v-model="queryParams.salaryStatus" placeholder="请选择状态" clearable>
          <el-option label="已计算" value="calculated" />
          <el-option label="已确认" value="confirmed" />
          <el-option label="已发放" value="paid" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增工资</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchConfirm"
        >批量确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Money"
          :disabled="multiple"
          @click="handleBatchPay"
        >批量发放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalSalaries }}</div>
            <div class="stat-label">总工资记录</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">￥{{ statistics.totalGrossAmount }}</div>
            <div class="stat-label">应发工资总额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">￥{{ statistics.totalNetAmount }}</div>
            <div class="stat-label">实发工资总额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.paidCount }}</div>
            <div class="stat-label">已发放数量</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="salaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="工资编号" align="center" prop="salaryId" width="100" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" width="120" />
      <el-table-column label="工资年月" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.salaryYear }}-{{ String(scope.row.salaryMonth).padStart(2, '0') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="基本工资" align="center" prop="baseSalary" width="100">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.baseSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" prop="courseBonus" width="100">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.courseBonus }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" prop="grossSalary" width="120">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.grossSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" align="center" prop="netSalary" width="120">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.netSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工资状态" align="center" prop="salaryStatus" width="100">
        <template #default="scope">
          <el-tag :type="getSalaryStatusType(scope.row.salaryStatus)">
            {{ getSalaryStatusText(scope.row.salaryStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="确认时间" align="center" prop="confirmedTime" width="140" />
      <el-table-column label="发放时间" align="center" prop="paidTime" width="140" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewDetail(scope.row)">详情</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button 
            link 
            type="warning" 
            v-if="scope.row.salaryStatus === 'calculated'"
            @click="handleConfirm(scope.row)"
          >确认</el-button>
          <el-button 
            link 
            type="success"
            v-if="scope.row.salaryStatus === 'confirmed'"
            @click="handlePay(scope.row)"
          >发放</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 工资详情对话框 -->
    <el-dialog title="工资详情" v-model="detailDialogVisible" width="800px">
      <div v-if="salaryDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工资编号">{{ salaryDetail.salaryId }}</el-descriptions-item>
          <el-descriptions-item label="教师姓名">{{ salaryDetail.teacherName }}</el-descriptions-item>
          <el-descriptions-item label="工资年月">{{ salaryDetail.salaryYear }}-{{ String(salaryDetail.salaryMonth).padStart(2, '0') }}</el-descriptions-item>
          <el-descriptions-item label="工资状态">
            <el-tag :type="getSalaryStatusType(salaryDetail.salaryStatus)">
              {{ getSalaryStatusText(salaryDetail.salaryStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">工资构成</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="基本工资">￥{{ salaryDetail.baseSalary || 0 }}</el-descriptions-item>
          <el-descriptions-item label="出勤天数">{{ salaryDetail.attendanceDays || 0 }}天</el-descriptions-item>
          <el-descriptions-item label="满勤奖">￥{{ salaryDetail.attendanceBonus || 0 }}</el-descriptions-item>
          <el-descriptions-item label="课时费">￥{{ salaryDetail.courseBonus || 0 }}</el-descriptions-item>
          <el-descriptions-item label="报名奖励">￥{{ salaryDetail.enrollmentBonus || 0 }}</el-descriptions-item>
          <el-descriptions-item label="出勤率奖励">￥{{ salaryDetail.attendanceRateBonus || 0 }}</el-descriptions-item>
          <el-descriptions-item label="新生奖励">￥{{ salaryDetail.newStudentBonus || 0 }}</el-descriptions-item>
          <el-descriptions-item label="其他奖励">￥{{ salaryDetail.otherBonus || 0 }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">扣除项目</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="退园扣款">￥{{ salaryDetail.withdrawalPenalty || 0 }}</el-descriptions-item>
          <el-descriptions-item label="社保代扣">￥{{ salaryDetail.socialInsurance || 0 }}</el-descriptions-item>
          <el-descriptions-item label="其他扣款">￥{{ salaryDetail.otherDeduction || 0 }}</el-descriptions-item>
          <el-descriptions-item label="绩效积分">{{ salaryDetail.performanceScore || 0 }}分</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">工资汇总</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="应发工资">
            <span class="text-price">￥{{ salaryDetail.grossSalary || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="实发工资">
            <span class="text-price">￥{{ salaryDetail.netSalary || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="确认人">{{ salaryDetail.confirmedByName || '未确认' }}</el-descriptions-item>
          <el-descriptions-item label="确认时间">{{ salaryDetail.confirmedTime || '未确认' }}</el-descriptions-item>
          <el-descriptions-item label="发放时间">{{ salaryDetail.paidTime || '未发放' }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ salaryDetail.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增/修改工资对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="salaryRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="教师" prop="teacherId">
              <el-select v-model="form.teacherId" placeholder="请选择教师" filterable>
                <el-option
                  v-for="teacher in teacherOptions"
                  :key="teacher.teacherId"
                  :label="teacher.teacherName"
                  :value="teacher.teacherId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工资年月" prop="salaryMonth">
              <el-date-picker
                v-model="form.salaryMonth"
                type="month"
                placeholder="选择年月"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="基本工资" prop="baseSalary">
              <el-input-number v-model="form.baseSalary" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出勤天数" prop="attendanceDays">
              <el-input-number v-model="form.attendanceDays" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="满勤奖" prop="attendanceBonus">
              <el-input-number v-model="form.attendanceBonus" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课时费" prop="courseBonus">
              <el-input-number v-model="form.courseBonus" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="SalaryManage">
import { 
  listTeacherSalary, 
  getTeacherSalary, 
  addTeacherSalary, 
  updateTeacherSalary, 
  delTeacherSalary,
  batchConfirmSalary,
  batchPaySalary,
  getSalarySummary
} from "@/api/kg/salary/manage";
import { listAllTeacher } from "@/api/kg/teacher/info";

export default {
  name: "SalaryManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工资表格数据
      salaryList: [],
      // 教师选项
      teacherOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情对话框
      detailDialogVisible: false,
      // 工资详情
      salaryDetail: {},
      // 查询日期
      queryDate: '',
      // 统计数据
      statistics: {
        totalSalaries: 0,
        totalGrossAmount: 0,
        totalNetAmount: 0,
        paidCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teacherId: null,
        salaryYear: null,
        salaryMonth: null,
        salaryStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        teacherId: [
          { required: true, message: "教师不能为空", trigger: "change" }
        ],
        salaryMonth: [
          { required: true, message: "工资年月不能为空", trigger: "change" }
        ],
        baseSalary: [
          { required: true, message: "基本工资不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadTeacherOptions();
    this.loadStatistics();
  },
  methods: {
    /** 查询工资列表 */
    getList() {
      this.loading = true;
      listTeacherSalary(this.queryParams).then(response => {
        this.salaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 加载教师选项 */
    loadTeacherOptions() {
      listAllTeacher().then(response => {
        this.teacherOptions = response.data || [];
      });
    },
    /** 加载统计数据 */
    loadStatistics() {
      getSalarySummary(this.queryParams).then(response => {
        this.statistics = response.data || this.statistics;
      });
    },
    /** 日期变化处理 */
    handleDateChange(date) {
      if (date) {
        const [year, month] = date.split('-');
        this.queryParams.salaryYear = parseInt(year);
        this.queryParams.salaryMonth = parseInt(month);
      } else {
        this.queryParams.salaryYear = null;
        this.queryParams.salaryMonth = null;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.loadStatistics();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryDate = '';
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        teacherId: null,
        salaryYear: null,
        salaryMonth: null,
        salaryStatus: null
      };
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.salaryId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工资记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const salaryId = row.salaryId || this.ids;
      getTeacherSalary(salaryId).then(response => {
        this.form = response.data;
        this.form.salaryMonth = this.form.salaryYear + '-' + String(this.form.salaryMonth).padStart(2, '0');
        this.open = true;
        this.title = "修改工资记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["salaryRef"].validate(valid => {
        if (valid) {
          // 处理日期
          if (this.form.salaryMonth) {
            const [year, month] = this.form.salaryMonth.split('-');
            this.form.salaryYear = parseInt(year);
            this.form.salaryMonth = parseInt(month);
          }
          
          if (this.form.salaryId != null) {
            updateTeacherSalary(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTeacherSalary(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        salaryId: null,
        teacherId: null,
        salaryYear: null,
        salaryMonth: null,
        baseSalary: 0,
        attendanceDays: 0,
        attendanceBonus: 0,
        courseBonus: 0,
        enrollmentBonus: 0,
        attendanceRateBonus: 0,
        newStudentBonus: 0,
        withdrawalPenalty: 0,
        socialInsurance: 0,
        otherBonus: 0,
        otherDeduction: 0,
        performanceScore: 0,
        grossSalary: 0,
        netSalary: 0,
        salaryStatus: 'calculated',
        remark: null
      };
    },
    /** 查看详情 */
    handleViewDetail(row) {
      getTeacherSalary(row.salaryId).then(response => {
        this.salaryDetail = response.data;
        this.detailDialogVisible = true;
      });
    },
    /** 确认工资 */
    handleConfirm(row) {
      this.$confirm(`确认教师"${row.teacherName}"的工资记录吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return batchConfirmSalary([row.salaryId]);
      }).then(() => {
        this.getList();
        this.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 发放工资 */
    handlePay(row) {
      this.$confirm(`确认发放教师"${row.teacherName}"的工资吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return batchPaySalary([row.salaryId]);
      }).then(() => {
        this.getList();
        this.msgSuccess("发放成功");
      }).catch(() => {});
    },
    /** 批量确认 */
    handleBatchConfirm() {
      const salaryIds = this.ids;
      this.$confirm(`确认批量确认选中的 ${salaryIds.length} 条工资记录吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return batchConfirmSalary(salaryIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("批量确认成功");
      }).catch(() => {});
    },
    /** 批量发放 */
    handleBatchPay() {
      const salaryIds = this.ids;
      this.$confirm(`确认批量发放选中的 ${salaryIds.length} 条工资吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return batchPaySalary(salaryIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("批量发放成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const salaryIds = row.salaryId || this.ids;
      this.$confirm('是否确认删除工资编号为"' + salaryIds + '"的数据项？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return delTeacherSalary(salaryIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/salary/export', {
        ...this.queryParams
      }, `teacher_salary_${new Date().getTime()}.xlsx`);
    },
    /** 获取工资状态类型 */
    getSalaryStatusType(status) {
      const statusMap = {
        'calculated': 'info',
        'confirmed': 'warning',
        'paid': 'success'
      };
      return statusMap[status] || 'info';
    },
    /** 获取工资状态文本 */
    getSalaryStatusText(status) {
      const statusMap = {
        'calculated': '已计算',
        'confirmed': '已确认',
        'paid': '已发放'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.text-price {
  color: #E6A23C;
  font-weight: bold;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}</style>
