import request from '@/utils/request'

// 查询教师课时统计列表
export function listTeacherStats(query) {
  return request({
    url: '/business/teacher-course-stats/list',
    method: 'get',
    params: query
  })
}

// 查询教师课时统计详细
export function getTeacherStats(statsId) {
  return request({
    url: '/business/teacher-course-stats/' + statsId,
    method: 'get'
  })
}

// 计算教师课时统计
export function calculateTeacherStats(data) {
  return request({
    url: '/business/teacher-course-stats/calculate',
    method: 'post',
    data: data
  })
}

// 重新计算教师课时统计
export function recalculateTeacherStats(data) {
  return request({
    url: '/business/teacher-course-stats/recalculate',
    method: 'post',
    data: data
  })
}

// 获取教师课时统计汇总
export function getTeacherStatsSummary(query) {
  return request({
    url: '/business/teacher-course-stats/summary',
    method: 'get',
    params: query
  })
}

// 导出教师课时统计
export function exportTeacherStats(query) {
  return request({
    url: '/business/teacher-course-stats/export',
    method: 'get',
    params: query
  })
}

// 获取教师详细授课记录
export function getTeacherCourseDetails(teacherId, query) {
  return request({
    url: '/business/teacher-course-stats/details/' + teacherId,
    method: 'get',
    params: query
  })
}

// 批量计算教师课时统计
export function batchCalculateTeacherStats(data) {
  return request({
    url: '/business/teacher-course-stats/batchCalculate',
    method: 'post',
    data: data
  })
}

// 确认教师课时统计
export function confirmTeacherStats(statsIds) {
  return request({
    url: '/business/teacher-course-stats/confirm',
    method: 'post',
    data: statsIds
  })
}

// 删除教师课时统计
export function delTeacherStats(statsIds) {
  return request({
    url: '/business/teacher-course-stats/' + statsIds,
    method: 'delete'
  })
}
