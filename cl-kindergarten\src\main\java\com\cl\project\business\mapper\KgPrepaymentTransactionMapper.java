package com.cl.project.business.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.cl.project.business.domain.KgPrepaymentTransaction;

/**
 * 预交款流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface KgPrepaymentTransactionMapper 
{
    /**
     * 查询预交款流水
     * 
     * @param transactionId 预交款流水ID
     * @return 预交款流水
     */
    public KgPrepaymentTransaction selectKgPrepaymentTransactionById(Long transactionId);

    /**
     * 查询预交款流水列表
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 预交款流水集合
     */
    public List<KgPrepaymentTransaction> selectKgPrepaymentTransactionList(KgPrepaymentTransaction kgPrepaymentTransaction);

    /**
     * 新增预交款流水
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 结果
     */
    public int insertKgPrepaymentTransaction(KgPrepaymentTransaction kgPrepaymentTransaction);

    /**
     * 修改预交款流水
     * 
     * @param kgPrepaymentTransaction 预交款流水
     * @return 结果
     */
    public int updateKgPrepaymentTransaction(KgPrepaymentTransaction kgPrepaymentTransaction);

    /**
     * 删除预交款流水
     * 
     * @param transactionId 预交款流水ID
     * @return 结果
     */
    public int deleteKgPrepaymentTransactionById(Long transactionId);

    /**
     * 批量删除预交款流水
     * 
     * @param transactionIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgPrepaymentTransactionByIds(Long[] transactionIds);

    /**
     * 获取流水统计数据
     * 
     * @param comId 公司ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据
     */
    public Map<String, Object> getTransactionStatistics(@Param("comId") Long comId, 
                                                        @Param("startTime") String startTime, 
                                                        @Param("endTime") String endTime);
}
