<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程类型" prop="courseType">
        <el-select v-model="queryParams.courseType" placeholder="请选择课程类型" clearable size="small">
          <el-option label="托管课程" value="1" />
          <el-option label="兴趣班" value="2" />
          <el-option label="特色课程" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="授课教师" prop="teacherId">
  <el-select v-model="queryParams.teacherId" placeholder="请选择授课教师" clearable size="small">
    <el-option v-for="item in teacherList" :key="item.teacherId" :label="item.teacherName" :value="item.teacherId" />
  </el-select>
</el-form-item>
      <el-form-item label="课程状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择课程状态" clearable size="small">
          <el-option label="启用" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['kg:course:manage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['kg:course:manage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['kg:course:manage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程编号" align="center" prop="courseId" />
      <el-table-column label="课程名称" align="center" prop="courseName" />
      <el-table-column label="课程类型" align="center" prop="courseType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_course_type" :value="scope.row.courseType"/>
        </template>
      </el-table-column>
      <el-table-column label="授课教师" align="center" prop="defaultTeacherId">
        <template slot-scope="scope">
          {{ (teacherList.find(t => t.teacherId === scope.row.defaultTeacherId) || {}).teacherName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="单节课价格" align="center" prop="pricePerSession" />
      
      <el-table-column label="课程时长" align="center" prop="duration" />
      <el-table-column label="最大人数" align="center" prop="maxStudents" />
      
      <el-table-column label="课程状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['kg:course:manage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['kg:course:manage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改课程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="courseName">
              <el-input v-model="form.courseName" placeholder="请输入课程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程类型" prop="courseType">
              <el-select v-model="form.courseType" placeholder="请选择课程类型">
                <el-option label="托管课程" value="1" />
                <el-option label="兴趣班" value="2" />
                <el-option label="特色课程" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="授课教师" prop="defaultTeacherId">
  <el-select v-model="form.defaultTeacherId" placeholder="请选择授课教师" filterable>
    <el-option v-for="item in teacherList" :key="item.teacherId" :label="item.teacherName" :value="item.teacherId" />
  </el-select>
</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单节课价格" prop="pricePerSession">
  <el-input v-model="form.pricePerSession" placeholder="请输入单节课价格" />
</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程时长" prop="duration">
              <el-input v-model="form.duration" placeholder="请输入课程时长(分钟)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
  <el-col :span="12">
    <el-form-item label="最少人数" prop="minStudents">
      <el-input v-model="form.minStudents" placeholder="请输入最少开班人数" />
    </el-form-item>
  </el-col>
  <el-col :span="12">
    <el-form-item label="最大人数" prop="maxStudents">
      <el-input v-model="form.maxStudents" placeholder="请输入最大人数" />
    </el-form-item>
  </el-col>
</el-row>
<el-row>
  <el-col :span="12">
    <el-form-item label="课程状态" prop="status">
      <el-radio-group v-model="form.status">
        <el-radio label="0">启用</el-radio>
        <el-radio label="1">停用</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-col>
</el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCourse, getCourse, delCourse, addCourse, updateCourse, exportCourse } from "@/api/kg/course/manage";
import { listTeacher } from "@/api/kg/teacher/info";

export default {
  name: "CourseManage",
  dicts: ['kg_course_type', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 课程表格数据
      courseList: [],
      // 教师列表
      teacherList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseName: undefined,
        courseType: undefined,
        teacherId: undefined,
        status: undefined,
      },
      // 表单参数
      form: {
  courseId: undefined,
  courseName: undefined,
  courseType: undefined,
  teacherId: undefined,
  pricePerSession: undefined,
  duration: undefined,
  minStudents: undefined,
  maxStudents: undefined,
  status: '0',
  remark: undefined
},
      // 表单校验
      rules: {
        courseName: [
          { required: true, message: "课程名称不能为空", trigger: "blur" }
        ],
        courseType: [
          { required: true, message: "课程类型不能为空", trigger: "change" }
        ],
        teacherId: [
          { required: true, message: "授课教师不能为空", trigger: "change" }
        ],
        pricePerSession: [
          { required: true, message: "单节课价格不能为空", trigger: "blur" }
        ],
        minStudents: [
          { required: true, message: "最少开班人数不能为空", trigger: "blur" }
        ],
        maxStudents: [
          { required: true, message: "最大人数不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTeacherList();
  },
  methods: {
    /** 查询课程列表 */
    getList() {
      this.loading = true;
      listCourse(this.queryParams).then(response => {
        this.courseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询教师列表 */
    getTeacherList() {
      listTeacher().then(response => {
        this.teacherList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        courseId: undefined,
        courseName: undefined,
        courseType: undefined,
        teacherId: undefined,
        pricePerSession: undefined,
        duration: undefined,
        maxStudents: undefined,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.courseId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课程";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const courseId = row.courseId || this.ids
      getCourse(courseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改课程";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.courseId != undefined) {
            updateCourse(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addCourse(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const courseIds = row.courseId || this.ids;
      this.$confirm('是否确认删除课程编号为"' + courseIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delCourse(courseIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有课程数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportCourse(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>
