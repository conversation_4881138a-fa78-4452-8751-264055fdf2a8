package com.cl.project.business.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.cl.project.business.domain.KgPrepaymentAccount;
import com.cl.project.business.dto.PrepaymentBalanceDto;
import com.cl.project.business.dto.PrepaymentStatisticsDto;

/**
 * 预交款账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface KgPrepaymentAccountMapper 
{
    /**
     * 查询预交款账户
     * 
     * @param accountId 预交款账户ID
     * @return 预交款账户
     */
    public KgPrepaymentAccount selectKgPrepaymentAccountById(Long accountId);

    /**
     * 查询预交款账户列表
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 预交款账户集合
     */
    public List<KgPrepaymentAccount> selectKgPrepaymentAccountList(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 新增预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    public int insertKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 修改预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    public int updateKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount);

    /**
     * 删除预交款账户
     * 
     * @param accountId 预交款账户ID
     * @return 结果
     */
    public int deleteKgPrepaymentAccountById(Long accountId);

    /**
     * 批量删除预交款账户
     * 
     * @param accountIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgPrepaymentAccountByIds(Long[] accountIds);
    
    // ========== 业务查询方法 ==========

    /**
     * 查询预交款余额列表
     * 
     * @param queryParams 查询参数
     * @return 预交款余额列表
     */
    public List<PrepaymentBalanceDto> selectBalanceList(PrepaymentBalanceDto queryParams);

    /**
     * 获取预交款余额统计
     * 
     * @param comId 公司ID
     * @return 统计数据
     */
    public PrepaymentStatisticsDto getBalanceStatistics(@Param("comId") String comId);

    /**
     * 获取学生预交款余额详情
     * 
     * @param studentId 学生ID
     * @return 余额详情
     */
    public PrepaymentBalanceDto getStudentBalanceDetail(Long studentId);
}
