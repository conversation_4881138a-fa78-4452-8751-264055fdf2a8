package com.cl.project.business.domain.dto;

import javax.validation.constraints.NotNull;

/**
 * 学生签到并确认DTO
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class StudentCheckinAndConfirmDto {
    
    /** 学生ID */
    @NotNull(message = "学生ID不能为空")
    private Long studentId;
    
    /** 考勤日期 */
    @NotNull(message = "考勤日期不能为空")
    private String attendanceDate;
    
    /** 考勤状态 (1出勤 2迟到 3缺勤 4请假 5早退 6病假 8休假) */
    private String attendanceStatus = "1"; // 默认出勤
    
    /** 备注 */
    private String remark;

    // Getter and Setter methods
    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "StudentCheckinAndConfirmDto{" +
                "studentId=" + studentId +
                ", attendanceDate='" + attendanceDate + '\'' +
                ", attendanceStatus='" + attendanceStatus + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
