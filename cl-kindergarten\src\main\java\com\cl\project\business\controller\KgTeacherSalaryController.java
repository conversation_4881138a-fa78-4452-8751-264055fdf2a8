package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTeacherSalary;
import com.cl.project.business.service.IKgTeacherSalaryService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 教师工资Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/salary")
public class KgTeacherSalaryController extends BaseController
{
    @Autowired
    private IKgTeacherSalaryService kgTeacherSalaryService;

    /**
     * 查询教师工资列表
     */
//    @SaCheckPermission("kg:salary:query:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTeacherSalary kgTeacherSalary)
    {
        startPage();
        List<KgTeacherSalary> list = kgTeacherSalaryService.selectKgTeacherSalaryList(kgTeacherSalary);
        return getDataTable(list);
    }

    /**
     * 导出教师工资列表
     */
    @SaCheckPermission("kg:salary:query:list")
    @Log(title = "教师工资", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTeacherSalary kgTeacherSalary)
    {
        List<KgTeacherSalary> list = kgTeacherSalaryService.selectKgTeacherSalaryList(kgTeacherSalary);
        ExcelUtil<KgTeacherSalary> util = new ExcelUtil<KgTeacherSalary>(KgTeacherSalary.class);
        return util.exportExcel(list, "salary");
    }

    /**
     * 获取教师工资详细信息
     */
    @SaCheckPermission("kg:salary:query:list")
    @GetMapping(value = "/{salaryId}")
    public AjaxResult getInfo(@PathVariable("salaryId") Long salaryId)
    {
        return AjaxResult.success(kgTeacherSalaryService.selectKgTeacherSalaryById(salaryId));
    }

    /**
     * 新增教师工资
     */
    @SaCheckPermission("kg:salary:calculate:confirm")
    @Log(title = "教师工资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTeacherSalary kgTeacherSalary)
    {
        return toAjax(kgTeacherSalaryService.insertKgTeacherSalary(kgTeacherSalary));
    }

    /**
     * 修改教师工资
     */
    @SaCheckPermission("kg:salary:calculate:confirm")
    @Log(title = "教师工资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTeacherSalary kgTeacherSalary)
    {
        return toAjax(kgTeacherSalaryService.updateKgTeacherSalary(kgTeacherSalary));
    }

    /**
     * 删除教师工资
     */
    @SaCheckPermission("kg:salary:calculate:pay")
    @Log(title = "教师工资", businessType = BusinessType.DELETE)
	@DeleteMapping("/{salaryIds}")
    public AjaxResult remove(@PathVariable Long[] salaryIds)
    {
        return toAjax(kgTeacherSalaryService.deleteKgTeacherSalaryByIds(salaryIds));
    }
}
