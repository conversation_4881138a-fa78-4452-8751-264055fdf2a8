package com.cl.project.business.service.impl;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.domain.*;
import com.cl.project.business.mapper.*;
import com.cl.project.business.service.IKgTuitionCalculationService;
import com.cl.project.business.service.IKgAttendanceStatisticsService;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;

/**
 * 园费计算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class KgTuitionCalculationServiceImpl implements IKgTuitionCalculationService 
{
    @Autowired
    private KgStudentMapper studentMapper;
    
    @Autowired
    private KgTuitionConfigMapper tuitionConfigMapper;
    
    @Autowired
    private KgTuitionBillMapper tuitionBillMapper;
    
    @Autowired
    private KgClassMapper classMapper;
    
    @Autowired
    private IKgAttendanceStatisticsService attendanceStatisticsService;

    /**
     * 计算学生月度园费
     */
    @Override
    public Map<String, Object> calculateMonthlyTuition(Long studentId, Integer year, Integer month) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取学生信息
        KgStudent student = studentMapper.selectKgStudentById(studentId);
        if (student == null) {
            throw new RuntimeException("学生不存在");
        }
        
        // 获取班级费用配置
        KgTuitionConfig config = getTuitionConfig(student.getClassId());
        if (config == null) {
            throw new RuntimeException("未找到班级费用配置");
        }
        
        // 计算考勤统计
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        Map<String, Object> attendanceStats = attendanceStatisticsService.getStudentAttendanceStatistics(
                student.getClassId(), studentId, startDate, endDate);
        
        Integer attendanceDays = (Integer) attendanceStats.get("attendanceDays");
        Double attendanceRate = (Double) attendanceStats.get("attendanceRate");
        
        // 计算各项费用
        Double mealFeeDouble = calculateMealFee(studentId, attendanceDays, config.getMealFeePerDay().doubleValue());
        Double educationFeeDouble = calculateEducationFee(studentId, attendanceRate, config.getEducationFeePerMonth().doubleValue());
        BigDecimal mealFee = BigDecimal.valueOf(mealFeeDouble);
        BigDecimal educationFee = BigDecimal.valueOf(educationFeeDouble);
        // 园费只包含餐费和保教费，不包含管理费
        
        // 计算总费用
        BigDecimal totalFee = mealFee.add(educationFee);
        
        // 计算余额结转
        String currentMonth = String.format("%d-%02d", year, month);
        Double balanceCarryoverDouble = calculateBalanceCarryover(studentId, currentMonth);
        BigDecimal balanceCarryover = BigDecimal.valueOf(balanceCarryoverDouble);
        
        // 计算实际应缴费用
        BigDecimal actualPayable = totalFee.subtract(balanceCarryover);
        
        // 计算下月预交金额
        String nextMonth = YearMonth.of(year, month).plusMonths(1).toString();
        Double advancePaymentDouble = calculateAdvancePayment(studentId, nextMonth);
        BigDecimal advancePayment = BigDecimal.valueOf(advancePaymentDouble);
        
        // 组装结果
        result.put("studentInfo", student);
        result.put("attendanceStats", attendanceStats);
        Map<String, Object> feeBreakdown = new HashMap<>();
        feeBreakdown.put("mealFee", mealFee);
        feeBreakdown.put("educationFee", educationFee);
        // 园费不包含管理费
        feeBreakdown.put("totalFee", totalFee);
        feeBreakdown.put("balanceCarryover", balanceCarryover);
        feeBreakdown.put("actualPayable", actualPayable);
        feeBreakdown.put("advancePayment", advancePayment);
        result.put("feeBreakdown", feeBreakdown);
        result.put("config", config);
        result.put("calculationDate", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 批量计算班级园费
     */
    @Override
    public List<Map<String, Object>> calculateClassTuitionBatch(Long classId, Integer year, Integer month) 
    {
        // 获取班级学生列表
        KgStudent queryParams = new KgStudent();
        queryParams.setClassId(classId);
        queryParams.setStatus("0"); // 只计算在读学生
        List<KgStudent> students = studentMapper.selectKgStudentList(queryParams);
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        for (KgStudent student : students) {
            try {
                Map<String, Object> calculation = calculateMonthlyTuition(student.getStudentId(), year, month);
                results.add(calculation);
            } catch (Exception e) {
                // 记录错误但继续处理其他学生
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("studentInfo", student);
                errorResult.put("error", e.getMessage());
                results.add(errorResult);
            }
        }
        
        return results;
    }

    /**
     * 批量计算全园园费
     */
    @Override
    public Map<String, Object> calculateAllTuitionBatch(Integer year, Integer month) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有班级
        List<KgClass> classes = classMapper.selectKgClassList(new KgClass());
        
        List<Map<String, Object>> classResults = new ArrayList<>();
        int totalStudents = 0;
        double totalAmount = 0;
        int successCount = 0;
        int errorCount = 0;
        
        for (KgClass classInfo : classes) {
            Map<String, Object> classResult = new HashMap<>();
            classResult.put("classInfo", classInfo);
            
            List<Map<String, Object>> studentResults = calculateClassTuitionBatch(classInfo.getClassId(), year, month);
            classResult.put("studentResults", studentResults);
            
            // 统计班级汇总
            int classStudentCount = studentResults.size();
            double classTotal = 0;
            int classSuccess = 0;
            int classError = 0;
            
            for (Map<String, Object> studentResult : studentResults) {
                if (studentResult.containsKey("error")) {
                    classError++;
                } else {
                    classSuccess++;
                    Map<String, Object> feeBreakdown = (Map<String, Object>) studentResult.get("feeBreakdown");
                    classTotal += ((BigDecimal) feeBreakdown.get("actualPayable")).doubleValue();
                }
            }
            
            Map<String, Object> classSummary = new HashMap<>();
            classSummary.put("studentCount", classStudentCount);
            classSummary.put("successCount", classSuccess);
            classSummary.put("errorCount", classError);
            classSummary.put("totalAmount", classTotal);
            classResult.put("summary", classSummary);
            
            classResults.add(classResult);
            
            // 累计全园统计
            totalStudents += classStudentCount;
            totalAmount += classTotal;
            successCount += classSuccess;
            errorCount += classError;
        }
        
        result.put("year", year);
        result.put("month", month);
        result.put("classResults", classResults);
        Map<String, Object> overallSummary = new HashMap<>();
        overallSummary.put("totalClasses", classes.size());
        overallSummary.put("totalStudents", totalStudents);
        overallSummary.put("successCount", successCount);
        overallSummary.put("errorCount", errorCount);
        overallSummary.put("totalAmount", totalAmount);
        result.put("overallSummary", overallSummary);
        result.put("calculationTime", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 预览园费计算结果
     */
    @Override
    public Map<String, Object> previewTuitionCalculation(Long studentId, Integer year, Integer month) 
    {
        // 预览与实际计算逻辑相同，但不保存数据
        return calculateMonthlyTuition(studentId, year, month);
    }

    /**
     * 获取园费计算规则
     */
    @Override
    public Map<String, Object> getTuitionCalculationRules(Long classId) 
    {
        Map<String, Object> rules = new HashMap<>();
        
        KgTuitionConfig config = getTuitionConfig(classId);
        if (config == null) {
            throw new RuntimeException("未找到班级费用配置");
        }
        
        rules.put("config", config);
        Map<String, String> calculationRules = new HashMap<>();
        calculationRules.put("mealFeeRule", "餐费 = 出勤天数 × 餐费单价(" + config.getMealFeePerDay() + "元/天)");
        calculationRules.put("educationFeeRule", "保教费 = 出勤率 ≥ 50% ? 全额(" + config.getEducationFeePerMonth() + "元) : 半额(" + (config.getEducationFeePerMonth().divide(BigDecimal.valueOf(2), 2, java.math.RoundingMode.HALF_UP)) + "元)");
        calculationRules.put("attendanceRateRule", "出勤率 = 出勤天数 / 应出勤天数 × 100%");
        calculationRules.put("totalFeeRule", "园费总额 = 餐费 + 保教费（不含管理费）");
        rules.put("calculationRules", calculationRules);
        
        return rules;
    }

    /**
     * 生成园费账单
     */
    @Override
    @Transactional
    public int generateTuitionBills(List<Map<String, Object>> calculations) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Map<String, Object> calculation : calculations) {
            if (calculation.containsKey("error")) {
                continue; // 跳过计算错误的记录
            }
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> feeBreakdown = objectMapper.convertValue(calculation.get("feeBreakdown"),Map.class) ;
//            Map<String, Object> feeBreakdown = (Map<String, Object>) calculation.get("feeBreakdown");
//            KgStudent student = (KgStudent) calculation.get("studentInfo");
        KgStudent student = objectMapper.convertValue(calculation.get("studentInfo"), KgStudent.class);
        Map<String, Object> attendanceStats = objectMapper.convertValue(calculation.get("attendanceStats"), Map.class);

        // 创建账单记录
        KgTuitionBill bill = new KgTuitionBill();
        bill.setStudentId(student.getStudentId());
        bill.setClassId(student.getClassId());

        String billMonthStr = (String) calculation.get("billMonth"); // "2025-08"
        if (billMonthStr != null && billMonthStr.contains("-")) {
            String[] parts = billMonthStr.split("-");
            bill.setBillYear(Long.valueOf(parts[0]));
            bill.setBillMonth(Long.valueOf(parts[1]));
        }
        
        // 修复：保存到正确的数据库字段，与回显字段一致
        bill.setMealFeeUsed(toBigDecimal(feeBreakdown.get("mealFee")));
        bill.setEducationFeeUsed(toBigDecimal(feeBreakdown.get("educationFee")));
        bill.setManagementFee(toBigDecimal(feeBreakdown.get("managementFee")));
        bill.setTotalBalance(toBigDecimal(feeBreakdown.get("balanceCarryover")));
        bill.setActualPayment(toBigDecimal(feeBreakdown.get("actualPayable")));
        bill.setNextMonthPrepay(toBigDecimal(feeBreakdown.get("advancePayment")));
        bill.setAttendanceRate(toBigDecimal(attendanceStats.get("attendanceRate")));
        bill.setAttendanceDays((Integer) attendanceStats.get("attendanceDays"));

        bill.setBillStatus("generated");
        bill.setCreateBy(currentUser);
        bill.setCreateTime(DateUtils.getNowDate());

        KgTuitionBill existBill = tuitionBillMapper.selectByStudentAndMonth(
            bill.getStudentId(), bill.getBillYear(), bill.getBillMonth());

        if (existBill != null) {
            // 如果已支付，跳过
            if ("paid".equals(existBill.getBillStatus())) {
                continue;
            }
            // 未支付，先删再插
            tuitionBillMapper.deleteByStudentAndMonth(
                bill.getStudentId(), bill.getBillYear(), bill.getBillMonth());
        }
        // 然后插入新账单
        tuitionBillMapper.insertKgTuitionBill(bill);
        
        count++;
    }
    
    return count;
}

    /**
     * 重新计算园费
     */
    @Override
    @Transactional
    public Map<String, Object> recalculateTuition(Long billId, String reason) 
    {
        // 获取原账单
        KgTuitionBill originalBill = tuitionBillMapper.selectKgTuitionBillById(billId);
        if (originalBill == null) {
            throw new RuntimeException("账单不存在");
        }
        
        // 解析账单月份 - billMonth是Long类型，格式如202501表示2025年1月
        Long billMonth = originalBill.getBillMonth();
        Integer year = (int) (billMonth / 100);
        Integer month = (int) (billMonth % 100);
        
        // 重新计算
        Map<String, Object> newCalculation = calculateMonthlyTuition(originalBill.getStudentId(), year, month);
        Map<String, Object> feeBreakdown = (Map<String, Object>) newCalculation.get("feeBreakdown");
        Map<String, Object> attendanceStats = (Map<String, Object>) newCalculation.get("attendanceStats");
        
        // 更新账单
        originalBill.setMealFee((BigDecimal) feeBreakdown.get("mealFee"));
        originalBill.setEducationFee((BigDecimal) feeBreakdown.get("educationFee"));
        originalBill.setManagementFee((BigDecimal) feeBreakdown.get("managementFee"));
        originalBill.setTotalFee((BigDecimal) feeBreakdown.get("totalFee"));
        originalBill.setBalanceCarryover((BigDecimal) feeBreakdown.get("balanceCarryover"));
        originalBill.setActualPayable((BigDecimal) feeBreakdown.get("actualPayable"));
        originalBill.setAdvancePayment((BigDecimal) feeBreakdown.get("advancePayment"));
        originalBill.setAttendanceDays((Integer) attendanceStats.get("attendanceDays"));
        originalBill.setAttendanceRate((BigDecimal) attendanceStats.get("attendanceRate"));
        originalBill.setRemark("重新计算：" + (reason != null ? reason : "系统重算"));
        originalBill.setUpdateBy(SecurityUtils.getUsername());
        originalBill.setUpdateTime(DateUtils.getNowDate());
        
        tuitionBillMapper.updateKgTuitionBill(originalBill);
        
        Map<String, Object> result = new HashMap<>();
        result.put("originalBill", originalBill);
        result.put("newCalculation", newCalculation);
        result.put("recalculationTime", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 获取费用明细
     */
    @Override
    public Map<String, Object> getFeeDetails(Long studentId, Integer year, Integer month) 
    {
        return calculateMonthlyTuition(studentId, year, month);
    }

    /**
     * 费用调整
     */
    @Override
    @Transactional
    public int adjustFee(Long billId, String feeType, Double adjustAmount, String reason) 
    {
        KgTuitionBill bill = tuitionBillMapper.selectKgTuitionBillById(billId);
        if (bill == null) {
            throw new RuntimeException("账单不存在");
        }
        
        BigDecimal adjustment = new BigDecimal(adjustAmount);
        
        switch (feeType) {
        case "meal":
            BigDecimal currentMealFeeUsed = toBigDecimal(bill.getMealFeeUsed());
            bill.setMealFeeUsed(currentMealFeeUsed.add(adjustment));
            break;
        case "education":
            BigDecimal currentEducationFeeUsed = toBigDecimal(bill.getEducationFeeUsed());
            bill.setEducationFeeUsed(currentEducationFeeUsed.add(adjustment));
            break;
        case "management":
            // 注意：管理费可能没有 Used 字段，使用原有的 managementFee
            BigDecimal currentManagementFee = toBigDecimal(bill.getManagementFee());
            bill.setManagementFee(currentManagementFee.add(adjustment));
            break;
        default:
            throw new RuntimeException("不支持的费用类型");
    }
        
        // 重新计算总费用和应缴费用（使用正确的数据库字段）
        BigDecimal mealFeeUsed = toBigDecimal(bill.getMealFeeUsed());
        BigDecimal educationFeeUsed = toBigDecimal(bill.getEducationFeeUsed());
        BigDecimal managementFee = toBigDecimal(bill.getManagementFee());
        BigDecimal totalBalance = toBigDecimal(bill.getTotalBalance());
        
        // 计算实际应缴金额（已用费用 - 余额结转）
        BigDecimal totalUsedFee = mealFeeUsed.add(educationFeeUsed).add(managementFee);
        BigDecimal actualPayment = totalUsedFee.subtract(totalBalance);
        bill.setActualPayment(actualPayment);
        
        bill.setRemark("费用调整：" + feeType + " " + (adjustAmount > 0 ? "+" : "") + adjustAmount + "元，原因：" + reason);
        bill.setUpdateBy(SecurityUtils.getUsername());
        bill.setUpdateTime(DateUtils.getNowDate());
        
        return tuitionBillMapper.updateKgTuitionBill(bill);
    }

    /**
     * 获取费用统计
     */
    @Override
    public Map<String, Object> getTuitionStatistics(Integer year, Integer month, Long classId) 
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 构建查询条件
        KgTuitionBill queryParams = new KgTuitionBill();
        queryParams.setBillMonth(String.format("%d-%02d", year, month));
        if (classId != null) {
            queryParams.setClassId(classId);
        }
        
        List<KgTuitionBill> bills = tuitionBillMapper.selectKgTuitionBillList(queryParams);
        
        if (bills.isEmpty()) {
            statistics.put("totalBills", 0);
            statistics.put("totalAmount", 0.0);
            return statistics;
        }
        
        // 统计汇总
        double totalMealFee = bills.stream().mapToDouble(bill -> bill.getMealFee().doubleValue()).sum();
        double totalEducationFee = bills.stream().mapToDouble(bill -> bill.getEducationFee().doubleValue()).sum();
        double totalManagementFee = bills.stream().mapToDouble(bill -> bill.getManagementFee().doubleValue()).sum();
        double totalAmount = bills.stream().mapToDouble(bill -> bill.getTotalFee().doubleValue()).sum();
        double totalPayable = bills.stream().mapToDouble(bill -> bill.getActualPayable().doubleValue()).sum();
        
        // 按状态分组统计
        Map<String, Long> statusCount = bills.stream()
                .collect(Collectors.groupingBy(
                    bill -> bill.getBillStatus(),
                    Collectors.counting()
                ));
        
        statistics.put("totalBills", bills.size());
        Map<String, Object> feeBreakdownStats = new HashMap<>();
        feeBreakdownStats.put("totalMealFee", totalMealFee);
        feeBreakdownStats.put("totalEducationFee", totalEducationFee);
        feeBreakdownStats.put("totalManagementFee", totalManagementFee);
        feeBreakdownStats.put("totalAmount", totalAmount);
        feeBreakdownStats.put("totalPayable", totalPayable);
        statistics.put("feeBreakdown", feeBreakdownStats);
        statistics.put("statusStatistics", statusCount);
        statistics.put("averageAmount", totalAmount / bills.size());
        
        return statistics;
    }

    /**
     * 计算餐费
     */
    @Override
    public Double calculateMealFee(Long studentId, Integer attendanceDays, Double mealPrice) 
    {
        return attendanceDays * mealPrice;
    }

    /**
     * 计算保教费
     */
    @Override
    public Double calculateEducationFee(Long studentId, Double attendanceRate, Double baseFee) 
    {
        // 出勤率达到50%收取全额，否则收取半额
        return attendanceRate >= 50.0 ? baseFee : baseFee / 2;
    }

    /**
     * 计算余额结转
     */
    @Override
    public Double calculateBalanceCarryover(Long studentId, String currentMonth) 
    {
        // 查询上月账单余额
        // 这里简化处理，实际应该查询上月账单的余额
        return 0.0;
    }

    /**
     * 计算下月预交金额
     */
    @Override
    public Double calculateAdvancePayment(Long studentId, String nextMonth) 
    {
        // 根据历史平均费用计算预交金额
        // 这里简化处理，可以设置为固定金额或根据历史数据计算
        return 1000.0;
    }

    /**
     * 获取班级费用配置
     */
    private KgTuitionConfig getTuitionConfig(Long classId) 
    {
        // 根据班级ID获取班级信息，然后根据班级类型查询费用配置
        KgClass classInfo = classMapper.selectKgClassById(classId);
        if (classInfo == null) {
            return null;
        }
        
        KgTuitionConfig queryParams = new KgTuitionConfig();
        queryParams.setClassType(classInfo.getClassType());
        List<KgTuitionConfig> configs = tuitionConfigMapper.selectKgTuitionConfigList(queryParams);
        
        return configs.isEmpty() ? null : configs.get(0);
    }

    private BigDecimal toBigDecimal(Object val) {
        if (val == null) return BigDecimal.ZERO;
        if (val instanceof BigDecimal) return (BigDecimal) val;
        if (val instanceof Integer) return BigDecimal.valueOf((Integer) val);
        if (val instanceof Long) return BigDecimal.valueOf((Long) val);
        if (val instanceof Double) return BigDecimal.valueOf((Double) val);
        return new BigDecimal(val.toString());
    }

    /**
     * 回显账单数据为计算结果格式
     * 保持与计算结果相同的数据结构
     */
    @Override
    public List<Map<String, Object>> recallTuitionCalculationResults(String calculationType, 
                                                                   Long studentId, 
                                                                   Long classId, 
                                                                   Integer year, 
                                                                   Integer month) {
        List<KgTuitionBill> bills = new ArrayList<>();
        
        // 根据计算类型查询账单
        if ("single".equals(calculationType) && studentId != null) {
            // 单个学生
            KgTuitionBill queryBill = new KgTuitionBill();
            queryBill.setStudentId(studentId);
            queryBill.setBillYear(Long.valueOf(year));
            queryBill.setBillMonth(Long.valueOf(month));
            bills = tuitionBillMapper.selectKgTuitionBillList(queryBill);
        } else if ("class".equals(calculationType) && classId != null) {
            // 班级批量
            KgTuitionBill queryBill = new KgTuitionBill();
            queryBill.setClassId(classId);
            queryBill.setBillYear(Long.valueOf(year));
            queryBill.setBillMonth(Long.valueOf(month));
            bills = tuitionBillMapper.selectKgTuitionBillList(queryBill);
        } else if ("all".equals(calculationType)) {
            // 全园批量
            KgTuitionBill queryBill = new KgTuitionBill();
            queryBill.setBillYear(Long.valueOf(year));
            queryBill.setBillMonth(Long.valueOf(month));
            bills = tuitionBillMapper.selectKgTuitionBillList(queryBill);
        }
        
        // 将账单数据转换为计算结果格式
        List<Map<String, Object>> results = new ArrayList<>();
        for (KgTuitionBill bill : bills) {
            Map<String, Object> result = convertBillToCalculationResult(bill);
            results.add(result);
        }
        
        return results;
    }
    
    /**
     * 将账单数据转换为标准的计算结果格式
     */
    private Map<String, Object> convertBillToCalculationResult(KgTuitionBill bill) {
        Map<String, Object> result = new HashMap<>();
        
        // 学生信息
        KgStudent student = studentMapper.selectKgStudentById(bill.getStudentId());
        result.put("studentInfo", student);
        
        // 考勤统计
        Map<String, Object> attendanceStats = new HashMap<>();
        attendanceStats.put("totalDays", bill.getTotalDays());
        attendanceStats.put("attendanceDays", bill.getAttendanceDays());
        attendanceStats.put("attendanceRate", bill.getAttendanceRate());
        result.put("attendanceStats", attendanceStats);
        
        // 费用明细
        Map<String, Object> feeBreakdown = new HashMap<>();
        feeBreakdown.put("mealFee", bill.getMealFeeUsed());
        feeBreakdown.put("educationFee", bill.getEducationFeeUsed());
        feeBreakdown.put("managementFee", bill.getManagementFee()); // 使用正确的字段
        
        // 计算总费用
        BigDecimal totalFee = BigDecimal.ZERO;
        if (bill.getMealFeeUsed() != null) totalFee = totalFee.add(bill.getMealFeeUsed());
        if (bill.getEducationFeeUsed() != null) totalFee = totalFee.add(bill.getEducationFeeUsed());
        if (bill.getManagementFee() != null) totalFee = totalFee.add(bill.getManagementFee());
        feeBreakdown.put("totalFee", totalFee);
        
        feeBreakdown.put("balanceCarryover", bill.getTotalBalance());
        feeBreakdown.put("actualPayable", bill.getActualPayment());
        feeBreakdown.put("advancePayment", bill.getNextMonthPrepay());
        result.put("feeBreakdown", feeBreakdown);
        
        // 配置信息（获取当前配置）
        KgTuitionConfig config = getTuitionConfig(bill.getClassId());
        result.put("config", config);
        
        // 计算日期（使用账单创建时间）
        result.put("calculationDate", bill.getCreateTime());
        
        // 保留原始账单数据
        result.put("originalBill", bill);
        
        return result;
    }

}
