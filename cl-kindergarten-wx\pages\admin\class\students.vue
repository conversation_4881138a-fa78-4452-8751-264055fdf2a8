<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ className }}学生</text>
				</view>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stat-card">
				<view class="stat-icon">👥</view>
				<view class="stat-info">
					<text class="stat-number">{{ studentList.length }}</text>
					<text class="stat-label">总人数</text>
				</view>
			</view>
			<view class="stat-card">
				<view class="stat-icon">👦</view>
				<view class="stat-info">
					<text class="stat-number">{{ maleCount }}</text>
					<text class="stat-label">男生</text>
				</view>
			</view>
			<view class="stat-card">
				<view class="stat-icon">👧</view>
				<view class="stat-info">
					<text class="stat-number">{{ femaleCount }}</text>
					<text class="stat-label">女生</text>
				</view>
			</view>
		</view>

		<!-- 学生列表 -->
		<view class="student-list">
			<!-- 加载状态 -->
			<view v-if="loading && studentList.length === 0" class="loading-container">
				<u-loading-icon mode="spinner" color="#667eea" size="40"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!loading && studentList.length === 0" class="empty-container">
				<view class="empty-icon">👶</view>
				<text class="empty-text">暂无学生数据</text>
				<text class="empty-tip">该班级还没有学生</text>
			</view>

			<!-- 学生列表 -->
			<view v-else>
				<view v-for="student in studentList" :key="student.studentId" class="student-item-card">
					<view class="student-avatar">
						<view class="avatar-circle" :class="student.gender === '0' ? 'male' : 'female'">
							<text class="avatar-text">{{ student.studentName.charAt(0) }}</text>
						</view>
					</view>
					
					<view class="student-info">
						<view class="student-name">{{ student.studentName }}</view>
						<view class="student-meta">
							<text class="student-code">{{ student.studentCode || student.studentId }}</text>
							<text class="student-gender">{{ student.gender === '0' ? '男' : '女' }}</text>
							<text class="student-age">{{ calculateAge(student.birthDate) }}岁</text>
						</view>
						<view class="student-details">
							<text class="detail-item">📞 {{ student.parentPhone || '未填写' }}</text>
							<text class="detail-item">🏠 {{ student.address || '未填写' }}</text>
						</view>
					</view>
					
					<view class="student-actions">
						<view class="action-btn" @click="viewStudentDetail(student)">
							<u-icon name="eye" color="#667eea" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 加载更多 -->
				<view v-if="hasMore" class="load-more" @click="loadMore">
					<u-loading-icon v-if="loading" mode="spinner" color="#667eea" size="24"></u-loading-icon>
					<text class="load-more-text">{{ loading ? '加载中...' : '点击加载更多' }}</text>
				</view>

				<!-- 没有更多数据 -->
				<view v-else-if="studentList.length > 0" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getStudentList } from '@/api/api.js'

export default {
	data() {
		return {
			classId: null,
			className: '',
			studentList: [],
			loading: false,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			hasMore: true,
			refreshing: false
		}
	},

	computed: {
		maleCount() {
			return this.studentList.filter(student => student.gender === '0').length
		},
		
		femaleCount() {
			return this.studentList.filter(student => student.gender === '1').length
		}
	},

	async onLoad(options) {
		if (options.classId) {
			this.classId = parseInt(options.classId)
			this.className = options.className || '班级'
			await this.loadStudentList()
		} else {
			toast('缺少班级ID参数')
			uni.navigateBack()
		}
	},

	onPullDownRefresh() {
		this.refreshList()
	},

	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore()
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 加载学生列表
		async loadStudentList() {
			if (this.loading) return

			this.loading = true
			try {
				const params = {
					classId: this.classId,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}

				const res = await getStudentList(params)

				if (res.code === 200) {
					const newList = res.rows || []

					if (this.pageNum === 1) {
						this.studentList = newList
					} else {
						this.studentList = [...this.studentList, ...newList]
					}

					this.total = res.total || 0
					this.hasMore = this.studentList.length < this.total
				} else {
					toast(res.msg || '加载失败')
				}
			} catch (error) {
				console.error('加载学生列表失败:', error)
				toast('加载失败，请稍后重试')
			} finally {
				this.loading = false
				if (this.refreshing) {
					uni.stopPullDownRefresh()
					this.refreshing = false
				}
			}
		},

		// 刷新列表
		refreshList() {
			this.refreshing = true
			this.pageNum = 1
			this.hasMore = true
			this.loadStudentList()
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.pageNum++
				this.loadStudentList()
			}
		},

		// 查看学生详情
		viewStudentDetail(student) {
			uni.navigateTo({
				url: `/pages/admin/student/detail?id=${student.studentId}`
			})
		},

		// 计算年龄
		calculateAge(birthDate) {
			if (!birthDate) return '未知'
			
			const birth = new Date(birthDate)
			const today = new Date()
			let age = today.getFullYear() - birth.getFullYear()
			
			const monthDiff = today.getMonth() - birth.getMonth()
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
				age--
			}
			
			return age > 0 ? age : 0
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 统计信息 */
.stats-section {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	padding-bottom: 20rpx;
}

.stat-card {
	flex: 1;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 学生列表 */
.student-list {
	padding: 0 30rpx 40rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.loading-text {
	margin-top: 20rpx;
	color: #667eea;
	font-size: 28rpx;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	color: #666;
	font-size: 32rpx;
	margin-bottom: 10rpx;
}

.empty-tip {
	color: #999;
	font-size: 26rpx;
}

/* 学生卡片 */
.student-item-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	gap: 20rpx;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.student-avatar {
	flex-shrink: 0;
}

.avatar-circle {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&.male {
		background: linear-gradient(135deg, #42a5f5, #2196f3);
	}
	
	&.female {
		background: linear-gradient(135deg, #ec407a, #e91e63);
	}
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #ffffff;
}

.student-info {
	flex: 1;
}

.student-name {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
}

.student-meta {
	display: flex;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.student-code {
	background: #e3f2fd;
	color: #1976d2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.student-gender {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.student-age {
	background: #e8f5e8;
	color: #388e3c;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.student-details {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.detail-item {
	font-size: 24rpx;
	color: #666;
}

.student-actions {
	flex-shrink: 0;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: #e0e0e0;
	}
}

/* 加载更多 */
.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	margin-top: 20rpx;
	cursor: pointer;
}

.load-more-text {
	margin-left: 10rpx;
	color: #667eea;
	font-size: 28rpx;
}

/* 没有更多数据 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	margin-top: 20rpx;
}

.no-more-text {
	color: #999;
	font-size: 26rpx;
}
</style>
