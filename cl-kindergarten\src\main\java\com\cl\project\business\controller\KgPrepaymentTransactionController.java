package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgPrepaymentTransaction;
import com.cl.project.business.service.IKgPrepaymentTransactionService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 预交款流水Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/business/transaction")
public class KgPrepaymentTransactionController extends BaseController
{
    @Autowired
    private IKgPrepaymentTransactionService kgPrepaymentTransactionService;

    /**
     * 查询预交款流水列表
     */
    @SaCheckPermission("business:transaction:list")
    @GetMapping("/list")
    public TableDataInfo list(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        startPage();
        List<KgPrepaymentTransaction> list = kgPrepaymentTransactionService.selectKgPrepaymentTransactionList(kgPrepaymentTransaction);
        return getDataTable(list);
    }

    /**
     * 导出预交款流水列表
     */
    @SaCheckPermission("business:transaction:export")
    @Log(title = "预交款流水", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        List<KgPrepaymentTransaction> list = kgPrepaymentTransactionService.selectKgPrepaymentTransactionList(kgPrepaymentTransaction);
        ExcelUtil<KgPrepaymentTransaction> util = new ExcelUtil<KgPrepaymentTransaction>(KgPrepaymentTransaction.class);
        return util.exportExcel(list, "transaction");
    }

    /**
     * 获取预交款流水详细信息
     */
    @SaCheckPermission("business:transaction:query")
    @GetMapping(value = "/{transactionId}")
    public AjaxResult getInfo(@PathVariable("transactionId") Long transactionId)
    {
        return AjaxResult.success(kgPrepaymentTransactionService.selectKgPrepaymentTransactionById(transactionId));
    }

    /**
     * 新增预交款流水
     */
    @SaCheckPermission("business:transaction:add")
    @Log(title = "预交款流水", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        return toAjax(kgPrepaymentTransactionService.insertKgPrepaymentTransaction(kgPrepaymentTransaction));
    }

    /**
     * 修改预交款流水
     */
    @SaCheckPermission("business:transaction:edit")
    @Log(title = "预交款流水", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgPrepaymentTransaction kgPrepaymentTransaction)
    {
        return toAjax(kgPrepaymentTransactionService.updateKgPrepaymentTransaction(kgPrepaymentTransaction));
    }

    /**
     * 删除预交款流水
     */
    @SaCheckPermission("business:transaction:remove")
    @Log(title = "预交款流水", businessType = BusinessType.DELETE)
	@DeleteMapping("/{transactionIds}")
    public AjaxResult remove(@PathVariable Long[] transactionIds)
    {
        return toAjax(kgPrepaymentTransactionService.deleteKgPrepaymentTransactionByIds(transactionIds));
    }
}
