import request from '@/utils/request'

// 查询课程列表
export function listCourse(query) {
  return request({
    url: '/business/course/list',
    method: 'get',
    params: query
  })
}

// 查询课程详细
export function getCourse(courseId) {
  return request({
    url: '/business/course/' + courseId,
    method: 'get'
  })
}

// 新增课程
export function addCourse(data) {
  return request({
    url: '/business/course',
    method: 'post',
    data: data
  })
}

// 修改课程
export function updateCourse(data) {
  return request({
    url: '/business/course',
    method: 'put',
    data: data
  })
}

// 删除课程
export function delCourse(courseId) {
  return request({
    url: '/business/course/' + courseId,
    method: 'delete'
  })
}

// 导出课程
export function exportCourse(query) {
  return request({
    url: '/business/course/export',
    method: 'get',
    params: query
  })
}

// 获取所有课程列表（用于选择）
export function listAllCourse() {
  return request({
    url: '/business/course/listAll',
    method: 'get'
  })
}
