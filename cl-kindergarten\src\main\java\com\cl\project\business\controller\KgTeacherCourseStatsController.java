package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.framework.web.controller.BaseController;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cl.project.business.service.IKgTeacherCourseStatsService;
import com.cl.project.business.domain.dto.TeacherCourseStatsDto;
import com.cl.common.utils.poi.ExcelUtil;

/**
 * 教师课时统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/business/teacher-course-stats")
public class KgTeacherCourseStatsController extends BaseController
{
    @Autowired
    private IKgTeacherCourseStatsService kgTeacherCourseStatsService;

    /**
     * 查询教师课时统计列表
     */
    @GetMapping("/list")
    public AjaxResult list(TeacherCourseStatsDto params)
    {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("statYear", params.getStatYear());
        queryParams.put("statMonth", params.getStatMonth());
        queryParams.put("teacherId", params.getTeacherId());
        
        List<TeacherCourseStatsDto> list = kgTeacherCourseStatsService.getTeacherCourseStatsList(queryParams);
        return AjaxResult.success(list);
    }

    /**
     * 获取教师课时统计详细信息
     */
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        // 返回指定教师的统计信息，用于编辑和详情查看
        TeacherCourseStatsDto result = new TeacherCourseStatsDto();
        result.setTeacherId(teacherId);
        return AjaxResult.success(result);
    }

    /**
     * 计算教师课时统计
     */
//    @SaCheckPermission("kg:teacher:stats:calculate")
    @Log(title = "计算教师课时统计", businessType = BusinessType.INSERT)
    @PostMapping("/calculate")
    public AjaxResult calculateStats(@RequestBody Map<String, Object> params)
    {
        try {
            Map<String, Object> result = kgTeacherCourseStatsService.calculateTeacherCourseStats(params);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("计算失败: " + e.getMessage());
        }
    }

    /**
     * 重新计算教师课时统计
     */
//    @SaCheckPermission("kg:teacher:stats:calculate")
    @Log(title = "重新计算教师课时统计", businessType = BusinessType.UPDATE)
    @PostMapping("/recalculate")
    public AjaxResult recalculateStats(@RequestBody Map<String, Object> params)
    {
        try {
            Long teacherId = Long.valueOf(params.get("teacherId").toString());
            Integer year = (Integer) params.get("statYear");
            Integer month = (Integer) params.get("statMonth");
            
            Map<String, Object> result = kgTeacherCourseStatsService.recalculateTeacherCourseStats(teacherId, year, month);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("重新计算失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师课时统计汇总
     */
    @GetMapping("/summary")
    public AjaxResult getStatsSummary(TeacherCourseStatsDto params)
    {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("statYear", params.getStatYear());
        queryParams.put("statMonth", params.getStatMonth());
        queryParams.put("teacherId", params.getTeacherId());
        
        Map<String, Object> summary = kgTeacherCourseStatsService.getTeacherCourseStatsSummary(queryParams);
        return AjaxResult.success(summary);
    }

    /**
     * 导出教师课时统计
     */
    @SaCheckPermission("kg:teacher:stats:export")
    @Log(title = "教师课时统计", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TeacherCourseStatsDto params)
    {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("statYear", params.getStatYear());
        queryParams.put("statMonth", params.getStatMonth());
        queryParams.put("teacherId", params.getTeacherId());
        
        List<TeacherCourseStatsDto> list = kgTeacherCourseStatsService.getTeacherCourseStatsList(queryParams);
        ExcelUtil<TeacherCourseStatsDto> util = new ExcelUtil<TeacherCourseStatsDto>(TeacherCourseStatsDto.class);
        return util.exportExcel(list, "teacher_stats");
    }

    /**
     * 获取教师详细授课记录
     */
    @GetMapping("/details/{teacherId}")
    public AjaxResult getCourseDetails(@PathVariable("teacherId") Long teacherId, TeacherCourseStatsDto params)
    {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("statYear", params.getStatYear());
        queryParams.put("statMonth", params.getStatMonth());
        
        List<Map<String, Object>> list = kgTeacherCourseStatsService.getTeacherCourseDetails(teacherId, queryParams);
        return AjaxResult.success(list);
    }

    /**
     * 批量计算教师课时统计
     */
    @SaCheckPermission("kg:teacher:stats:calculate")
    @Log(title = "批量计算教师课时统计", businessType = BusinessType.INSERT)
    @PostMapping("/batchCalculate")
    public AjaxResult batchCalculateStats(@RequestBody Map<String, Object> params)
    {
        try {
            // 批量计算就是计算所有教师
            params.put("calculateType", "all");
            Map<String, Object> result = kgTeacherCourseStatsService.calculateTeacherCourseStats(params);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("批量计算失败: " + e.getMessage());
        }
    }

    /**
     * 确认教师课时统计
     */
    @SaCheckPermission("kg:teacher:stats:confirm")
    @Log(title = "确认教师课时统计", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirmStats(@RequestBody Long[] statsIds)
    {
        // 实际实现需要根据业务逻辑来开发
        return AjaxResult.success("统计确认成功");
    }

    /**
     * 删除教师课时统计
     */
//    @SaCheckPermission("kg:teacher:stats:delete")
    @Log(title = "教师课时统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{statsIds}")
    public AjaxResult remove(@PathVariable Long[] statsIds)
    {
        // 实际实现需要根据业务逻辑来开发
        return AjaxResult.success("删除成功");
    }
}
