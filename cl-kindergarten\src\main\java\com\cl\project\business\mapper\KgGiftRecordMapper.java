package com.cl.project.business.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.cl.project.business.domain.KgGiftRecord;

/**
 * 赠送记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface KgGiftRecordMapper 
{
    /**
     * 查询赠送记录
     * 
     * @param recordId 赠送记录主键
     * @return 赠送记录
     */
    public KgGiftRecord selectKgGiftRecordByRecordId(Long recordId);

    /**
     * 查询赠送记录列表
     * 
     * @param kgGiftRecord 赠送记录
     * @return 赠送记录集合
     */
    public List<KgGiftRecord> selectKgGiftRecordList(KgGiftRecord kgGiftRecord);

    /**
     * 新增赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    public int insertKgGiftRecord(KgGiftRecord kgGiftRecord);

    /**
     * 修改赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    public int updateKgGiftRecord(KgGiftRecord kgGiftRecord);

    /**
     * 删除赠送记录
     * 
     * @param recordId 赠送记录主键
     * @return 结果
     */
    public int deleteKgGiftRecordByRecordId(Long recordId);

    /**
     * 批量删除赠送记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKgGiftRecordByRecordIds(Long[] recordIds);

    /**
     * 查询学生的有效赠送记录
     * 
     * @param studentId 学生ID
     * @return 有效赠送记录集合
     */
    public List<KgGiftRecord> selectActiveGiftRecordsByStudentId(Long studentId);

    /**
     * 查询学生在指定规则下的赠送次数
     * 
     * @param studentId 学生ID
     * @param ruleId 规则ID
     * @return 赠送次数
     */
    public int countGiftRecordsByStudentAndRule(Long studentId, Long ruleId);

    /**
     * 查询指定规则在指定月份的赠送次数
     * 
     * @param ruleId 规则ID
     * @param giftMonth 赠送月份
     * @return 赠送次数
     */
    public int countGiftRecordsByRuleAndMonth(Long ruleId, String giftMonth);

    /**
     * 更新赠送记录的使用课时
     * 
     * @param recordId 记录ID
     * @param usedSessions 使用课时数
     * @return 结果
     */
    public int updateGiftRecordUsedSessions(@Param("recordId") Long recordId, @Param("usedSessions") Integer usedSessions);

    /**
     * 统计学生赠送记录的课时使用情况
     * 
     * @param studentId 学生ID
     * @return 统计结果：包含总赠送课时、已使用课时、剩余课时等
     */
    public Map<String, Object> getGiftStatisticsByStudentId(Long studentId);

    /**
     * 根据规则ID、学生ID、月份统计赠送记录使用次数
     * 
     * @param ruleId 规则ID
     * @param studentId 学生ID
     * @param month 月份(格式：yyyy-MM)
     * @return 使用次数
     */
    public int countRuleUsageByStudentMonth(@Param("ruleId") Long ruleId, @Param("studentId") Long studentId, @Param("month") String month);

    /**
     * 使用赠送课时
     * 
     * @param recordId 记录ID
     * @param sessions 课时数
     * @return 结果
     */
    public int useGiftSessions(@Param("recordId") Long recordId, @Param("sessions") Long sessions);

    /**
     * 根据学生ID和课程ID查询有效的赠送记录
     * 
     * @param studentId 学生ID
     * @param courseId 课程ID
     * @return 有效赠送记录集合
     */
    public List<KgGiftRecord> selectActiveGiftRecordsByStudentAndCourse(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
}
