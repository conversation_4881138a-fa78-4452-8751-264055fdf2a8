import request from '@/utils/request'

// 查询赠送记录列表
export function listGiftRecord(query) {
  return request({
    url: '/business/gift/record/list',
    method: 'get',
    params: query
  })
}

// 查询赠送记录详细
export function getGiftRecord(recordId) {
  return request({
    url: '/business/gift/record/' + recordId,
    method: 'get'
  })
}

// 新增赠送记录
export function addGiftRecord(data) {
  return request({
    url: '/business/gift/record',
    method: 'post',
    data: data
  })
}

// 修改赠送记录
export function updateGiftRecord(data) {
  return request({
    url: '/business/gift/record',
    method: 'put',
    data: data
  })
}

// 删除赠送记录
export function delGiftRecord(recordId) {
  return request({
    url: '/business/gift/record/' + recordId,
    method: 'delete'
  })
}

// 导出赠送记录
export function exportGiftRecord(query) {
  return request({
    url: '/business/gift/record/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}

// 查询学生的有效赠送记录
export function getStudentGiftRecords(studentId) {
  return request({
    url: '/business/gift/record/student/' + studentId,
    method: 'get'
  })
}

// 更新赠送记录的使用课时
export function updateUsedSessions(recordId, usedSessions) {
  return request({
    url: '/business/gift/record/' + recordId + '/useSessions/' + usedSessions,
    method: 'put'
  })
}
