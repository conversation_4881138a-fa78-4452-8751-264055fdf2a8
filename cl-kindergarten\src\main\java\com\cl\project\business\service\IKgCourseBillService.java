package com.cl.project.business.service;

import java.util.List;
import java.util.Map;
import com.cl.project.business.domain.KgCourseBill;

/**
 * 托管费账单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgCourseBillService 
{
    /**
     * 查询托管费账单
     * 
     * @param billId 托管费账单ID
     * @return 托管费账单
     */
    public KgCourseBill selectKgCourseBillById(Long billId);

    /**
     * 查询托管费账单完整信息（包含明细和学生信息）
     * 
     * @param billId 托管费账单ID
     * @return 账单完整信息（包含账单、明细、学生、班级信息）
     */
    public Map<String, Object> selectKgCourseBillWithDetails(Long billId);

    /**
     * 查询托管费账单列表
     * 
     * @param kgCourseBill 托管费账单
     * @return 托管费账单集合
     */
    public List<KgCourseBill> selectKgCourseBillList(KgCourseBill kgCourseBill);

    /**
     * 新增托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    public int insertKgCourseBill(KgCourseBill kgCourseBill);

    /**
     * 修改托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    public int updateKgCourseBill(KgCourseBill kgCourseBill);

    /**
     * 批量删除托管费账单
     * 
     * @param billIds 需要删除的托管费账单ID
     * @return 结果
     */
    public int deleteKgCourseBillByIds(Long[] billIds);

    /**
     * 删除托管费账单信息
     * 
     * @param billId 托管费账单ID
     * @return 结果
     */
    public int deleteKgCourseBillById(Long billId);

    /**
     * 生成月度托管费账单
     * 
     * @param billYear 账单年份
     * @param billMonth 账单月份
     * @param classId 班级ID（只针对某个班级生成，为null时全部班级）
     * @param studentId 学生ID（只针对某个学生生成，为null时全部学生）
     * @return 生成的账单数量
     */
    public int generateMonthlyBills(Integer billYear, Integer billMonth, Long classId, Long studentId);

    /**
     * 发送托管费账单
     * 
     * @param billIds 账单ID数组
     * @return 发送成功的账单数量
     */
    public int sendBills(Long[] billIds);

    /**
     * 标记账单为已支付
     * 
     * @param billIds 账单ID数组
     * @return 标记成功的账单数量
     */
    public int markBillsPaid(Long[] billIds);

    /**
     * 获取账单统计信息
     * 
     * @param params 查询参数
     * @return 统计信息
     */
    public Map<String, Object> getBillStatistics(KgCourseBill params);
}
