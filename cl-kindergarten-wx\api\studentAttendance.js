import request from '@/utils/request.js'

// ========== 学生考勤管理相关接口 ==========

// 查询学生考勤概览列表
export function getStudentAttendanceOverview(params) {
  return request.get('/business/student-attendance/overview', params)
}

// 查询学生考勤记录列表
export function getStudentAttendanceList(params) {
  return request.get('/business/student-attendance/list', params)
}

// 获取学生考勤详情
export function getStudentAttendanceDetail(attendanceId) {
  return request.get(`/business/student-attendance/${attendanceId}`)
}

// 新增学生考勤记录
export function addStudentAttendance(data) {
  return request.post('/business/student-attendance', data)
}

// 修改学生考勤记录
export function updateStudentAttendance(data) {
  return request.put('/business/student-attendance', data)
}

// 删除学生考勤记录
export function deleteStudentAttendance(attendanceIds) {
  return request.delete(`/business/student-attendance/${attendanceIds}`)
}

// 学生签到（单个）
export function studentCheckin(data) {
  return request.post('/business/student-attendance/checkin', data)
}

// 学生签到并确认（组合接口）
export function studentCheckinAndConfirm(data) {
  return request.post('/business/student-attendance/checkinAndConfirm', data)
}

// 单个学生签到（使用批量接口）
export function singleStudentCheckin(studentId, attendanceDate, remark = '') {
  return batchStudentCheckin({
    studentIds: [studentId.toString()],
    attendanceDate: attendanceDate,
    attendanceStatus: '1', // 1-出勤
    checkInMethod: 'manual',
    remark: remark
  })
}

// 学生签退
export function studentCheckout(data) {
  return request.post('/business/student-attendance/checkout', data)
}

// 批量学生签到
export function batchStudentCheckin(data) {
  return request.post('/business/student-attendance/batchCheckin', data)
}

// 考勤确认（单个）
export function confirmStudentAttendance(attendanceId) {
  return request.post(`/business/student-attendance/confirm/${attendanceId}`)
}

// 批量确认学生考勤
export function batchConfirmStudentAttendance(data) {
  return request.post('/business/student-attendance/batchConfirm', data)
}

// 缺勤登记
export function registerAbsence(data) {
  return request.post('/business/student-attendance/absence', data)
}

// 导出学生考勤记录
export function exportStudentAttendance(params) {
  return request.get('/business/student-attendance/export', params)
}

// 获取钉钉原始打卡数据
export function getDingtalkAttendanceRecords(params) {
  return request.get('/business/dingtalk-attendance/list', params)
}

// ========== 学生信息相关接口 ==========

// 获取所有学生列表
export function getAllStudentList() {
  return request.get('/business/student/allList')
}

// 查询学生列表
export function getStudentList(params) {
  return request.get('/business/student/list', params)
}

// 获取学生详细信息
export function getStudentDetail(studentId) {
  return request.get(`/business/student/${studentId}`)
}

// ========== 班级信息相关接口 ==========

// 获取所有班级列表
export function getAllClassList() {
  return request.get('/business/class/allList')
}

// 查询班级列表
export function getClassList(params) {
  return request.get('/business/class/list', params)
}

// 获取班级详细信息
export function getClassDetail(classId) {
  return request.get(`/business/class/${classId}`)
}

// ========== 考勤统计相关接口 ==========

// 获取学生考勤统计
export function getStudentAttendanceStatistics(params) {
  return request.get('/business/attendance-statistics/student', params)
}

// 获取班级考勤统计
export function getClassAttendanceStatistics(params) {
  return request.get('/business/attendance-statistics/class', params)
}

// 获取考勤汇总报表
export function getAttendanceSummary(params) {
  return request.get('/business/attendance-statistics/summary', params)
}

// ========== 辅助功能接口 ==========

// 同步钉钉考勤数据
export function syncDingtalkAttendance(data) {
  // 如果userIdList为空数组，则调用同步所有用户的接口
  if (!data.userIdList || data.userIdList.length === 0) {
    return request.post('/business/dingtalk/syncAttendance', data)
  }
  return request.post('/business/dingtalk/syncAttendanceByUsers', data)
}

// 同步单个学生的钉钉考勤数据
export function syncStudentDingtalkAttendance(data) {
  return request.post('/business/dingtalk/syncAttendanceByUsers', data)
}

// 获取考勤异常记录
export function getAttendanceExceptions(params) {
  return request.get('/business/student-attendance/exceptions', params)
}

// 处理考勤异常
export function handleAttendanceException(data) {
  return request.post('/business/student-attendance/handle-exception', data)
}
