package com.cl.project.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgGiftRecordMapper;
import com.cl.project.business.domain.KgGiftRecord;
import com.cl.project.business.service.IKgGiftRecordService;

/**
 * 赠送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class KgGiftRecordServiceImpl implements IKgGiftRecordService 
{
    @Autowired
    private KgGiftRecordMapper kgGiftRecordMapper;

    /**
     * 查询赠送记录
     * 
     * @param recordId 赠送记录主键
     * @return 赠送记录
     */
    @Override
    public KgGiftRecord selectKgGiftRecordByRecordId(Long recordId)
    {
        return kgGiftRecordMapper.selectKgGiftRecordByRecordId(recordId);
    }

    /**
     * 查询赠送记录列表
     * 
     * @param kgGiftRecord 赠送记录
     * @return 赠送记录
     */
    @Override
    public List<KgGiftRecord> selectKgGiftRecordList(KgGiftRecord kgGiftRecord)
    {
        return kgGiftRecordMapper.selectKgGiftRecordList(kgGiftRecord);
    }

    /**
     * 新增赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    @Override
    public int insertKgGiftRecord(KgGiftRecord kgGiftRecord)
    {
        return kgGiftRecordMapper.insertKgGiftRecord(kgGiftRecord);
    }

    /**
     * 修改赠送记录
     * 
     * @param kgGiftRecord 赠送记录
     * @return 结果
     */
    @Override
    public int updateKgGiftRecord(KgGiftRecord kgGiftRecord)
    {
        return kgGiftRecordMapper.updateKgGiftRecord(kgGiftRecord);
    }

    /**
     * 批量删除赠送记录
     * 
     * @param recordIds 需要删除的赠送记录主键
     * @return 结果
     */
    @Override
    public int deleteKgGiftRecordByRecordIds(Long[] recordIds)
    {
        return kgGiftRecordMapper.deleteKgGiftRecordByRecordIds(recordIds);
    }

    /**
     * 删除赠送记录信息
     * 
     * @param recordId 赠送记录主键
     * @return 结果
     */
    @Override
    public int deleteKgGiftRecordByRecordId(Long recordId)
    {
        return kgGiftRecordMapper.deleteKgGiftRecordByRecordId(recordId);
    }

    /**
     * 查询学生的有效赠送记录
     * 
     * @param studentId 学生ID
     * @return 有效赠送记录列表
     */
    @Override
    public List<KgGiftRecord> selectActiveGiftRecordsByStudentId(Long studentId)
    {
        return kgGiftRecordMapper.selectActiveGiftRecordsByStudentId(studentId);
    }

    /**
     * 统计学生在指定规则下的赠送次数
     * 
     * @param studentId 学生ID
     * @param ruleId 规则ID
     * @return 赠送次数
     */
    @Override
    public int countGiftRecordsByStudentAndRule(Long studentId, Long ruleId)
    {
        return kgGiftRecordMapper.countGiftRecordsByStudentAndRule(studentId, ruleId);
    }

    /**
     * 统计指定规则在指定月份的赠送次数
     * 
     * @param ruleId 规则ID
     * @param giftMonth 赠送月份
     * @return 赠送次数
     */
    @Override
    public int countGiftRecordsByRuleAndMonth(Long ruleId, String giftMonth)
    {
        return kgGiftRecordMapper.countGiftRecordsByRuleAndMonth(ruleId, giftMonth);
    }

    /**
     * 更新赠送记录的使用课时
     * 
     * @param recordId 记录ID
     * @param usedSessions 已使用课时
     * @return 结果
     */
    @Override
    public int updateGiftRecordUsedSessions(Long recordId, Integer usedSessions)
    {
        return kgGiftRecordMapper.updateGiftRecordUsedSessions(recordId, usedSessions);
    }
}
