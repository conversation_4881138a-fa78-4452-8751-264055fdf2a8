package com.cl.project.business.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;
import com.cl.project.business.domain.KgPrepaymentTransaction;
import com.cl.project.business.dto.PrepaymentBalanceDto;
import com.cl.project.business.dto.PrepaymentRechargeDto;
import com.cl.project.business.dto.PrepaymentStatisticsDto;
import com.cl.project.business.service.IKgPrepaymentTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgPrepaymentAccountMapper;
import com.cl.project.business.domain.KgPrepaymentAccount;
import com.cl.project.business.service.IKgPrepaymentAccountService;

/**
 * 预交款账户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class KgPrepaymentAccountServiceImpl implements IKgPrepaymentAccountService 
{
    @Autowired
    private KgPrepaymentAccountMapper kgPrepaymentAccountMapper;
    
    @Autowired
    private IKgPrepaymentTransactionService prepaymentTransactionService;
    
    // 收入服务暂未实现
    // @Autowired
    // private IKgIncomeService incomeService;

    /**
     * 查询预交款账户
     * 
     * @param accountId 预交款账户ID
     * @return 预交款账户
     */
    @Override
    public KgPrepaymentAccount selectKgPrepaymentAccountById(Long accountId)
    {
        return kgPrepaymentAccountMapper.selectKgPrepaymentAccountById(accountId);
    }

    /**
     * 查询预交款账户列表
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 预交款账户
     */
    @Override
    public List<KgPrepaymentAccount> selectKgPrepaymentAccountList(KgPrepaymentAccount kgPrepaymentAccount)
    {
        return kgPrepaymentAccountMapper.selectKgPrepaymentAccountList(kgPrepaymentAccount);
    }

    /**
     * 新增预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    @Override
    public int insertKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount)
    {
        kgPrepaymentAccount.setCreateTime(DateUtils.getNowDate());
        return kgPrepaymentAccountMapper.insertKgPrepaymentAccount(kgPrepaymentAccount);
    }

    /**
     * 修改预交款账户
     * 
     * @param kgPrepaymentAccount 预交款账户
     * @return 结果
     */
    @Override
    public int updateKgPrepaymentAccount(KgPrepaymentAccount kgPrepaymentAccount)
    {
        kgPrepaymentAccount.setUpdateTime(DateUtils.getNowDate());
        return kgPrepaymentAccountMapper.updateKgPrepaymentAccount(kgPrepaymentAccount);
    }

    /**
     * 批量删除预交款账户
     * 
     * @param accountIds 需要删除的预交款账户ID
     * @return 结果
     */
    @Override
    public int deleteKgPrepaymentAccountByIds(Long[] accountIds)
    {
        return kgPrepaymentAccountMapper.deleteKgPrepaymentAccountByIds(accountIds);
    }

    /**
     * 删除预交款账户信息
     * 
     * @param accountId 预交款账户ID
     * @return 结果
     */
    @Override
    public int deleteKgPrepaymentAccountById(Long accountId)
    {
        return kgPrepaymentAccountMapper.deleteKgPrepaymentAccountById(accountId);
    }

    // ========== 业务方法实现 ==========

    /**
     * 查询预交款余额列表
     * 
     * @param queryParams 查询参数
     * @return 预交款余额列表
     */
    @Override
    public List<PrepaymentBalanceDto> selectBalanceList(PrepaymentBalanceDto queryParams)
    {
        String comId = SecurityUtils.getCurrComId();
        queryParams.setComId(comId);
        return kgPrepaymentAccountMapper.selectBalanceList(queryParams);
    }

    /**
     * 获取预交款余额统计
     * 
     * @return 统计数据
     */
    @Override
    public PrepaymentStatisticsDto getBalanceStatistics()
    {
        String comId = SecurityUtils.getCurrComId();
        return kgPrepaymentAccountMapper.getBalanceStatistics(comId);
    }

    /**
     * 获取学生预交款余额详情
     * 
     * @param studentId 学生ID
     * @return 余额详情
     */
    @Override
    public PrepaymentBalanceDto getStudentBalanceDetail(Long studentId)
    {
        PrepaymentBalanceDto balanceDto = kgPrepaymentAccountMapper.getStudentBalanceDetail(studentId);
        if (balanceDto != null) {
            // 获取最近的流水记录
            KgPrepaymentTransaction queryTransaction = new KgPrepaymentTransaction();
            queryTransaction.setStudentId(studentId);
            List<KgPrepaymentTransaction> recentTransactions = prepaymentTransactionService
                .selectKgPrepaymentTransactionList(queryTransaction)
                .stream()
                .limit(10) // 只取最近10条
                .collect(Collectors.toList());
            
            // 转换为DTO
            List<PrepaymentBalanceDto.TransactionRecordDto> recordDtos = new ArrayList<>();
            for (KgPrepaymentTransaction transaction : recentTransactions) {
                PrepaymentBalanceDto.TransactionRecordDto recordDto = new PrepaymentBalanceDto.TransactionRecordDto();
                recordDto.setTransactionTime(transaction.getTransactionTime());
                recordDto.setTransactionType(transaction.getTransactionType());
                recordDto.setAmount(transaction.getAmount());
                recordDto.setAfterBalance(transaction.getAfterBalance());
                recordDto.setDescription(transaction.getDescription());
                recordDtos.add(recordDto);
            }
            balanceDto.setRecentTransactions(recordDtos);
        }
        return balanceDto;
    }

    /**
     * 预交款充值
     * 
     * @param rechargeDto 充值信息
     * @return 结果
     */
    @Override
    @Transactional
    public int rechargeBalance(PrepaymentRechargeDto rechargeDto)
    {
        // 1. 获取或创建账户
        KgPrepaymentAccount account = getOrCreateAccount(rechargeDto.getStudentId(), rechargeDto.getAccountType());
        
        // 2. 计算充值前后余额
        BigDecimal beforeBalance = account.getBalance();
        BigDecimal rechargeAmount = rechargeDto.getAmount();
        BigDecimal afterBalance = beforeBalance.add(rechargeAmount);
        
        // 3. 更新账户余额
        account.setBalance(afterBalance);
        account.setLastRechargeTime(new Date());
        account.setUpdateTime(new Date());
        int result = updateKgPrepaymentAccount(account);
        
        if (result > 0) {
            // 4. 记录充值流水 relatedBillType
            KgPrepaymentTransaction transaction = new KgPrepaymentTransaction();
            transaction.setRelatedBillType(rechargeDto.getAccountType());
            transaction.setAccountId(account.getAccountId());
            transaction.setStudentId(rechargeDto.getStudentId());
            transaction.setTransactionType("recharge");
            transaction.setAmount(rechargeAmount);
            transaction.setBeforeBalance(beforeBalance);
            transaction.setAfterBalance(afterBalance);
            transaction.setPaymentMethod(rechargeDto.getPaymentMethod());
            transaction.setTransactionTime(new Date());
            transaction.setDescription("预交款充值");
            transaction.setRemark(rechargeDto.getRemark());
            transaction.setOperatorId(SecurityUtils.getCurrUserId());
            transaction.setComId(SecurityUtils.getCurrComId());
            transaction.setCreateBy(SecurityUtils.getCurrUserId().toString());
            
            prepaymentTransactionService.insertKgPrepaymentTransaction(transaction);
            
            // 5. 记录收入（可选）
            // TODO: 根据需要记录到kg_income表
        }
        
        return result;
    }

    /**
     * 批量扣费
     * 
     * @param studentIds 学生ID列表
     * @param accountType 账户类型
     * @param amount 扣费金额
     * @param description 描述
     * @return 扣费成功的账户数
     */
    @Override
    @Transactional
    public int batchDeduct(List<Long> studentIds, String accountType, Double amount, String description)
    {
        int successCount = 0;
        for (Long studentId : studentIds) {
            try {
                Double afterBalance = deductBalance(studentId, accountType, amount, null, "batch_deduct", description);
                if (afterBalance != null) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误日志，但继续处理其他账户
                // TODO: 添加日志记录
            }
        }
        return successCount;
    }

    /**
     * 余额转移
     * 
     * @param fromStudentId 转出学生ID
     * @param toStudentId 转入学生ID
     * @param accountType 账户类型
     * @param amount 转移金额
     * @param description 描述
     * @return 结果
     */
    @Override
    @Transactional
    public int transferBalance(Long fromStudentId, Long toStudentId, String accountType, Double amount, String description)
    {
        // 1. 从转出账户扣除
        Double fromAfterBalance = deductBalance(fromStudentId, accountType, amount, null, "transfer_out", "转出给学生ID:" + toStudentId + "," + description);
        
        if (fromAfterBalance == null) {
            throw new RuntimeException("转出账户余额不足");
        }
        
        // 2. 向转入账户充值
        KgPrepaymentAccount toAccount = getOrCreateAccount(toStudentId, accountType);
        BigDecimal beforeBalance = toAccount.getBalance();
        BigDecimal afterBalance = beforeBalance.add(BigDecimal.valueOf(amount));
        
        toAccount.setBalance(afterBalance);
        toAccount.setUpdateTime(new Date());
        int result = updateKgPrepaymentAccount(toAccount);
        
        if (result > 0) {
            // 记录转入流水
            KgPrepaymentTransaction transaction = new KgPrepaymentTransaction();
            transaction.setAccountId(toAccount.getAccountId());
            transaction.setStudentId(toStudentId);
            transaction.setTransactionType("transfer_in");
            transaction.setAmount(BigDecimal.valueOf(amount));
            transaction.setBeforeBalance(beforeBalance);
            transaction.setAfterBalance(afterBalance);
            transaction.setTransactionTime(new Date());
            transaction.setDescription("转入自学生ID:" + fromStudentId + "," + description);
            transaction.setOperatorId(SecurityUtils.getCurrUserId());
            transaction.setComId(SecurityUtils.getCurrComId());
            transaction.setCreateBy(SecurityUtils.getCurrUserId().toString());
            
            prepaymentTransactionService.insertKgPrepaymentTransaction(transaction);
        }
        
        return result;
    }

    /**
     * 冻结/解冻账户
     * 
     * @param accountId 账户ID
     * @param freeze 是否冻结
     * @return 结果
     */
    @Override
    public int freezeAccount(Long accountId, Boolean freeze)
    {
        KgPrepaymentAccount account = selectKgPrepaymentAccountById(accountId);
        if (account != null) {
            account.setStatus(freeze ? "frozen" : "active");
            account.setUpdateTime(new Date());
            return updateKgPrepaymentAccount(account);
        }
        return 0;
    }

    /**
     * 发送余额不足提醒
     * 
     * @return 发送成功的提醒数
     */
    @Override
    public int notifyInsufficientBalance()
    {
        // TODO: 实现余额不足提醒功能
        // 1. 查询余额不足的账户（余额 < 100）
        // 2. 发送微信/短信提醒
        // 3. 记录提醒日志
        return 0;
    }

    /**
     * 扣除预交款余额
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @param amount 扣费金额
     * @param billId 关联账单ID
     * @param billType 账单类型
     * @param description 描述
     * @return 扣费后的余额
     */
    @Override
    @Transactional
    public Double deductBalance(Long studentId, String accountType, Double amount, Long billId, String billType, String description)
    {
        // 1. 获取账户
        KgPrepaymentAccount account = getOrCreateAccount(studentId, accountType);
        
        // 2. 检查余额是否充足
        if (account.getBalance().doubleValue() < amount) {
            throw new RuntimeException("账户余额不足，当前余额:" + account.getBalance() + "，需要扣费:" + amount);
        }
        
        // 3. 扣除余额
        BigDecimal beforeBalance = account.getBalance();
        BigDecimal afterBalance = beforeBalance.subtract(BigDecimal.valueOf(amount));
        
        account.setBalance(afterBalance);
        account.setLastUsageTime(new Date());
        account.setUpdateTime(new Date());
        int result = updateKgPrepaymentAccount(account);
        
        if (result > 0) {
            // 4. 记录扣费流水
            KgPrepaymentTransaction transaction = new KgPrepaymentTransaction();
            transaction.setAccountId(account.getAccountId());
            transaction.setStudentId(studentId);
            transaction.setTransactionType("consume");
            transaction.setAmount(BigDecimal.valueOf(amount));
            transaction.setBeforeBalance(beforeBalance);
            transaction.setAfterBalance(afterBalance);
            transaction.setRelatedBillId(billId);
            transaction.setRelatedBillType(billType);
            transaction.setTransactionTime(new Date());
            transaction.setDescription(description);
            transaction.setOperatorId(SecurityUtils.getCurrUserId());
            transaction.setComId(SecurityUtils.getCurrComId());
            transaction.setCreateBy(SecurityUtils.getCurrUserId().toString());
            
            prepaymentTransactionService.insertKgPrepaymentTransaction(transaction);
            
            return afterBalance.doubleValue();
        }
        
        return null;
    }

    /**
     * 检查账户余额是否充足
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @param amount 需要扣费的金额
     * @return 是否充足
     */
    @Override
    public boolean checkBalanceSufficient(Long studentId, String accountType, Double amount)
    {
        KgPrepaymentAccount account = getOrCreateAccount(studentId, accountType);
        return account.getBalance().doubleValue() >= amount;
    }

    /**
     * 获取或创建学生预交款账户
     * 
     * @param studentId 学生ID
     * @param accountType 账户类型
     * @return 账户信息
     */
    @Override
    public KgPrepaymentAccount getOrCreateAccount(Long studentId, String accountType)
    {
        // 1. 先尝试查询已存在的账户
        KgPrepaymentAccount queryAccount = new KgPrepaymentAccount();
        queryAccount.setStudentId(studentId);
        queryAccount.setAccountType(accountType);
        queryAccount.setComId(SecurityUtils.getCurrComId());
        
        List<KgPrepaymentAccount> accounts = selectKgPrepaymentAccountList(queryAccount);
        
        if (!accounts.isEmpty()) {
            return accounts.get(0);
        }
        
        // 2. 账户不存在，创建新账户
        KgPrepaymentAccount newAccount = new KgPrepaymentAccount();
        newAccount.setStudentId(studentId);
        newAccount.setAccountType(accountType);
        newAccount.setBalance(BigDecimal.ZERO);
        newAccount.setFrozenAmount(BigDecimal.ZERO);
        newAccount.setStatus("active");
        newAccount.setCreateTime(new Date());
        newAccount.setComId(SecurityUtils.getCurrComId());
        
        int result = insertKgPrepaymentAccount(newAccount);
        if (result > 0) {
            return newAccount;
        }
        
        throw new RuntimeException("创建预交款账户失败");
    }
}
