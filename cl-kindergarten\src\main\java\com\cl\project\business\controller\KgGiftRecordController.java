package com.cl.project.business.controller;

import java.util.List;

import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.project.business.domain.KgGiftRecord;
import com.cl.project.business.service.IKgGiftRecordService;

/**
 * 赠送记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/business/gift/record")
public class KgGiftRecordController extends BaseController
{
    @Autowired
    private IKgGiftRecordService kgGiftRecordService;

    /**
     * 查询赠送记录列表
     */
//    @SaCheckPermission("business:gift:record:list")
    @GetMapping("/list")
    public TableDataInfo list(KgGiftRecord kgGiftRecord)
    {
        startPage();
        List<KgGiftRecord> list = kgGiftRecordService.selectKgGiftRecordList(kgGiftRecord);
        return getDataTable(list);
    }


    /**
     * 获取赠送记录详细信息
     */
//    @SaCheckPermission("business:gift:record:query")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return AjaxResult.success(kgGiftRecordService.selectKgGiftRecordByRecordId(recordId));
    }

    /**
     * 新增赠送记录
     */
//    @SaCheckPermission("business:gift:record:add")
    @Log(title = "赠送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgGiftRecord kgGiftRecord)
    {
        return toAjax(kgGiftRecordService.insertKgGiftRecord(kgGiftRecord));
    }

    /**
     * 修改赠送记录
     */
//    @SaCheckPermission("business:gift:record:edit")
    @Log(title = "赠送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgGiftRecord kgGiftRecord)
    {
        return toAjax(kgGiftRecordService.updateKgGiftRecord(kgGiftRecord));
    }

    /**
     * 删除赠送记录
     */
//    @SaCheckPermission("business:gift:record:remove")
    @Log(title = "赠送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(kgGiftRecordService.deleteKgGiftRecordByRecordIds(recordIds));
    }

    /**
     * 查询学生的有效赠送记录
     */
//    @SaCheckPermission("business:gift:record:list")
    @GetMapping("/student/{studentId}")
    public AjaxResult getStudentGiftRecords(@PathVariable("studentId") Long studentId)
    {
        List<KgGiftRecord> list = kgGiftRecordService.selectActiveGiftRecordsByStudentId(studentId);
        return AjaxResult.success(list);
    }

    /**
     * 更新赠送记录的使用课时
     */
//    @SaCheckPermission("business:gift:record:edit")
    @Log(title = "更新赠送记录使用课时", businessType = BusinessType.UPDATE)
    @PutMapping("/{recordId}/useSessions/{usedSessions}")
    public AjaxResult updateUsedSessions(@PathVariable("recordId") Long recordId, 
                                       @PathVariable("usedSessions") Integer usedSessions)
    {
        return toAjax(kgGiftRecordService.updateGiftRecordUsedSessions(recordId, usedSessions));
    }
}
