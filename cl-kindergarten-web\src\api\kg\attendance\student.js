import request from '@/utils/request'

// 查询学生考勤列表
export function listStudentAttendance(query) {
  return request({
    url: '/business/student-attendance/list',
    method: 'get',
    params: query
  })
}

// 查询学生考勤详细
export function getStudentAttendance(attendanceId) {
  return request({
    url: '/business/student-attendance/' + attendanceId,
    method: 'get'
  })
}

// 新增学生考勤
export function addStudentAttendance(data) {
  return request({
    url: '/business/student-attendance',
    method: 'post',
    data: data
  })
}

// 修改学生考勤
export function updateStudentAttendance(data) {
  return request({
    url: '/business/student-attendance',
    method: 'put',
    data: data
  })
}

// 删除学生考勤
export function delStudentAttendance(attendanceId) {
  return request({
    url: '/business/student-attendance/' + attendanceId,
    method: 'delete'
  })
}

// 导出学生考勤
export function exportStudentAttendance(query) {
  return request({
    url: '/business/student-attendance/export',
    method: 'get',
    params: query
  })
}

// 学生签到
export function studentCheckin(data) {
  return request({
    url: '/business/student-attendance/checkin',
    method: 'post',
    data: data
  })
}

// 学生签退
export function studentCheckout(data) {
  return request({
    url: '/business/student-attendance/checkout',
    method: 'post',
    data: data
  })
}

// 考勤确认（单个）
export function confirmStudentAttendance(attendanceId) {
  return request({
    url: '/business/student-attendance/confirm/' + attendanceId,
    method: 'post'
  })
}

// 缺勤登记
export function registerAbsence(data) {
  return request({
    url: '/business/student-attendance/absence',
    method: 'post',
    data: data
  })
}

// 查询学生考勤概览
export function getStudentAttendanceOverview(query) {
  return request({
    url: '/business/student-attendance/overview',
    method: 'get',
    params: query
  })
}

// 获取钉钉原始打卡数据
export function getDingtalkAttendanceRecords(query) {
  return request({
    url: '/business/dingtalk-attendance/list',
    method: 'get',
    params: query
  })
}

// 批量学生签到
export function batchStudentCheckin(data) {
  return request({
    url: '/business/student-attendance/batchCheckin',
    method: 'post',
    data: data
  })
}

// 批量确认学生考勤
export function batchConfirmStudentAttendance(data) {
  return request({
    url: '/business/student-attendance/batchConfirm',
    method: 'post',
    data: data
  })
}

// 考勤确认（批量，兼容旧接口）
export function confirmStudentAttendanceBatch(attendanceIds) {
  return request({
    url: '/business/student-attendance/confirm',
    method: 'post',
    data: { attendanceIds: attendanceIds }
  })
}


