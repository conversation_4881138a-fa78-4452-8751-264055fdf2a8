<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="消息类型" prop="messageType">
        <el-select v-model="queryParams.messageType" placeholder="请选择消息类型" clearable>
          <el-option label="园费账单" value="tuition_bill"/>
          <el-option label="托管费账单" value="course_bill"/>
          <el-option label="考勤通知" value="attendance_notice"/>
        </el-select>
      </el-form-item>
      <el-form-item label="接收者类型" prop="recipientType">
        <el-select v-model="queryParams.recipientType" placeholder="请选择接收者类型" clearable>
          <el-option label="学生" value="student"/>
          <el-option label="教师" value="teacher"/>
          <el-option label="家长" value="parent"/>
        </el-select>
      </el-form-item>
      <el-form-item label="推送状态" prop="pushStatus">
        <el-select v-model="queryParams.pushStatus" placeholder="请选择推送状态" clearable>
          <el-option label="待推送" value="pending"/>
          <el-option label="成功" value="success"/>
          <el-option label="失败" value="failed"/>
        </el-select>
      </el-form-item>
      <el-form-item label="推送时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 推送统计概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalMessages || 0 }}</div>
            <div class="stat-label">总消息数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value success">{{ statistics.successCount || 0 }}</div>
            <div class="stat-label">发送成功</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value warning">{{ statistics.pendingCount || 0 }}</div>
            <div class="stat-label">待发送</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value danger">{{ statistics.failedCount || 0 }}</div>
            <div class="stat-label">发送失败</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:push:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:push:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:push:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:push:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-s-promotion"
          :disabled="multiple"
          @click="handleSendPush"
          v-hasPermi="['business:push:send']"
        >发送消息</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          :disabled="multiple"
          @click="handleResendFailed"
          v-hasPermi="['business:push:resend']"
        >重发失败</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-check"
          :disabled="multiple"
          @click="handleMarkRead"
          v-hasPermi="['business:push:markRead']"
        >标记已读</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <el-table v-loading="loading" :data="pushList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="推送ID" align="center" prop="pushId" />
      <el-table-column label="消息类型" align="center" prop="messageType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.messageType === 'tuition_bill'" type="success">园费账单</el-tag>
          <el-tag v-else-if="scope.row.messageType === 'course_bill'" type="primary">托管费账单</el-tag>
          <el-tag v-else-if="scope.row.messageType === 'attendance_notice'" type="warning">考勤通知</el-tag>
          <el-tag v-else type="info">{{ scope.row.messageType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="接收者类型" align="center" prop="recipientType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.recipientType === 'student'" type="success">学生</el-tag>
          <el-tag v-else-if="scope.row.recipientType === 'teacher'" type="primary">教师</el-tag>
          <el-tag v-else-if="scope.row.recipientType === 'parent'" type="warning">家长</el-tag>
          <el-tag v-else type="info">{{ scope.row.recipientType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="接收者ID" align="center" prop="recipientId" />
      <el-table-column label="消息标题" align="center" prop="title" min-width="150" show-overflow-tooltip />
      <el-table-column label="推送时间" align="center" prop="pushTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.pushTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推送状态" align="center" prop="pushStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.pushStatus === 'pending'" type="info">待推送</el-tag>
          <el-tag v-else-if="scope.row.pushStatus === 'success'" type="success">成功</el-tag>
          <el-tag v-else-if="scope.row.pushStatus === 'failed'" type="danger">失败</el-tag>
          <el-tag v-else type="info">{{ scope.row.pushStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="阅读状态" align="center" prop="readStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.readStatus === 0" type="warning">未读</el-tag>
          <el-tag v-else-if="scope.row.readStatus === 1" type="success">已读</el-tag>
          <el-tag v-else type="info">-</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['business:push:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:push:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:push:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.pushStatus === 'pending'"
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handleSendSingle(scope.row)"
            v-hasPermi="['business:push:send']"
          >发送</el-button>
          <el-button
            v-if="scope.row.pushStatus === 'failed'"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleResendSingle(scope.row)"
            v-hasPermi="['business:push:resend']"
          >重发</el-button>
          <el-button
            v-if="scope.row.readStatus === 0"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleMarkReadSingle(scope.row)"
            v-hasPermi="['business:push:markRead']"
          >已读</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改消息推送记录对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="消息类型" prop="messageType">
          <el-select v-model="form.messageType" placeholder="请选择消息类型">
            <el-option label="园费账单" value="tuition_bill"/>
            <el-option label="托管费账单" value="course_bill"/>
            <el-option label="考勤通知" value="attendance_notice"/>
          </el-select>
        </el-form-item>
        <el-form-item label="接收者类型" prop="recipientType">
          <el-select v-model="form.recipientType" placeholder="请选择接收者类型">
            <el-option label="学生" value="student"/>
            <el-option label="教师" value="teacher"/>
            <el-option label="家长" value="parent"/>
          </el-select>
        </el-form-item>
        <el-form-item label="接收者ID" prop="recipientId">
          <el-input v-model="form.recipientId" placeholder="请输入接收者ID" />
        </el-form-item>
        <el-form-item label="微信openid" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入微信openid" />
        </el-form-item>
        <el-form-item label="消息标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入消息标题" />
        </el-form-item>
        <el-form-item label="消息内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入消息内容" />
        </el-form-item>
        <el-form-item label="模板ID" prop="templateId">
          <el-input v-model="form.templateId" placeholder="请输入模板ID" />
        </el-form-item>
        <el-form-item label="推送状态" prop="pushStatus">
          <el-select v-model="form.pushStatus" placeholder="请选择推送状态">
            <el-option label="待推送" value="pending"/>
            <el-option label="成功" value="success"/>
            <el-option label="失败" value="failed"/>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看消息推送记录详情对话框 -->
    <el-dialog title="消息推送记录详情" v-model="viewOpen" width="800px" append-to-body>
      <el-form :model="viewForm" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="推送ID：">
              <span>{{ viewForm.pushId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="消息类型：">
              <el-tag v-if="viewForm.messageType === 'tuition_bill'" type="success">园费账单</el-tag>
              <el-tag v-else-if="viewForm.messageType === 'course_bill'" type="primary">托管费账单</el-tag>
              <el-tag v-else-if="viewForm.messageType === 'attendance_notice'" type="warning">考勤通知</el-tag>
              <el-tag v-else type="info">{{ viewForm.messageType }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="接收者类型：">
              <el-tag v-if="viewForm.recipientType === 'student'" type="success">学生</el-tag>
              <el-tag v-else-if="viewForm.recipientType === 'teacher'" type="primary">教师</el-tag>
              <el-tag v-else-if="viewForm.recipientType === 'parent'" type="warning">家长</el-tag>
              <el-tag v-else type="info">{{ viewForm.recipientType }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收者ID：">
              <span>{{ viewForm.recipientId }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="微信openid：">
              <span>{{ viewForm.openid }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="消息标题：">
              <span>{{ viewForm.title }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="消息内容：">
              <div class="message-content">{{ viewForm.content }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板ID：">
              <span>{{ viewForm.templateId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推送时间：">
              <span>{{ parseTime(viewForm.pushTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="推送状态：">
              <el-tag v-if="viewForm.pushStatus === 'pending'" type="info">待推送</el-tag>
              <el-tag v-else-if="viewForm.pushStatus === 'success'" type="success">成功</el-tag>
              <el-tag v-else-if="viewForm.pushStatus === 'failed'" type="danger">失败</el-tag>
              <el-tag v-else type="info">{{ viewForm.pushStatus }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="阅读状态：">
              <el-tag v-if="viewForm.readStatus === 0" type="warning">未读</el-tag>
              <el-tag v-else-if="viewForm.readStatus === 1" type="success">已读</el-tag>
              <el-tag v-else type="info">-</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="viewForm.readTime">
          <el-col :span="12">
            <el-form-item label="阅读时间：">
              <span>{{ parseTime(viewForm.readTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="viewForm.errorMessage">
          <el-col :span="24">
            <el-form-item label="错误信息：">
              <div class="error-message">{{ viewForm.errorMessage }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="viewForm.remark">
          <el-col :span="24">
            <el-form-item label="备注：">
              <div>{{ viewForm.remark }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="MessagePush">
import { 
  listPush, 
  getPush, 
  delPush, 
  addPush, 
  updatePush, 
  exportPush,
  sendPushMessage,
  resendFailedPush,
  markPushAsRead,
  getPushStatistics
} from "@/api/kg/system/push";

export default {
  name: "MessagePush",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 消息推送记录表格数据
      pushList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        messageType: null,
        recipientType: null,
        recipientId: null,
        pushStatus: null,
        pushTimeStart: null,
        pushTimeEnd: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 统计数据
      statistics: {
        totalMessages: 0,
        successCount: 0,
        pendingCount: 0,
        failedCount: 0
      },
      // 表单校验
      rules: {
        messageType: [
          { required: true, message: "消息类型不能为空", trigger: "change" }
        ],
        recipientType: [
          { required: true, message: "接收者类型不能为空", trigger: "change" }
        ],
        recipientId: [
          { required: true, message: "接收者ID不能为空", trigger: "blur" }
        ],
        title: [
          { required: true, message: "消息标题不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "消息内容不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadStatistics();
  },
  methods: {
    /** 查询消息推送记录列表 */
    getList() {
      this.loading = true;
      listPush(this.queryParams).then(response => {
        this.pushList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        pushId: null,
        messageType: null,
        recipientType: null,
        recipientId: null,
        openid: null,
        title: null,
        content: null,
        templateId: null,
        pushStatus: "pending",
        errorMessage: null,
        readStatus: null,
        readTime: null,
        remark: null
      };
      this.resetForm("formRef");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.handleDateChange([]);
      this.resetForm("queryRef");
      this.handleQuery();
    },
    /** 日期范围变化处理 */
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.queryParams.pushTimeStart = value[0];
        this.queryParams.pushTimeEnd = value[1];
      } else {
        this.queryParams.pushTimeStart = null;
        this.queryParams.pushTimeEnd = null;
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pushId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加消息推送记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const pushId = row.pushId || this.ids;
      getPush(pushId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改消息推送记录";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getPush(row.pushId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["formRef"].validate(valid => {
        if (valid) {
          if (this.form.pushId != null) {
            updatePush(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPush(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const pushIds = row.pushId || this.ids;
      this.$confirm('是否确认删除消息推送记录编号为"' + pushIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delPush(pushIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有消息推送记录数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportPush(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    },
    /** 批量发送消息 */
    handleSendPush() {
      const pushIds = this.ids;
      this.$confirm(`确认发送选中的 ${pushIds.length} 条消息吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return sendPushMessage(pushIds);
      }).then(response => {
        this.msgSuccess(`成功发送 ${response.data} 条消息`);
        this.getList();
      }).catch(() => {});
    },
    /** 单个发送消息 */
    handleSendSingle(row) {
      this.$confirm(`确认发送消息“${row.title}”吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return sendPushMessage([row.pushId]);
      }).then(() => {
        this.msgSuccess("发送成功");
        this.getList();
      }).catch(() => {});
    },
    /** 批量重发失败消息 */
    handleResendFailed() {
      const pushIds = this.ids;
      this.$confirm(`确认重新发送选中的 ${pushIds.length} 条失败消息吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return resendFailedPush(pushIds);
      }).then(response => {
        this.msgSuccess(`成功重发 ${response.data} 条消息`);
        this.getList();
      }).catch(() => {});
    },
    /** 单个重发失败消息 */
    handleResendSingle(row) {
      this.$confirm(`确认重新发送消息“${row.title}”吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return resendFailedPush([row.pushId]);
      }).then(() => {
        this.msgSuccess("重发成功");
        this.getList();
      }).catch(() => {});
    },
    /** 批量标记已读 */
    handleMarkRead() {
      const pushIds = this.ids;
      this.$confirm(`确认标记选中的 ${pushIds.length} 条消息为已读吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        return markPushAsRead(pushIds);
      }).then(response => {
        this.msgSuccess(`成功标记 ${response.data} 条消息为已读`);
        this.getList();
      }).catch(() => {});
    },
    /** 单个标记已读 */
    handleMarkReadSingle(row) {
      markPushAsRead([row.pushId]).then(() => {
        this.msgSuccess("标记已读成功");
        this.getList();
      });
    },
    /** 加载统计数据 */
    loadStatistics() {
      getPushStatistics(this.queryParams).then(response => {
        this.statistics = response.data || {
          totalMessages: 0,
          successCount: 0,
          pendingCount: 0,
          failedCount: 0
        };
      }).catch(() => {
        console.warn('Failed to load push statistics');
      });
    }
  }
};
</script>

<style scoped>
.message-content {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.error-message {
  color: #F56C6C;
  background-color: #fef0f0;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.warning {
  color: #E6A23C;
}

.stat-value.danger {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
