import request from '@/utils/request'

// 查询教师考勤列表
export function listTeacherAttendance(query) {
  return request({
    url: '/business/teacher-attendance/list',
    method: 'get',
    params: query
  })
}

// 查询教师考勤详细
export function getTeacherAttendance(attendanceId) {
  return request({
    url: '/business/teacher-attendance/' + attendanceId,
    method: 'get'
  })
}

// 新增教师考勤
export function addTeacherAttendance(data) {
  return request({
    url: '/business/teacher-attendance',
    method: 'post',
    data: data
  })
}

// 修改教师考勤
export function updateTeacherAttendance(data) {
  return request({
    url: '/business/teacher-attendance',
    method: 'put',
    data: data
  })
}

// 删除教师考勤
export function delTeacherAttendance(attendanceId) {
  return request({
    url: '/business/teacher-attendance/' + attendanceId,
    method: 'delete'
  })
}

// 导出教师考勤
export function exportTeacherAttendance(query) {
  return request({
    url: '/business/teacher-attendance/export',
    method: 'get',
    params: query
  })
}

// 教师签到
export function teacherCheckin(data) {
  return request({
    url: '/business/teacher-attendance/checkin',
    method: 'post',
    data: data
  })
}

// 教师签退
export function teacherCheckout(data) {
  return request({
    url: '/business/teacher-attendance/checkout',
    method: 'post',
    data: data
  })
}


// 查询教师考勤概览
export function getTeacherAttendanceOverview(query) {
  return request({
    url: '/business/teacher-attendance/overview',
    method: 'get',
    params: query
  })
}

// 获取钉钉原始打卡数据
export function getDingtalkAttendanceRecords(query) {
  return request({
    url: '/business/dingtalk-attendance/list',
    method: 'get',
    params: query
  })
}

// 批量教师签到
export function batchTeacherCheckin(data) {
  return request({
    url: '/business/teacher-attendance/batchCheckin',
    method: 'post',
    data: data
  })
}


// 批量确认教师考勤
export function batchConfirmAttendance(data) {
  return request({
    url: '/business/teacher-attendance/batchConfirm',
    method: 'post',
    data: data
  })
}
