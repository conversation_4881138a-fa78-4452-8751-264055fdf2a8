<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgPrepaymentAccountMapper">

    <resultMap type="KgPrepaymentAccount" id="KgPrepaymentAccountResult">
        <result property="accountId"           column="account_id"           />
        <result property="studentId"          column="student_id"           />
        <result property="accountType"        column="account_type"         />
        <result property="totalPrepaid"       column="total_prepaid"       />
        <result property="totalUsed"          column="total_used"          />
        <result property="balance"             column="current_balance"      />
        <result property="frozenAmount"       column="frozen_amount"       />
        <result property="availableBalance"   column="available_balance"   />
        <result property="lastRechargeTime"   column="last_recharge_time"   />
        <result property="lastUsageTime"      column="last_usage_time"      />
        <result property="status"             column="account_status"       />
        <result property="comId"              column="com_id"              />
        <result property="createBy"           column="create_by"           />
        <result property="createTime"         column="create_time"         />
        <result property="updateBy"           column="update_by"           />
        <result property="updateTime"         column="update_time"         />
        <result property="remark"             column="remark"             />
    </resultMap>

    <sql id="selectKgPrepaymentAccountVo">
        select account_id, student_id, account_type, total_prepaid, total_used, current_balance, frozen_amount, available_balance, last_recharge_time, last_usage_time, account_status, com_id, create_by, create_time, update_by, update_time, remark from kg_prepayment_account
    </sql>

    <select id="selectKgPrepaymentAccountList" parameterType="KgPrepaymentAccount" resultMap="KgPrepaymentAccountResult">
        <include refid="selectKgPrepaymentAccountVo"/>
        <where>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="accountType != null  and accountType != ''"> and account_type = #{accountType}</if>
            <if test="status != null  and status != ''"> and account_status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectKgPrepaymentAccountById" parameterType="Long" resultMap="KgPrepaymentAccountResult">
        <include refid="selectKgPrepaymentAccountVo"/>
        where account_id = #{accountId}
    </select>

    <insert id="insertKgPrepaymentAccount" parameterType="KgPrepaymentAccount" useGeneratedKeys="true" keyProperty="accountId">
        insert into kg_prepayment_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="accountType != null and accountType != ''">account_type,</if>
            <if test="totalPrepaid != null">total_prepaid,</if>
            <if test="totalUsed != null">total_used,</if>
            <if test="balance != null">current_balance,</if>
            <if test="frozenAmount != null">frozen_amount,</if>
            <if test="availableBalance != null">available_balance,</if>
            <if test="lastRechargeTime != null">last_recharge_time,</if>
            <if test="lastUsageTime != null">last_usage_time,</if>
            <if test="status != null and status != ''">account_status,</if>
            <if test="comId != null and comId != ''">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="accountType != null and accountType != ''">#{accountType},</if>
            <if test="totalPrepaid != null">#{totalPrepaid},</if>
            <if test="totalUsed != null">#{totalUsed},</if>
            <if test="balance != null">#{balance},</if>
            <if test="frozenAmount != null">#{frozenAmount},</if>
            <if test="availableBalance != null">#{availableBalance},</if>
            <if test="lastRechargeTime != null">#{lastRechargeTime},</if>
            <if test="lastUsageTime != null">#{lastUsageTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="comId != null and comId != ''">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateKgPrepaymentAccount" parameterType="KgPrepaymentAccount">
        update kg_prepayment_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="totalPrepaid != null">total_prepaid = #{totalPrepaid},</if>
            <if test="totalUsed != null">total_used = #{totalUsed},</if>
            <if test="balance != null">current_balance = #{balance},</if>
            <if test="frozenAmount != null">frozen_amount = #{frozenAmount},</if>
            <if test="availableBalance != null">available_balance = #{availableBalance},</if>
            <if test="lastRechargeTime != null">last_recharge_time = #{lastRechargeTime},</if>
            <if test="lastUsageTime != null">last_usage_time = #{lastUsageTime},</if>
            <if test="status != null and status != ''">account_status = #{status},</if>
            <if test="comId != null and comId != ''">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where account_id = #{accountId}
    </update>

    <delete id="deleteKgPrepaymentAccountById" parameterType="Long">
        delete from kg_prepayment_account where account_id = #{accountId}
    </delete>

    <delete id="deleteKgPrepaymentAccountByIds" parameterType="String">
        delete from kg_prepayment_account where account_id in 
        <foreach item="accountId" collection="array" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </delete>

    <!-- 查询预交款余额列表 -->
    <select id="selectBalanceList" parameterType="com.cl.project.business.dto.PrepaymentBalanceDto" resultType="com.cl.project.business.dto.PrepaymentBalanceDto">
        SELECT 
            s.student_id AS studentId,
            s.student_name AS studentName,
            s.student_code AS studentCode,
            c.class_name AS className,
            COALESCE(t_acc.current_balance, 0) AS tuitionBalance,
            COALESCE(c_acc.current_balance, 0) AS courseBalance,
            (COALESCE(t_acc.current_balance, 0) + COALESCE(c_acc.current_balance, 0)) AS totalBalance,
            COALESCE(t_acc.total_prepaid, 0) AS tuitionPrepaid,
            COALESCE(c_acc.total_prepaid, 0) AS coursePrepaid,
            COALESCE(t_acc.total_used, 0) AS tuitionUsed,
            COALESCE(c_acc.total_used, 0) AS courseUsed,
            COALESCE(t_acc.last_recharge_time, c_acc.last_recharge_time) AS lastRechargeTime,
            COALESCE(t_acc.last_usage_time, c_acc.last_usage_time) AS lastUsageTime
        FROM kg_student s
        LEFT JOIN kg_class c ON s.class_id = c.class_id
        LEFT JOIN kg_prepayment_account t_acc ON s.student_id = t_acc.student_id AND t_acc.account_type = 'tuition' AND t_acc.com_id = #{comId}
        LEFT JOIN kg_prepayment_account c_acc ON s.student_id = c_acc.student_id AND c_acc.account_type = 'course' AND c_acc.com_id = #{comId}
        WHERE s.com_id = #{comId}
        <if test="studentName != null and studentName != ''">
            AND s.student_name LIKE CONCAT('%', #{studentName}, '%')
        </if>
        <if test="classId != null">
            AND s.class_id = #{classId}
        </if>
        <if test="accountType != null and accountType != ''">
            <if test="accountType == 'tuition'">
                AND t_acc.account_id IS NOT NULL
            </if>
            <if test="accountType == 'course'">
                AND c_acc.account_id IS NOT NULL
            </if>
        </if>
        <if test="balanceStatus != null and balanceStatus != ''">
            <if test="balanceStatus == 'sufficient'">
                AND (COALESCE(t_acc.current_balance, 0) &gt;= 100 OR COALESCE(c_acc.current_balance, 0) &gt;= 100)
            </if>
            <if test="balanceStatus == 'insufficient'">
                AND (COALESCE(t_acc.current_balance, 0) &gt; 0 AND COALESCE(t_acc.current_balance, 0) &lt; 100) 
                 OR (COALESCE(c_acc.current_balance, 0) &gt; 0 AND COALESCE(c_acc.current_balance, 0) &lt; 100)
            </if>
            <if test="balanceStatus == 'negative'">
                AND (COALESCE(t_acc.current_balance, 0) &lt;= 0 OR COALESCE(c_acc.current_balance, 0) &lt;= 0)
            </if>
        </if>
        ORDER BY s.student_id DESC
    </select>

    <!-- 获取余额统计 -->
    <select id="getBalanceStatistics" resultType="com.cl.project.business.dto.PrepaymentStatisticsDto">
        <![CDATA[
        SELECT 
            COALESCE(SUM(CASE WHEN account_type = 'tuition' THEN current_balance END), 0) AS tuitionTotal,
            COUNT(DISTINCT CASE WHEN account_type = 'tuition' THEN student_id END) AS tuitionStudentCount,
            COALESCE(SUM(CASE WHEN account_type = 'course' THEN current_balance END), 0) AS courseTotal,
            COUNT(DISTINCT CASE WHEN account_type = 'course' THEN student_id END) AS courseStudentCount,
            COUNT(DISTINCT CASE WHEN current_balance > 0 AND current_balance < 100 THEN student_id END) AS insufficientCount,
            COUNT(DISTINCT CASE WHEN current_balance <= 0 THEN student_id END) AS negativeCount,
            COALESCE(SUM(current_balance), 0) AS totalBalance,
            COUNT(DISTINCT student_id) AS totalStudentCount
        FROM kg_prepayment_account 
        WHERE com_id = #{comId}
        ]]>
    </select>



    <!-- 获取学生余额详情 -->
    <select id="getStudentBalanceDetail" resultType="com.cl.project.business.dto.PrepaymentBalanceDto">
        SELECT 
            s.student_id,
            s.student_name,
            s.student_code,
            c.class_name,
            COALESCE(t_acc.total_prepaid, 0) AS tuitionPrepaid,
            COALESCE(t_acc.total_used, 0) AS tuitionUsed,
            COALESCE(t_acc.current_balance, 0) AS tuitionBalance,
            COALESCE(c_acc.total_prepaid, 0) AS coursePrepaid,
            COALESCE(c_acc.total_used, 0) AS courseUsed,
            COALESCE(c_acc.current_balance, 0) AS courseBalance,
            (COALESCE(t_acc.current_balance, 0) + COALESCE(c_acc.current_balance, 0)) AS totalBalance,
            COALESCE(t_acc.last_recharge_time, c_acc.last_recharge_time) AS lastRechargeTime,
            COALESCE(t_acc.last_usage_time, c_acc.last_usage_time) AS lastUsageTime
        FROM kg_student s
        LEFT JOIN kg_class c ON s.class_id = c.class_id
        LEFT JOIN kg_prepayment_account t_acc ON s.student_id = t_acc.student_id AND t_acc.account_type = 'tuition'
        LEFT JOIN kg_prepayment_account c_acc ON s.student_id = c_acc.student_id AND c_acc.account_type = 'course'
        WHERE s.student_id = #{studentId}
    </select>

</mapper>
