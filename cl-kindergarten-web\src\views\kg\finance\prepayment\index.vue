<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班级" prop="classId">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable>
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账户类型" prop="accountType">
        <el-select v-model="queryParams.accountType" placeholder="请选择账户类型" clearable>
          <el-option label="园费账户" value="tuition" />
          <el-option label="托管费账户" value="course" />
        </el-select>
      </el-form-item>
      <el-form-item label="余额状态" prop="balanceStatus">
        <el-select v-model="queryParams.balanceStatus" placeholder="请选择余额状态" clearable>
          <el-option label="充足" value="sufficient" />
          <el-option label="不足" value="insufficient" />
          <el-option label="负数" value="negative" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card tuition-stats">
          <div class="stats-icon">
            <i class="el-icon-wallet"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">园费预交款总额</div>
            <div class="stats-value">{{ formatMoney((statistics && statistics.tuitionTotal) || 0) }}</div>
            <div class="stats-desc">{{ (statistics && statistics.tuitionStudentCount) || 0 }}名学生</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card course-stats">
          <div class="stats-icon">
            <i class="el-icon-school"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">托管费预交款总额</div>
            <div class="stats-value">{{ formatMoney((statistics && statistics.courseTotal) || 0) }}</div>
            <div class="stats-desc">{{ (statistics && statistics.courseStudentCount) || 0 }}名学生</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card warning-stats">
          <div class="stats-icon">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">余额不足学生</div>
            <div class="stats-value">{{ (statistics && statistics.insufficientCount) || 0 }}</div>
            <div class="stats-desc">需要提醒充值</div>
          </div>
        </div>
      </el-col>
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
        <div class="stats-card negative-stats">
          <div class="stats-icon">
            <i class="el-icon-remove"></i>
          </div>
          <div class="stats-content">
            <div class="stats-title">负余额学生</div>
            <div class="stats-value">{{ (statistics && statistics.negativeCount) || 0 }}</div>
            <div class="stats-desc">需要补交费用</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleRecharge"
          v-hasPermi="['kg:prepayment:recharge']"
        >充值</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-money"
          size="mini"
          @click="handleBatchDeduct"
          :disabled="multiple"
          v-hasPermi="['kg:prepayment:deduct']"
        >批量扣费</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-message"
          size="mini"
          @click="handleNotifyRecharge"
          v-hasPermi="['kg:prepayment:notify']"
        >余额提醒</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:prepayment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 预交款余额列表 -->
    <el-table v-loading="loading" :data="balanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生信息" align="left" width="180">
        <template slot-scope="scope">
          <div class="student-info">
            <div class="student-name">{{ scope.row.studentName }}</div>
            <div class="student-meta">
              <el-tag size="mini" type="info">{{ scope.row.className }}</el-tag>
              <span class="student-code">{{ scope.row.studentCode }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 园费预交款 -->
      <el-table-column label="园费预交款" align="center" width="200">
        <template slot-scope="scope">
          <div class="balance-info">
            <div class="balance-amount" :class="getBalanceClass(scope.row.tuitionBalance)">
              {{ formatMoney(scope.row.tuitionBalance) }}
            </div>
            <div class="balance-status">
              <el-tag 
                size="mini" 
                :type="getBalanceTagType(scope.row.tuitionBalance)"
              >
                {{ getBalanceStatus(scope.row.tuitionBalance) }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 托管费预交款 -->
      <el-table-column label="托管费预交款" align="center" width="200">
        <template slot-scope="scope">
          <div class="balance-info">
            <div class="balance-amount" :class="getBalanceClass(scope.row.courseBalance)">
              {{ formatMoney(scope.row.courseBalance) }}
            </div>
            <div class="balance-status">
              <el-tag 
                size="mini" 
                :type="getBalanceTagType(scope.row.courseBalance)"
              >
                {{ getBalanceStatus(scope.row.courseBalance) }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 总余额 -->
      <el-table-column label="总余额" align="center" width="150">
        <template slot-scope="scope">
          <div class="total-balance" :class="getBalanceClass(scope.row.totalBalance)">
            {{ formatMoney(scope.row.totalBalance) }}
          </div>
        </template>
      </el-table-column>

      <!-- 最后充值时间 -->
      <el-table-column label="最后充值时间" align="center" prop="lastRechargeTime" width="155">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastRechargeTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click="handleRechargeStudent(scope.row)"
            v-hasPermi="['kg:prepayment:recharge']"
          >充值</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['kg:prepayment:view']"
          >详情</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['kg:prepayment:manage']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="transaction" icon="el-icon-tickets">流水查询</el-dropdown-item>
              <el-dropdown-item command="freeze" icon="el-icon-lock">冻结账户</el-dropdown-item>
              <el-dropdown-item command="transfer" icon="el-icon-sort">余额转移</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 充值对话框 -->
    <el-dialog title="预交款充值" :visible.sync="rechargeOpen" width="500px" append-to-body>
      <el-form ref="rechargeForm" :model="rechargeForm" :rules="rechargeRules" label-width="100px">
        <el-form-item label="学生" prop="studentId">
          <el-select v-model="rechargeForm.studentId" placeholder="请选择学生" style="width: 100%">
            <el-option
              v-for="item in studentList"
              :key="item.studentId"
              :label="item.studentName + '(' + item.className + ')'"
              :value="item.studentId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="账户类型" prop="accountType">
          <el-radio-group v-model="rechargeForm.accountType">
            <el-radio label="tuition">园费账户</el-radio>
            <el-radio label="course">托管费账户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="充值金额" prop="amount">
          <el-input-number 
            v-model="rechargeForm.amount" 
            :precision="2" 
            :step="100" 
            :min="0.01" 
            :max="99999.99"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="rechargeForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option label="微信" value="wechat" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="现金" value="cash" />
            <el-option label="银行转账" value="bank" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="rechargeForm.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRecharge">确 定</el-button>
        <el-button @click="cancelRecharge">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 余额详情对话框 -->
    <el-dialog title="余额详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="学生姓名">{{ detailData.studentName }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ detailData.className }}</el-descriptions-item>
        <el-descriptions-item label="学号">{{ detailData.studentCode }}</el-descriptions-item>
        <el-descriptions-item label="园费余额" :span="2">
          <span :class="getBalanceClass(detailData.tuitionBalance)">
            {{ formatMoney(detailData.tuitionBalance) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="托管费余额" :span="2">
          <span :class="getBalanceClass(detailData.courseBalance)">
            {{ formatMoney(detailData.courseBalance) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="最后充值时间">{{ parseTime(detailData.lastRechargeTime) }}</el-descriptions-item>
        <el-descriptions-item label="最后使用时间">{{ parseTime(detailData.lastUsageTime) }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 最近流水 -->
      <div class="recent-transactions">
        <h4>最近流水记录</h4>
        <el-table :data="detailData.recentTransactions" size="mini">
          <el-table-column label="交易时间" prop="transactionTime" width="140">
            <template slot-scope="scope">
              {{ parseTime(scope.row.transactionTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="交易类型" prop="transactionType" width="80">
            <template slot-scope="scope">
              <el-tag :type="getTransactionTypeTag(scope.row.transactionType)" size="mini">
                {{ getTransactionTypeName(scope.row.transactionType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="金额" prop="amount" width="100">
            <template slot-scope="scope">
              <span :class="scope.row.transactionType === 'recharge' ? 'text-success' : 'text-danger'">
                {{ scope.row.transactionType === 'recharge' ? '+' : '-' }}{{ formatMoney(scope.row.amount) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="余额" prop="afterBalance" width="100">
            <template slot-scope="scope">
              {{ formatMoney(scope.row.afterBalance) }}
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description" show-overflow-tooltip />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBalanceList, getBalanceStatistics, rechargeBalance, batchNotifyRecharge } from "@/api/kg/prepayment";
import { listAllStudent } from "@/api/kg/student/info";
import { listClass } from "@/api/kg/student/class";
import { listAllClass } from "@/api/kg/class/manage"    


export default {
  name: "PrepaymentBalance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预交款余额表格数据
      balanceList: [],
      // 班级列表
      classList: [],
      // 学生列表
      studentList: [],
      // 统计数据
      statistics: {
        tuitionTotal: 0,
        tuitionStudentCount: 0,
        courseTotal: 0,
        courseStudentCount: 0,
        insufficientCount: 0,
        negativeCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        classId: null,
        accountType: null,
        balanceStatus: null
      },
      // 充值对话框
      rechargeOpen: false,
      // 充值表单
      rechargeForm: {},
      // 充值表单校验
      rechargeRules: {
        studentId: [
          { required: true, message: "学生不能为空", trigger: "change" }
        ],
        accountType: [
          { required: true, message: "账户类型不能为空", trigger: "change" }
        ],
        amount: [
          { required: true, message: "充值金额不能为空", trigger: "blur" }
        ],
        paymentMethod: [
          { required: true, message: "支付方式不能为空", trigger: "change" }
        ]
      },
      // 详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {}
    };
  },
  created() {
    this.getList();
    this.getClassList();
    this.getStudentList();
    this.getStatistics();
  },
  methods: {
    /** 查询预交款余额列表 */
    getList() {
      this.loading = true;
      getBalanceList(this.queryParams).then(response => {
        this.balanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询班级列表 */
    getClassList() {
        listAllClass().then(response => {
        this.classList = response.data;
      });
    },
    /** 查询学生列表 */
    getStudentList() {
      listAllStudent().then(response => {
        this.studentList = response.data;
      });
    },
    /** 查询统计数据 */
    getStatistics() {
      getBalanceStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.studentId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 充值按钮操作 */
    handleRecharge() {
      this.resetRechargeForm();
      this.rechargeOpen = true;
    },
    /** 学生充值按钮操作 */
    handleRechargeStudent(row) {
      this.resetRechargeForm();
      this.rechargeForm.studentId = row.studentId;
      this.rechargeOpen = true
      ;
    },
    /** 重置充值表单 */
    resetRechargeForm() {
      this.rechargeForm = {
        studentId: null,
        accountType: "tuition",
        amount: null,
        paymentMethod: "wechat",
        remark: ""
      };
    },
    /** 提交充值 */
    submitRecharge() {
      this.$refs["rechargeForm"].validate(valid => {
        if (valid) {
          rechargeBalance(this.rechargeForm).then(response => {
            this.msgSuccess("充值成功");
            this.rechargeOpen = false;
            this.getList();
            this.getStatistics();
          });
        }
      });
    },
    /** 取消充值 */
    cancelRecharge() {
      this.rechargeOpen = false;
      this.resetRechargeForm();
    },
    /** 查看详情 */
    handleViewDetail(row) {
      this.detailData = row;
      this.detailOpen = true;
    },
    /** 批量扣费 */
    handleBatchDeduct() {
      if (this.ids.length === 0) {
        this.msgError("请选择要扣费的学生");
        return;
      }
      this.$confirm('是否确认批量扣费？').then(() => {
        // TODO: 实现批量扣费逻辑
        this.msgSuccess("批量扣费成功");
        this.getList();
      });
    },
    /** 余额提醒 */
    handleNotifyRecharge() {
      this.$confirm('是否发送余额不足提醒？').then(() => {
        batchNotifyRecharge().then(response => {
          this.msgSuccess("提醒发送成功");
        });
      });
    },
    /** 导出 */
    handleExport() {
      // TODO: 实现导出功能
      this.msgSuccess("导出功能开发中");
    },
    /** 下拉菜单操作 */
    handleCommand(command, row) {
      switch(command) {
        case 'transaction':
          this.$router.push({
            path: '/finance/prepayment/transaction',
            query: { studentId: row.studentId }
          });
          break;
        case 'freeze':
          this.msgSuccess("冻结功能开发中");
          break;
        case 'transfer':
          this.msgSuccess("转移功能开发中");
          break;
      }
    },
    /** 获取余额状态类名 */
    getBalanceClass(balance) {
      if (balance <= 0) return 'balance-negative';
      if (balance < 100) return 'balance-insufficient';
      return 'balance-sufficient';
    },
    /** 获取余额状态标签类型 */
    getBalanceTagType(balance) {
      if (balance <= 0) return 'danger';
      if (balance < 100) return 'warning';
      return 'success';
    },
    /** 获取余额状态文本 */
    getBalanceStatus(balance) {
      if (balance <= 0) return '余额为负';
      if (balance < 100) return '余额不足';
      return '余额充足';
    },
    /** 获取交易类型标签 */
    getTransactionTypeTag(type) {
      const map = {
        'recharge': 'success',
        'consume': 'info',
        'refund': 'warning'
      };
      return map[type] || 'info';
    },
    /** 获取交易类型名称 */
    getTransactionTypeName(type) {
      const map = {
        'recharge': '充值',
        'consume': '消费',
        'refund': '退款'
      };
      return map[type] || type;
    },
    /** 格式化金额 */
    formatMoney(amount) {
      return amount ? '¥' + parseFloat(amount).toFixed(2) : '¥0.00';
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.stats-card {
  display: flex;
  padding: 20px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 24px;
  color: #fff;
  margin-right: 16px;
}

.tuition-stats .stats-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.course-stats .stats-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.warning-stats .stats-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.negative-stats .stats-icon {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #f56c6c;
}

.stats-content {
  flex: 1;
}

.stats-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stats-desc {
  font-size: 12px;
  color: #909399;
}

.student-info {
  text-align: left;
}

.student-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.student-meta {
  font-size: 12px;
  color: #909399;
}

.student-code {
  margin-left: 8px;
}

.balance-info {
  text-align: center;
}

.balance-amount {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.balance-sufficient {
  color: #67C23A;
}

.balance-insufficient {
  color: #E6A23C;
}

.balance-negative {
  color: #F56C6C;
}

.total-balance {
  font-size: 18px;
  font-weight: bold;
}

.recent-transactions {
  margin-top: 20px;
}

.text-success {
  color: #67C23A;
}

.text-danger {
  color: #F56C6C;
}
</style>
