package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 园费配置对象 kg_tuition_config
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTuitionConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 班级类型（托班、小班、中班、大班） */
    @Excel(name = "班级类型", readConverterExp = "托=班、小班、中班、大班")
    private String classType;

    /** 每日餐费 */
    @Excel(name = "每日餐费")
    private BigDecimal mealFeePerDay;

    /** 每月保教费 */
    @Excel(name = "每月保教费")
    private BigDecimal educationFeePerMonth;

    /** 出勤率阈值（超过此值收全额保教费） */
    @Excel(name = "出勤率阈值", readConverterExp = "超=过此值收全额保教费")
    private BigDecimal attendanceThreshold;

    /** 半额保教费 */
    @Excel(name = "半额保教费")
    private BigDecimal halfEducationFee;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    /** 管理费 */
    @Excel(name = "管理费")
    private BigDecimal managementFee;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }
    public void setClassType(String classType) 
    {
        this.classType = classType;
    }

    public String getClassType() 
    {
        return classType;
    }
    public void setMealFeePerDay(BigDecimal mealFeePerDay) 
    {
        this.mealFeePerDay = mealFeePerDay;
    }

    public BigDecimal getMealFeePerDay() 
    {
        return mealFeePerDay;
    }
    public void setEducationFeePerMonth(BigDecimal educationFeePerMonth) 
    {
        this.educationFeePerMonth = educationFeePerMonth;
    }

    public BigDecimal getEducationFeePerMonth() 
    {
        return educationFeePerMonth;
    }
    public void setAttendanceThreshold(BigDecimal attendanceThreshold) 
    {
        this.attendanceThreshold = attendanceThreshold;
    }

    public BigDecimal getAttendanceThreshold() 
    {
        return attendanceThreshold;
    }
    public void setHalfEducationFee(BigDecimal halfEducationFee) 
    {
        this.halfEducationFee = halfEducationFee;
    }

    public BigDecimal getHalfEducationFee() 
    {
        return halfEducationFee;
    }
    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    // ========== 业务逻辑兼容方法 ==========
    public BigDecimal getMealPrice() {
        return this.mealFeePerDay;
    }

    public BigDecimal getEducationFee() {
        return this.educationFeePerMonth;
    }

    public BigDecimal getManagementFee() {
        return this.managementFee;
    }

    public void setManagementFee(BigDecimal managementFee) {
        this.managementFee = managementFee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("classType", getClassType())
            .append("mealFeePerDay", getMealFeePerDay())
            .append("educationFeePerMonth", getEducationFeePerMonth())
            .append("attendanceThreshold", getAttendanceThreshold())
            .append("halfEducationFee", getHalfEducationFee())
            .append("effectiveDate", getEffectiveDate())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
