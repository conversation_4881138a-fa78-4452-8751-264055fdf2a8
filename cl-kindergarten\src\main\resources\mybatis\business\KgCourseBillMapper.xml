<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgCourseBillMapper">
    
    <resultMap type="KgCourseBill" id="KgCourseBillResult">
        <result property="billId"    column="bill_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="billYear"    column="bill_year"    />
        <result property="billMonth"    column="bill_month"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="giftSessions"    column="gift_sessions"    />
        <result property="giftCourseName"    column="gift_course_name"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="sentTime"    column="sent_time"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="studentName"    column="student_name"    />
        <result property="className"    column="class_name"    />
    </resultMap>

    <sql id="selectKgCourseBillVo">
        select 
            kcb.bill_id, 
            kcb.student_id, 
            kcb.bill_year, 
            kcb.bill_month, 
            kcb.total_amount, 
            kcb.gift_sessions, 
            kcb.gift_course_name, 
            kcb.bill_status, 
            kcb.sent_time, 
            kcb.paid_time, 
            kcb.com_id, 
            kcb.create_by, 
            kcb.create_time, 
            kcb.update_by, 
            kcb.update_time, 
            kcb.remark,
            ks.student_name,
            kc.class_name
        from 
            kg_course_bill kcb
        left join 
            kg_student ks on kcb.student_id = ks.student_id
        left join 
            kg_class kc on ks.class_id = kc.class_id
    </sql>

    <select id="selectKgCourseBillList" parameterType="KgCourseBill" resultMap="KgCourseBillResult">
        <include refid="selectKgCourseBillVo"/>
        <where>  
            <if test="studentId != null "> and kcb.student_id = #{studentId}</if>
            <if test="billYear != null "> and kcb.bill_year = #{billYear}</if>
            <if test="billMonth != null "> and kcb.bill_month = #{billMonth}</if>
            <if test="totalAmount != null "> and kcb.total_amount = #{totalAmount}</if>
            <if test="giftSessions != null "> and kcb.gift_sessions = #{giftSessions}</if>
            <if test="giftCourseName != null  and giftCourseName != ''"> and kcb.gift_course_name like concat('%', #{giftCourseName}, '%')</if>
            <if test="billStatus != null  and billStatus != ''"> and kcb.bill_status = #{billStatus}</if>
            <if test="sentTime != null "> and kcb.sent_time = #{sentTime}</if>
            <if test="paidTime != null "> and kcb.paid_time = #{paidTime}</if>
            <if test="comId != null  and comId != ''"> and kcb.com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgCourseBillById" parameterType="Long" resultMap="KgCourseBillResult">
        <include refid="selectKgCourseBillVo"/>
        where kcb.bill_id = #{billId}
    </select>
        
    <insert id="insertKgCourseBill" parameterType="KgCourseBill" useGeneratedKeys="true" keyProperty="billId">
        insert into kg_course_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="billYear != null">bill_year,</if>
            <if test="billMonth != null">bill_month,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="giftSessions != null">gift_sessions,</if>
            <if test="giftCourseName != null">gift_course_name,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="sentTime != null">sent_time,</if>
            <if test="paidTime != null">paid_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="billYear != null">#{billYear},</if>
            <if test="billMonth != null">#{billMonth},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="giftSessions != null">#{giftSessions},</if>
            <if test="giftCourseName != null">#{giftCourseName},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="sentTime != null">#{sentTime},</if>
            <if test="paidTime != null">#{paidTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgCourseBill" parameterType="KgCourseBill">
        update kg_course_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="billYear != null">bill_year = #{billYear},</if>
            <if test="billMonth != null">bill_month = #{billMonth},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="giftSessions != null">gift_sessions = #{giftSessions},</if>
            <if test="giftCourseName != null">gift_course_name = #{giftCourseName},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="sentTime != null">sent_time = #{sentTime},</if>
            <if test="paidTime != null">paid_time = #{paidTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where bill_id = #{billId}
    </update>

    <delete id="deleteKgCourseBillById" parameterType="Long">
        delete from kg_course_bill where bill_id = #{billId}
    </delete>

    <delete id="deleteKgCourseBillByIds" parameterType="String">
        delete from kg_course_bill where bill_id in 
        <foreach item="billId" collection="array" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </delete>
    
</mapper>