import request from '@/utils/request'

// 查询教师工资列表
export function listTeacherSalary(query) {
  return request({
    url: '/business/salary/list',
    method: 'get',
    params: query
  })
}

// 获取教师工资详细信息
export function getTeacherSalary(salaryId) {
  return request({
    url: '/business/salary/' + salaryId,
    method: 'get'
  })
}

// 新增教师工资
export function addTeacherSalary(data) {
  return request({
    url: '/business/salary',
    method: 'post',
    data: data
  })
}

// 修改教师工资
export function updateTeacherSalary(data) {
  return request({
    url: '/business/salary',
    method: 'put',
    data: data
  })
}

// 删除教师工资
export function delTeacherSalary(salaryIds) {
  return request({
    url: '/business/salary/' + salaryIds,
    method: 'delete'
  })
}

// 导出教师工资
export function exportTeacherSalary(query) {
  return request({
    url: '/business/salary/export',
    method: 'get',
    params: query
  })
}

// 批量确认工资
export function batchConfirmSalary(salaryIds) {
  return request({
    url: '/business/salary/batch-confirm',
    method: 'post',
    data: salaryIds
  })
}

// 批量发放工资
export function batchPaySalary(salaryIds) {
  return request({
    url: '/business/salary/batch-pay',
    method: 'post',
    data: salaryIds  
  })
}

// 获取工资统计汇总
export function getSalarySummary(query) {
  return request({
    url: '/business/salary/summary',
    method: 'get',
    params: query
  })
}

// 生成工资单PDF
export function generatePayslipPdf(salaryId) {
  return request({
    url: '/business/salary/payslip-pdf/' + salaryId,
    method: 'get',
    responseType: 'blob'
  })
}
