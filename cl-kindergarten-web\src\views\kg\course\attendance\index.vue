<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="考勤日期" prop="attendanceDate">
        <el-date-picker
          v-model="queryParams.attendanceDate"
          type="date"
          placeholder="选择考勤日期"
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="课程" prop="courseId">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable>
          <el-option
            v-for="course in courseOptions"
            :key="course.courseId"
            :label="course.courseName"
            :value="course.courseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考勤状态" prop="attendanceStatus">
        <el-select v-model="queryParams.attendanceStatus" placeholder="请选择考勤状态" clearable>
          <el-option label="出勤" value="present" />
          <el-option label="缺勤" value="absent" />
          <el-option label="请假" value="leave" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 出勤率统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-people">
              <i class="el-icon-user-solid card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总人数</div>
              <div class="card-panel-num">{{ statistics.totalStudents }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-present">
              <i class="el-icon-success card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">出勤人数</div>
              <div class="card-panel-num">{{ statistics.presentCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-absent">
              <i class="el-icon-error card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">缺勤人数</div>
              <div class="card-panel-num">{{ statistics.absentCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-rate">
              <i class="el-icon-pie-chart card-panel-icon"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">出勤率</div>
              <div class="card-panel-num">{{ statistics.attendanceRate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-circle-check"
          @click="handleCheckin"
        >签到</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          :disabled="multiple"
          @click="handleBatchConfirm"
        >批量确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="multiple"
          @click="handleDelete"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <!-- 课程分组切换 -->
    <div class="course-group-tabs mb20">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全部课程" name="all"></el-tab-pane>
        <el-tab-pane 
          v-for="course in courseOptions" 
          :key="course.courseId"
          :label="course.courseName" 
          :name="course.courseId.toString()"
        ></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 考勤列表 -->
    <el-table 
      v-loading="loading" 
      :data="filteredAttendanceList" 
      @selection-change="handleSelectionChange"
      class="attendance-table"
      :row-class-name="tableRowClassName"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" prop="studentName" min-width="120" sortable>
        <template slot-scope="scope">
          <div class="student-info">
            <i class="el-icon-user"></i>
            <span>{{ scope.row.studentName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="课程名称" prop="courseName" min-width="150" sortable>
        <template slot-scope="scope">
          <el-tag size="small" type="info">{{ scope.row.courseName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="考勤日期" prop="attendanceDate" min-width="120" sortable />
      <el-table-column label="考勤状态" prop="attendanceStatus" min-width="120">
        <template slot-scope="scope">
          <el-tag :type="getAttendanceStatusType(scope.row.attendanceStatus)" effect="dark">
            <i :class="getAttendanceStatusIcon(scope.row.attendanceStatus)"></i>
            {{ getAttendanceStatusText(scope.row.attendanceStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="确认状态" prop="isConfirmed" min-width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isConfirmed ? 'success' : 'warning'">
            {{ scope.row.isConfirmed ? '已确认' : '待确认' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="200">
        <template slot-scope="scope">
          <el-button 
            v-if="!scope.row.isConfirmed" 
            size="mini" 
            type="primary" 
            @click="handleConfirm(scope.row)"
          >确认</el-button>
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="报名记录" prop="enrollmentId">
          <el-select 
            v-model="form.enrollmentId" 
            placeholder="请选择报名记录" 
            @change="handleEnrollmentChange"
            filterable
          >
            <el-option
              v-for="enrollment in enrollmentOptions"
              :key="enrollment.enrollmentId"
              :label="`${enrollment.studentName} - ${enrollment.courseName} (剩余${enrollment.remainingSessions}课时)`"
              :value="enrollment.enrollmentId"
            >
              <div class="enrollment-option">
                <div class="enrollment-main">
                  <span class="student-name">{{ enrollment.studentName }}</span>
                  <span class="course-name">{{ enrollment.courseName }}</span>
                </div>
                <div class="enrollment-detail">
                  <el-tag size="mini" :type="enrollment.remainingSessions > 0 ? 'success' : 'danger'">
                    剩余{{ enrollment.remainingSessions }}课时
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <!-- 显示选中报名的详细信息 -->
        <el-form-item v-if="form.enrollmentId" label="学生信息">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="学生姓名">{{ selectedEnrollment.studentName }}</el-descriptions-item>
            <el-descriptions-item label="课程名称">{{ selectedEnrollment.courseName }}</el-descriptions-item>
            <el-descriptions-item label="总课时">{{ selectedEnrollment.totalSessions }}</el-descriptions-item>
            <el-descriptions-item label="剩余课时">{{ selectedEnrollment.remainingSessions }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        
        <el-form-item label="授课教师" prop="teacherId">
          <el-select v-model="form.teacherId" placeholder="请选择教师">
            <el-option
              v-for="teacher in teacherOptions"
              :key="teacher.teacherId"
              :label="teacher.teacherName"
              :value="teacher.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期" prop="attendanceDate">
          <el-date-picker
            v-model="form.attendanceDate"
            type="date"
            placeholder="选择考勤日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="考勤状态" prop="attendanceStatus">
          <el-select v-model="form.attendanceStatus" placeholder="请选择状态">
            <el-option label="出勤" value="present" />
            <el-option label="缺勤" value="absent" />
            <el-option label="迟到" value="late" />
            <el-option label="早退" value="early" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listCourseAttendance, 
  getCourseAttendance, 
  delCourseAttendance, 
  addCourseAttendance, 
  updateCourseAttendance,
  courseCheckin,
  confirmCourseAttendance,
  confirmSingleAttendance,
  exportCourseAttendance 
} from "@/api/kg/course/attendance";
import { getCourse } from "@/api/kg/course/manage";
import { listAllCourse } from "@/api/kg/course/manage";
import { listActiveEnrollment } from "@/api/kg/course/enrollment";
import { listAllTeacher } from "@/api/kg/teacher/info";

export default {
  name: "CourseAttendance",
  data() {
    return {
      attendanceList: [],
      courseOptions: [],
      enrollmentOptions: [],
      teacherOptions: [],
      selectedEnrollment: {},
      loading: true,
      showSearch: true,
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      open: false,
      title: "",
      activeTab: "all",
      // 统计数据
      statistics: {
        totalStudents: 0,
        presentCount: 0,
        absentCount: 0,
        attendanceRate: 0
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        courseId: null,
        attendanceDate: null,
        attendanceStatus: null
      },
      form: {
        attendanceId: null,
        enrollmentId: null,
        studentId: null,
        courseId: null,
        teacherId: null,
        attendanceDate: null,
        startTime: null,
        endTime: null,
        attendanceStatus: "present",
        checkInMethod: "manual",
        remark: null
      },
      rules: {
        enrollmentId: [
          { required: true, message: "请选择报名记录", trigger: "change" }
        ],
        attendanceDate: [
          { required: true, message: "考勤日期不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    // 根据活动标签过滤考勤列表
    filteredAttendanceList() {
      if (this.activeTab === 'all') {
        return this.attendanceList;
      }
      return this.attendanceList.filter(item => 
        item.courseId && item.courseId.toString() === this.activeTab
      );
    }
  },
  created() {
    // 处理路由参数，从报名页面传递的参数
    const query = this.$route.query;
    if (query.enrollmentId) {
      this.queryParams.enrollmentId = query.enrollmentId;
    }
    if (query.studentId) {
      this.queryParams.studentId = query.studentId;
    }
    if (query.courseId) {
      this.queryParams.courseId = query.courseId;
      this.activeTab = query.courseId.toString(); // 设置活动标签为对应课程
    }
    if (query.studentName) {
      this.queryParams.studentName = query.studentName;
    }
    
    this.getList();
    this.loadOptions();
  },
  methods: {
    /** 查询考勤列表 */
    getList() {
      this.loading = true;
      listCourseAttendance(this.queryParams).then(response => {
        this.attendanceList = response.rows;
        this.total = response.total;
        this.calculateStatistics();
        this.loading = false;
      });
    },
    /** 计算统计数据 */
    calculateStatistics() {
      const list = this.activeTab === 'all' ? this.attendanceList : this.filteredAttendanceList;
      this.statistics.totalStudents = list.length;
      this.statistics.presentCount = list.filter(item => item.attendanceStatus === 'present').length;
      this.statistics.absentCount = list.filter(item => item.attendanceStatus === 'absent').length;
      this.statistics.attendanceRate = this.statistics.totalStudents > 0 
        ? Math.round((this.statistics.presentCount / this.statistics.totalStudents) * 100)
        : 0;
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.attendanceId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考勤";
    },
    /** 签到按钮操作 */
    handleCheckin() {
      this.reset();
      this.open = true;
      this.title = "学生签到";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const attendanceId = row.attendanceId || this.ids[0];
      getCourseAttendance(attendanceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考勤";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.attendanceId != null) {
            updateCourseAttendance(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 根据标题判断是签到还是新增
            const apiCall = this.title === "学生签到" ? courseCheckin : addCourseAttendance;
            const successMsg = this.title === "学生签到" ? "签到成功" : "新增成功";
            
            apiCall(this.form).then(response => {
              this.msgSuccess(successMsg);
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const attendanceIds = row.attendanceId || this.ids;
      this.$confirm('是否确认删除考勤记录编号为"' + attendanceIds + '"的数据项？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return delCourseAttendance(attendanceIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 确认按钮操作 */
    handleConfirm(row) {
      const attendanceId = row.attendanceId;
      this.$confirm('是否确认该考勤记录？确认后将更新课时使用情况。', "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        return confirmSingleAttendance(attendanceId);
      }).then(() => {
        this.getList();
        this.msgSuccess("确认成功");
      }).catch(() => {});
    },
    /** 批量确认按钮操作 */
    handleBatchConfirm() {
      const attendanceIds = this.ids;
      this.$confirm('是否确认批量确认选中的考勤记录？', "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        return confirmCourseAttendance(attendanceIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("批量确认成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/course/attendance/export', {
        ...this.queryParams
      }, `course_attendance_${new Date().getTime()}.xlsx`);
    },
    /** 日期变化处理 */
    handleDateChange(date) {
      this.queryParams.attendanceDate = date;
      this.handleQuery();
    },
    /** 标签页切换处理 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
      this.calculateStatistics();
    },
    /** 表格行样式 */
    tableRowClassName({row}) {
      if (row.attendanceStatus === 'absent') {
        return 'absent-row';
      } else if (row.attendanceStatus === 'late') {
        return 'late-row';
      }
      return '';
    },
    /** 获取考勤状态类型 */
    getAttendanceStatusType(status) {
      const statusMap = {
        'present': 'success',
        'absent': 'danger',
        'late': 'warning',
        'early': 'info',
        'leave': 'info'
      };
      return statusMap[status] || 'info';
    },
    /** 获取考勤状态图标 */
    getAttendanceStatusIcon(status) {
      const iconMap = {
        'present': 'el-icon-success',
        'absent': 'el-icon-error',
        'late': 'el-icon-warning',
        'early': 'el-icon-info',
        'leave': 'el-icon-question'
      };
      return iconMap[status] || 'el-icon-info';
    },
    /** 获取考勤状态文本 */
    getAttendanceStatusText(status) {
      const statusMap = {
        'present': '出勤',
        'absent': '缺勤',
        'late': '迟到',
        'early': '早退'
      };
      return statusMap[status] || '未知';
    },
    /** 报名记录变化处理 */
    async handleEnrollmentChange(enrollmentId) {
      const enrollment = this.enrollmentOptions.find(item => item.enrollmentId === enrollmentId);
      if (!enrollment) return;
      
      this.selectedEnrollment = enrollment;
      // 自动填充学生和课程信息
      this.form.studentId = enrollment.studentId;
      this.form.courseId = enrollment.courseId;
      
      try {
        // 获取课程详情以获取默认教师
        const courseRes = await getCourse(enrollment.courseId);
        const course = courseRes.data;
        
        // 如果课程有默认教师ID，则自动填充
        if (course.defaultTeacherId) {
          this.form.teacherId = course.defaultTeacherId;
          
          // 如果教师选项中没有该教师，则添加到选项中
          const teacherExists = this.teacherOptions.some(
            teacher => teacher.teacherId === course.defaultTeacherId
          );
          
          if (!teacherExists && course.defaultTeacherName) {
            this.teacherOptions.push({
              teacherId: course.defaultTeacherId,
              teacherName: course.defaultTeacherName || '未知教师'
            });
          }
        } else {
          // 如果没有默认教师，则清空教师选择
          this.form.teacherId = null;
        }
      } catch (error) {
        console.error('获取课程详情失败:', error);
        this.$message.error('获取课程信息失败，请稍后重试');
        this.form.teacherId = null;
      }
    },
    
    /** 重置表单 */
    reset() {
      this.form = {
        attendanceId: null,
        enrollmentId: null,
        studentId: null,
        courseId: null,
        teacherId: null,
        attendanceDate: null,
        startTime: null,
        endTime: null,
        attendanceStatus: "present",
        checkInMethod: "manual",
        remark: null
      };
      this.selectedEnrollment = {};
      this.resetForm("form");
    },
    
    /** 加载选项数据 */
    loadOptions() {
      // 加载课程选项
      listAllCourse().then(response => {
        this.courseOptions = response.data || [];
      });
      
      // 加载活跃的报名记录
      listActiveEnrollment({ status: 'active' }).then(response => {
        this.enrollmentOptions = response.rows || [];
      });
      
      // 加载教师选项
      listAllTeacher().then(response => {
        this.teacherOptions = response.data || [];
      });
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.mb20 {
  margin-bottom: 20px;
}

/* 统计卡片样式 */
.statistics-card {
  cursor: pointer;
  transition: transform 0.3s;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-panel {
  display: flex;
  align-items: center;
  padding: 15px;
}

.card-panel-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.icon-people {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-present {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-absent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.icon-rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-panel-icon {
  color: white;
  font-size: 24px;
}

.card-panel-description {
  flex: 1;
}

.card-panel-text {
  color: #999;
  font-size: 14px;
  margin-bottom: 8px;
}

.card-panel-num {
  color: #333;
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
}

/* 课程分组样式 */
.course-group-tabs {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
}

.course-group-tabs .el-tabs__header {
  margin: 0;
}

.course-group-tabs .el-tabs__nav-wrap {
  padding: 0;
}

/* 表格样式 */
.attendance-table {
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.student-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.student-info i {
  color: #409EFF;
}

/* 表格行样式 */
.attendance-table .absent-row {
  background-color: #fef0f0;
}

.attendance-table .late-row {
  background-color: #fdf6ec;
}

.attendance-table .el-table__row:hover {
  background-color: #f5f7fa !important;
}

/* 状态标签样式 */
.el-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.el-tag i {
  font-size: 12px;
}

/* 操作按钮样式 */
.el-button {
  transition: all 0.3s;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 卡片动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.statistics-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 报名选项样式 */
.enrollment-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
}

.enrollment-main {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.student-name {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.course-name {
  color: #606266;
  font-size: 12px;
}

.enrollment-detail {
  flex-shrink: 0;
}

/* 学生信息显示 */
.el-descriptions {
  margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-panel {
    padding: 10px;
  }
  
  .card-panel-icon-wrapper {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  
  .card-panel-icon {
    font-size: 20px;
  }
  
  .card-panel-num {
    font-size: 24px;
  }
  
  .enrollment-option {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
