<template>
  <div class="app-container">
    <el-card class="recharge-card">
      <div slot="header" class="clearfix">
        <span class="card-title">预交款充值</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <!-- 充值表单 -->
      <el-form ref="rechargeForm" :model="rechargeForm" :rules="rechargeRules" label-width="120px" class="recharge-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择学生" prop="studentId" required>
              <el-select 
                v-model="rechargeForm.studentId" 
                placeholder="请选择学生" 
                style="width: 100%"
                filterable
                @change="onStudentChange"
              >
                <el-option
                  v-for="item in studentList"
                  :key="item.studentId"
                  :label="item.studentName + '(' + item.className + ')'"
                  :value="item.studentId"
                >
                  <span style="float: left">{{ item.studentName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.className }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="账户类型" prop="accountType" required>
              <el-radio-group v-model="rechargeForm.accountType" @change="onAccountTypeChange">
                <el-radio label="tuition">
                  <i class="el-icon-wallet"></i>
                  园费账户
                </el-radio>
                <el-radio label="course">
                  <i class="el-icon-school"></i>
                  托管费账户
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="充值金额" prop="amount" required>
              <el-input-number 
                v-model="rechargeForm.amount" 
                :precision="2" 
                :step="50" 
                :min="0.01" 
                :max="99999.99"
                style="width: 100%"
                placeholder="请输入充值金额"
              >
                <template slot="prepend">¥</template>
              </el-input-number>
            </el-form-item>

            <el-form-item label="支付方式" prop="paymentMethod" required>
              <el-select v-model="rechargeForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option label="微信支付" value="wechat">
                  <i class="el-icon-chat-round" style="color: #07c160"></i>
                  微信支付
                </el-option>
                <el-option label="支付宝" value="alipay">
                  <i class="el-icon-coin" style="color: #1677ff"></i>
                  支付宝
                </el-option>
                <el-option label="现金" value="cash">
                  <i class="el-icon-money" style="color: #f5222d"></i>
                  现金
                </el-option>
                <el-option label="银行转账" value="bank">
                  <i class="el-icon-bank-card" style="color: #fa8c16"></i>
                  银行转账
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="备注信息" prop="remark">
              <el-input 
                v-model="rechargeForm.remark" 
                type="textarea" 
                :rows="3"
                placeholder="请输入备注信息（可选）"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- 学生信息卡片 -->
            <div v-if="selectedStudent" class="student-info-card">
              <h4>学生信息</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="姓名">{{ selectedStudent.studentName }}</el-descriptions-item>
                <el-descriptions-item label="学号">{{ selectedStudent.studentCode }}</el-descriptions-item>
                <el-descriptions-item label="班级">{{ selectedStudent.className }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 当前余额卡片 -->
            <div v-if="balanceInfo" class="balance-info-card">
              <h4>当前余额</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="园费余额">
                  <span :class="getBalanceClass(balanceInfo.tuitionBalance)">
                    {{ formatMoney(balanceInfo.tuitionBalance) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="托管费余额">
                  <span :class="getBalanceClass(balanceInfo.courseBalance)">
                    {{ formatMoney(balanceInfo.courseBalance) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="总余额">
                  <span :class="getBalanceClass(balanceInfo.totalBalance)" style="font-weight: bold;">
                    {{ formatMoney(balanceInfo.totalBalance) }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 充值预览 -->
            <div v-if="rechargeForm.amount && rechargeForm.accountType" class="recharge-preview-card">
              <h4>充值预览</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="充值账户">
                  {{ rechargeForm.accountType === 'tuition' ? '园费账户' : '托管费账户' }}
                </el-descriptions-item>
                <el-descriptions-item label="充值金额">
                  <span class="text-success">+{{ formatMoney(rechargeForm.amount) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="充值后余额">
                  <span class="text-primary" style="font-weight: bold;">
                    {{ getAfterRechargeBalance() }}
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <el-form-item style="margin-top: 30px; text-align: center;">
          <el-button type="primary" size="medium" @click="submitRecharge" :loading="submitLoading">
            <i class="el-icon-check"></i>
            确认充值
          </el-button>
          <el-button size="medium" @click="resetForm">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
          <el-button size="medium" @click="goBack">
            <i class="el-icon-back"></i>
            返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 充值成功对话框 -->
    <el-dialog
      title="充值成功"
      :visible.sync="successDialogVisible"
      width="500px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="success-content">
        <el-result
          icon="success"
          title="充值成功！"
          :sub-title="successMessage"
        >
          <template slot="extra">
            <el-button type="primary" @click="continueRecharge">继续充值</el-button>
            <el-button @click="goToList">返回列表</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { rechargeBalance, getStudentBalance } from "@/api/kg/prepayment";
import { listStudentForSelect } from "@/api/kg/student/info";

export default {
  name: "PrepaymentRecharge",
  data() {
    return {
      // 提交loading
      submitLoading: false,
      // 学生列表
      studentList: [],
      // 选中的学生
      selectedStudent: null,
      // 余额信息
      balanceInfo: null,
      // 充值表单
      rechargeForm: {
        studentId: null,
        accountType: "tuition",
        amount: null,
        paymentMethod: "wechat",
        remark: ""
      },
      // 表单校验规则
      rechargeRules: {
        studentId: [
          { required: true, message: "请选择学生", trigger: "change" }
        ],
        accountType: [
          { required: true, message: "请选择账户类型", trigger: "change" }
        ],
        amount: [
          { required: true, message: "请输入充值金额", trigger: "blur" },
          { type: 'number', min: 0.01, max: 99999.99, message: "充值金额必须在0.01-99999.99之间", trigger: "blur" }
        ],
        paymentMethod: [
          { required: true, message: "请选择支付方式", trigger: "change" }
        ]
      },
      // 成功对话框
      successDialogVisible: false,
      successMessage: ""
    };
  },
  created() {
    this.getStudentList();
    // 如果有传入的学生ID，自动选中
    if (this.$route.query.studentId) {
      this.rechargeForm.studentId = parseInt(this.$route.query.studentId);
    }
  },
  methods: {
    /** 获取学生列表 */
    getStudentList() {
      listStudentForSelect().then(response => {
        this.studentList = response.data;
        // 如果有预设的学生ID，设置选中状态
        if (this.rechargeForm.studentId) {
          this.onStudentChange();
        }
      });
    },
    /** 学生变更事件 */
    onStudentChange() {
      if (this.rechargeForm.studentId) {
        // 设置选中的学生信息
        this.selectedStudent = this.studentList.find(item => item.studentId === this.rechargeForm.studentId);
        // 获取余额信息
        this.getBalanceInfo();
      } else {
        this.selectedStudent = null;
        this.balanceInfo = null;
      }
    },
    /** 账户类型变更事件 */
    onAccountTypeChange() {
      // 重新获取余额信息以更新预览
    },
    /** 获取余额信息 */
    getBalanceInfo() {
      if (this.rechargeForm.studentId) {
        getStudentBalance(this.rechargeForm.studentId).then(response => {
          this.balanceInfo = response.data;
        }).catch(() => {
          // 如果获取失败，设置默认值
          this.balanceInfo = {
            tuitionBalance: 0,
            courseBalance: 0,
            totalBalance: 0
          };
        });
      }
    },
    /** 提交充值 */
    submitRecharge() {
      this.$refs["rechargeForm"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          rechargeBalance(this.rechargeForm).then(response => {
            const accountTypeName = this.rechargeForm.accountType === 'tuition' ? '园费账户' : '托管费账户';
            this.successMessage = `已成功向${this.selectedStudent.studentName}的${accountTypeName}充值${this.formatMoney(this.rechargeForm.amount)}`;
            this.successDialogVisible = true;
            this.submitLoading = false;
          }).catch(() => {
            this.submitLoading = false;
          });
        }
      });
    },
    /** 重置表单 */
    resetForm() {
      this.$refs["rechargeForm"].resetFields();
      this.selectedStudent = null;
      this.balanceInfo = null;
      this.rechargeForm = {
        studentId: null,
        accountType: "tuition",
        amount: null,
        paymentMethod: "wechat",
        remark: ""
      };
    },
    /** 继续充值 */
    continueRecharge() {
      this.successDialogVisible = false;
      this.resetForm();
    },
    /** 返回列表 */
    goToList() {
      this.$router.push('/finance/prepayment');
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1);
    },
    /** 获取余额状态类名 */
    getBalanceClass(balance) {
      if (balance <= 0) return 'balance-negative';
      if (balance < 100) return 'balance-insufficient';
      return 'balance-sufficient';
    },
    /** 格式化金额 */
    formatMoney(amount) {
      return amount ? '¥' + parseFloat(amount).toFixed(2) : '¥0.00';
    },
    /** 获取充值后余额 */
    getAfterRechargeBalance() {
      if (!this.balanceInfo || !this.rechargeForm.amount) return '¥0.00';
      
      let currentBalance = 0;
      if (this.rechargeForm.accountType === 'tuition') {
        currentBalance = this.balanceInfo.tuitionBalance || 0;
      } else {
        currentBalance = this.balanceInfo.courseBalance || 0;
      }
      
      const afterBalance = parseFloat(currentBalance) + parseFloat(this.rechargeForm.amount);
      return this.formatMoney(afterBalance);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.recharge-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.recharge-form {
  margin-top: 20px;
}

.student-info-card,
.balance-info-card,
.recharge-preview-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.student-info-card h4,
.balance-info-card h4,
.recharge-preview-card h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.balance-sufficient {
  color: #67C23A;
  font-weight: bold;
}

.balance-insufficient {
  color: #E6A23C;
  font-weight: bold;
}

.balance-negative {
  color: #F56C6C;
  font-weight: bold;
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-primary {
  color: #409eff;
  font-weight: bold;
}

.success-content {
  text-align: center;
}

/* 自定义选择器样式 */
.el-select-dropdown__item {
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recharge-form .el-col {
    margin-bottom: 20px;
  }
  
  .student-info-card,
  .balance-info-card,
  .recharge-preview-card {
    margin-bottom: 15px;
  }
}

/* 表单项图标样式 */
.el-radio {
  margin-right: 30px;
}

.el-radio .el-radio__label {
  padding-left: 5px;
}

.el-radio .el-radio__label i {
  margin-right: 5px;
}
</style>
