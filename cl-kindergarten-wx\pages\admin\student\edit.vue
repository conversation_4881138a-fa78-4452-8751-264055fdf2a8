<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ isEdit ? '编辑学生' : '添加学生' }}</text>
				</view>
				<view class="nav-right" @click="saveStudent">
					<text class="save-text">保存</text>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">
					<u-icon name="user" color="#667eea" size="20"></u-icon>
					<text class="title-text">基本信息</text>
				</view>
				
				<view class="form-item">
					<text class="label">学生姓名 <text class="required">*</text></text>
					<u-input 
						v-model="formData.studentName" 
						placeholder="请输入学生姓名"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">性别</text>
					<view class="radio-group">
						<view class="radio-item" :class="{ active: formData.gender === '0' }" @click="selectGender('0')">
							<view class="radio-dot" :class="{ active: formData.gender === '0' }"></view>
							<text class="radio-text">男</text>
						</view>
						<view class="radio-item" :class="{ active: formData.gender === '1' }" @click="selectGender('1')">
							<view class="radio-dot" :class="{ active: formData.gender === '1' }"></view>
							<text class="radio-text">女</text>
						</view>
					</view>
				</view>

				<view class="form-item">
					<text class="label">出生日期</text>
					<view class="date-input" @click="showDatePicker = true">
						<text class="date-text">{{ formatDateDisplay(formData.birthDate) || '请选择出生日期' }}</text>
						<u-icon name="calendar" color="#999" size="20"></u-icon>
					</view>
				</view>

				<view class="form-item">
					<text class="label">身份证号</text>
					<u-input 
						v-model="formData.idCard" 
						placeholder="请输入身份证号"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">联系电话</text>
					<u-input 
						v-model="formData.phone" 
						placeholder="请输入联系电话"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>
			</view>

			<!-- 家庭信息 -->
			<view class="form-section">
				<view class="section-title">
					<u-icon name="home" color="#667eea" size="20"></u-icon>
					<text class="title-text">家庭信息</text>
				</view>

				<view class="form-item">
					<text class="label">家长姓名</text>
					<u-input 
						v-model="formData.parentName" 
						placeholder="请输入家长姓名"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">家长电话</text>
					<u-input 
						v-model="formData.parentPhone" 
						placeholder="请输入家长电话"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">紧急联系人</text>
					<u-input 
						v-model="formData.emergencyContact" 
						placeholder="请输入紧急联系人"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">紧急联系电话</text>
					<u-input 
						v-model="formData.emergencyPhone" 
						placeholder="请输入紧急联系电话"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<text class="label">家庭住址</text>
					<view class="textarea-wrapper">
						<textarea
							v-model="formData.address"
							placeholder="请输入家庭住址"
							maxlength="200"
							class="custom-textarea"
							@input="onAddressInput"
						/>
						<view class="char-count">{{ addressLength }}/200</view>
					</view>
				</view>
			</view>

			<!-- 班级信息 -->
			<view class="form-section">
				<view class="section-title">
					<u-icon name="bookmark" color="#667eea" size="20"></u-icon>
					<text class="title-text">班级信息</text>
				</view>

				<view class="form-item">
					<text class="label">班级</text>
					<view class="input-wrapper" @click="showClassPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedClass }">
								{{ selectedClass ? selectedClass.label : '请选择班级' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<view class="form-item">
					<text class="label">入园日期</text>
					<view class="date-input" @click="showEnrollmentDatePicker = true">
						<text class="date-text">{{ formatDateDisplay(formData.enrollmentDate) || '请选择入园日期' }}</text>
						<u-icon name="calendar" color="#999" size="20"></u-icon>
					</view>
				</view>

				<view class="form-item">
					<text class="label">状态</text>
					<view class="radio-group">
						<view class="radio-item" :class="{ active: formData.status === '0' }" @click="selectStatus('0')">
							<view class="radio-dot" :class="{ active: formData.status === '0' }"></view>
							<text class="radio-text">在园</text>
						</view>
						<view class="radio-item" :class="{ active: formData.status === '1' }" @click="selectStatus('1')">
							<view class="radio-dot" :class="{ active: formData.status === '1' }"></view>
							<text class="radio-text">退园</text>
						</view>
						<view class="radio-item" :class="{ active: formData.status === '2' }" @click="selectStatus('2')">
							<view class="radio-dot" :class="{ active: formData.status === '2' }"></view>
							<text class="radio-text">请假</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他信息 -->
			<view class="form-section">
				<view class="section-title">
					<u-icon name="edit-pen" color="#667eea" size="20"></u-icon>
					<text class="title-text">其他信息</text>
				</view>

				<view class="form-item">
					<text class="label">学号</text>
					<u-input 
						v-model="formData.studentNumber" 
						placeholder="请输入学号"
						:border="false"
						:disabled="isEdit"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
					<text v-if="isEdit" class="form-tip">学号不可修改</text>
				</view>

				<view class="form-item">
					<text class="label">邮箱</text>
					<u-input 
						v-model="formData.email" 
						placeholder="请输入邮箱"
						:border="false"
						customStyle="background: #f8f9fa; padding: 24rpx; border-radius: 12rpx;"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">
						<text>备注</text>
					</view>
					<view class="textarea-wrapper">
						<textarea
							v-model="formData.remark"
							placeholder="请输入备注信息"
							maxlength="500"
							class="custom-textarea"
							@input="onRemarkInput"
						/>
						<view class="char-count">{{ remarkLength }}/500</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveStudent" :disabled="saving">
				<u-icon v-if="saving" name="loading" color="#ffffff" size="20"></u-icon>
				<text class="save-btn-text">{{ saving ? '保存中...' : '保存学生信息' }}</text>
			</button>
		</view>

		<!-- 出生日期选择器 -->
		<u-popup v-model="showDatePicker" mode="bottom" border-radius="24">
			<view class="date-picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showDatePicker = false">取消</text>
					<text class="picker-title">选择出生日期</text>
					<text class="picker-confirm" @click="confirmBirthDate">确定</text>
				</view>
				<picker-view class="date-picker-view" :value="birthDatePickerValue" @change="onBirthDateChange">
					<picker-view-column>
						<view v-for="year in yearOptions" :key="year" class="picker-item">
							{{ year }}年
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="month in monthOptions" :key="month" class="picker-item">
							{{ month }}月
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="day in dayOptions" :key="day" class="picker-item">
							{{ day }}日
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 入园日期选择器 -->
		<u-popup v-model="showEnrollmentDatePicker" mode="bottom" border-radius="24">
			<view class="date-picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showEnrollmentDatePicker = false">取消</text>
					<text class="picker-title">选择入园日期</text>
					<text class="picker-confirm" @click="confirmEnrollmentDate">确定</text>
				</view>
				<picker-view class="date-picker-view" :value="enrollmentDatePickerValue" @change="onEnrollmentDateChange">
					<picker-view-column>
						<view v-for="year in yearOptions" :key="year" class="picker-item">
							{{ year }}年
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="month in monthOptions" :key="month" class="picker-item">
							{{ month }}月
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="day in dayOptions" :key="day" class="picker-item">
							{{ day }}日
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getStudentDetail, addStudent, updateStudent, getAllClassList } from '@/api/api.js'

export default {
	data() {
		return {
			isEdit: false,
			studentId: null,
			saving: false,
			showDatePicker: false,
			showEnrollmentDatePicker: false,

			// 日期选择器相关数据
			yearOptions: [],
			monthOptions: [],
			dayOptions: [],
			birthDatePickerValue: [0, 0, 0],
			enrollmentDatePickerValue: [0, 0, 0],
			tempBirthDate: [0, 0, 0],
			tempEnrollmentDate: [0, 0, 0],

			// 地址字符计数
			addressLength: 0,
			// 备注字符计数
			remarkLength: 0,
			formData: {
				studentName: '',
				studentNumber: '',
				gender: '0',
				birthDate: '',
				idCard: '',
				phone: '',
				classId: '',
				parentName: '',
				parentPhone: '',
				emergencyContact: '',
				emergencyPhone: '',
				address: '',
				enrollmentDate: '',
				status: '0',
				email: '',
				remark: ''
			},
			
			// 班级选择
			selectedClass: null,
			classOptions: []
		}
	},

	onLoad(options) {
		this.initDatePicker()
		this.loadClassList()
		if (options.id) {
			this.isEdit = true
			this.studentId = options.id
			this.loadStudentDetail()
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 加载班级列表
		async loadClassList() {
			try {
				const res = await getAllClassList()
				if (res && res.code === 200 && res.data) {
					this.classOptions = res.data.map(classItem => ({
						value: classItem.classId.toString(),
						label: classItem.className
					}))
				} else {
					console.warn('获取班级列表失败，使用默认数据')
					this.classOptions = [
						{ value: '1', label: '小班' },
						{ value: '2', label: '中班' },
						{ value: '3', label: '大班' }
					]
				}
			} catch (error) {
				console.error('加载班级列表失败:', error)
				this.classOptions = [
					{ value: '1', label: '小班' },
					{ value: '2', label: '中班' },
					{ value: '3', label: '大班' }
				]
			}
		},

		// 加载学生详情
		async loadStudentDetail() {
			if (!this.studentId) return

			try {
				uni.showLoading({ title: '加载中...' })
				const res = await getStudentDetail(this.studentId)
				uni.hideLoading()

				if (res.code === 200 && res.data) {
					const data = res.data
					this.formData = {
						studentName: data.studentName || '',
						studentNumber: data.studentNumber || data.studentCode || '',
						gender: data.gender || '0',
						birthDate: data.birthDate || '',
						idCard: data.idCard || '',
						phone: data.phone || '',
						classId: data.classId || '',
						parentName: data.parentName || '',
						parentPhone: data.parentPhone || '',
						emergencyContact: data.emergencyContact || '',
						emergencyPhone: data.emergencyPhone || '',
						address: data.address || '',
						enrollmentDate: data.enrollmentDate || '',
						status: data.status || '0',
						email: data.email || '',
						remark: data.remark || ''
					}

					// 初始化地址长度
					this.addressLength = this.formData.address.length
					// 初始化备注长度
					this.remarkLength = this.formData.remark.length
					
					// 设置选中的班级
					if (data.classId) {
						this.selectedClass = this.classOptions.find(item => item.value === data.classId.toString())
					}
					
					// 设置日期选择器的值
					this.setDatePickerValues()
				} else {
					toast(res.msg || '获取学生信息失败')
					this.goBack()
				}
			} catch (error) {
				uni.hideLoading()
				console.error('加载学生详情失败:', error)
				toast('加载失败，请稍后重试')
				this.goBack()
			}
		},

		// 保存学生信息
		async saveStudent() {
			// 表单验证
			if (!this.formData.studentName.trim()) {
				toast('请输入学生姓名')
				return
			}
			if (!this.formData.studentNumber.trim()) {
				toast('请输入学号')
				return
			}
			if (!this.formData.classId) {
				toast('请选择班级')
				return
			}

			this.saving = true
			try {
				uni.showLoading({ title: '保存中...' })
				
				let result
				if (this.isEdit) {
					// 编辑学生
					const updateData = {
						studentId: this.studentId,
						studentName: this.formData.studentName.trim(),
						studentNumber: this.formData.studentNumber.trim(),
						gender: this.formData.gender,
						birthDate: this.formData.birthDate,
						idCard: this.formData.idCard ? this.formData.idCard.trim() : null,
						phone: this.formData.phone ? this.formData.phone.trim() : null,
						classId: this.formData.classId ? parseInt(this.formData.classId) : null,
						parentName: this.formData.parentName.trim(),
						parentPhone: this.formData.parentPhone.trim(),
						emergencyContact: this.formData.emergencyContact ? this.formData.emergencyContact.trim() : null,
						emergencyPhone: this.formData.emergencyPhone ? this.formData.emergencyPhone.trim() : null,
						address: this.formData.address ? this.formData.address.trim() : null,
						enrollmentDate: this.formData.enrollmentDate,
						status: this.formData.status,
						email: this.formData.email ? this.formData.email.trim() : null,
						remark: this.formData.remark ? this.formData.remark.trim() : null
					}
					result = await updateStudent(updateData)
				} else {
					// 新增学生
					result = await addStudent(this.formData)
				}

				uni.hideLoading()

				if (result.code === 200) {
					toast(this.isEdit ? '修改成功' : '添加成功')
					
					// 触发父页面刷新
					const eventChannel = this.getOpenerEventChannel()
					if (eventChannel) {
						eventChannel.emit('refreshStudentList')
					}
					
					// 延迟返回上一页
					setTimeout(() => {
						this.goBack()
					}, 1000)
				} else {
					toast(result.msg || (this.isEdit ? '修改失败' : '添加失败'))
				}
			} catch (error) {
				uni.hideLoading()
				console.error('保存学生信息失败:', error)
				toast('保存失败，请稍后重试')
			} finally {
				this.saving = false
			}
		},

		// 设置日期选择器的值
		setDatePickerValues() {
			// 设置出生日期选择器的值
			if (this.formData.birthDate) {
				try {
					// 处理不同格式的日期字符串
					let birthDateStr = this.formData.birthDate
					if (typeof birthDateStr === 'string') {
						// 去掉时间部分，只保留日期
						birthDateStr = birthDateStr.split(' ')[0]
					}
					
					const birthDate = new Date(birthDateStr)
					if (!isNaN(birthDate.getTime())) {
						const birthYear = birthDate.getFullYear()
						const birthMonth = birthDate.getMonth() + 1
						const birthDay = birthDate.getDate()
						
						const yearIndex = this.yearOptions.findIndex(year => year === birthYear)
						const monthIndex = this.monthOptions.findIndex(month => month === birthMonth)
						
						// 更新日期选项以确保正确的天数
						this.updateDayOptions(birthYear, birthMonth)
						const dayIndex = this.dayOptions.findIndex(day => day === birthDay)
						
						if (yearIndex >= 0 && monthIndex >= 0 && dayIndex >= 0) {
							this.birthDatePickerValue = [yearIndex, monthIndex, dayIndex]
							this.tempBirthDate = [...this.birthDatePickerValue]
						}
					}
				} catch (error) {
					console.error('解析出生日期失败:', error)
				}
			}
			
			// 设置入园日期选择器的值
			if (this.formData.enrollmentDate) {
				try {
					// 处理不同格式的日期字符串
					let enrollmentDateStr = this.formData.enrollmentDate
					if (typeof enrollmentDateStr === 'string') {
						// 去掉时间部分，只保留日期
						enrollmentDateStr = enrollmentDateStr.split(' ')[0]
					}
					
					const enrollmentDate = new Date(enrollmentDateStr)
					if (!isNaN(enrollmentDate.getTime())) {
						const enrollYear = enrollmentDate.getFullYear()
						const enrollMonth = enrollmentDate.getMonth() + 1
						const enrollDay = enrollmentDate.getDate()
						
						const yearIndex = this.yearOptions.findIndex(year => year === enrollYear)
						const monthIndex = this.monthOptions.findIndex(month => month === enrollMonth)
						
						// 更新日期选项以确保正确的天数
						this.updateDayOptions(enrollYear, enrollMonth)
						const dayIndex = this.dayOptions.findIndex(day => day === enrollDay)
						
						if (yearIndex >= 0 && monthIndex >= 0 && dayIndex >= 0) {
							this.enrollmentDatePickerValue = [yearIndex, monthIndex, dayIndex]
							this.tempEnrollmentDate = [...this.enrollmentDatePickerValue]
						}
					}
				} catch (error) {
					console.error('解析入园日期失败:', error)
				}
			}
		},

		// 初始化日期选择器
		initDatePicker() {
			// 生成年份选项 (1950-当前年份)
			const currentYear = new Date().getFullYear()
			this.yearOptions = []
			for (let i = 1950; i <= currentYear; i++) {
				this.yearOptions.push(i)
			}

			// 生成月份选项
			this.monthOptions = []
			for (let i = 1; i <= 12; i++) {
				this.monthOptions.push(i)
			}

			// 生成日期选项 (默认31天)
			this.updateDayOptions(currentYear, 1)

			// 设置默认值
			const defaultYear = currentYear - 5 // 默认5岁
			const yearIndex = this.yearOptions.findIndex(year => year === defaultYear)
			this.birthDatePickerValue = [yearIndex, 0, 0]
			this.enrollmentDatePickerValue = [this.yearOptions.length - 1, 0, 0] // 当前年份
			this.tempBirthDate = [...this.birthDatePickerValue]
			this.tempEnrollmentDate = [...this.enrollmentDatePickerValue]
		},

		// 更新日期选项
		updateDayOptions(year, month) {
			const daysInMonth = new Date(year, month, 0).getDate()
			this.dayOptions = []
			for (let i = 1; i <= daysInMonth; i++) {
				this.dayOptions.push(i)
			}
		},

		// 出生日期选择器变化
		onBirthDateChange(e) {
			this.tempBirthDate = e.detail.value
			const year = this.yearOptions[this.tempBirthDate[0]]
			const month = this.monthOptions[this.tempBirthDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempBirthDate[2] >= this.dayOptions.length) {
				this.tempBirthDate[2] = this.dayOptions.length - 1
			}
		},

		// 入园日期选择器变化
		onEnrollmentDateChange(e) {
			this.tempEnrollmentDate = e.detail.value
			const year = this.yearOptions[this.tempEnrollmentDate[0]]
			const month = this.monthOptions[this.tempEnrollmentDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempEnrollmentDate[2] >= this.dayOptions.length) {
				this.tempEnrollmentDate[2] = this.dayOptions.length - 1
			}
		},

		// 确认出生日期
		confirmBirthDate() {
			const year = this.yearOptions[this.tempBirthDate[0]]
			const month = this.monthOptions[this.tempBirthDate[1]]
			const day = this.dayOptions[this.tempBirthDate[2]]

			this.formData.birthDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.birthDatePickerValue = [...this.tempBirthDate]
			this.showDatePicker = false
		},

		// 确认入园日期
		confirmEnrollmentDate() {
			const year = this.yearOptions[this.tempEnrollmentDate[0]]
			const month = this.monthOptions[this.tempEnrollmentDate[1]]
			const day = this.dayOptions[this.tempEnrollmentDate[2]]

			this.formData.enrollmentDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.enrollmentDatePickerValue = [...this.tempEnrollmentDate]
			this.showEnrollmentDatePicker = false
		},

		// 格式化日期为YYYY-MM-DD
		formatDate(timestamp) {
			if (!timestamp) return ''
			const date = new Date(timestamp)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 格式化日期显示
		formatDateDisplay(dateStr) {
			if (!dateStr) return ''
			return dateStr.split(' ')[0] // 只显示日期部分
		},

		// 地址输入处理
		onAddressInput(e) {
			this.formData.address = e.detail.value
			this.addressLength = e.detail.value.length
		},

		// 显示班级选择器
		showClassPicker() {
			uni.showActionSheet({
				itemList: this.classOptions.map(item => item.label),
				success: (res) => {
					this.selectedClass = this.classOptions[res.tapIndex]
					this.formData.classId = this.selectedClass.value
				}
			})
		},

		// 备注输入处理
		onRemarkInput(e) {
			this.formData.remark = e.detail.value
			this.remarkLength = e.detail.value.length
		},

		// 地址输入处理
		onAddressInput(e) {
			this.formData.address = e.detail.value
			this.addressLength = e.detail.value.length
		},

		// 选择性别
		selectGender(value) {
			this.formData.gender = value
		},

		// 选择状态
		selectStatus(value) {
			this.formData.status = value
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.nav-right {
	background: rgba(255, 255, 255, 0.15);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.save-text {
	font-size: 24rpx;
	font-weight: 600;
	color: #ffffff;
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 表单容器 */
.form-container {
	padding: 30rpx 20rpx;
	padding-bottom: 120rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	padding: 24rpx 30rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-bottom: 1rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	gap: 12rpx;

	.title-text {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}
}

.form-item {
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}
}

.label {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
	line-height: 1.2;
}

.required {
	color: #f44336;
	margin-left: 4rpx;
}

.form-tip {
	font-size: 22rpx;
	color: #999;
	margin-top: 8rpx;
	line-height: 1.3;
}

.date-input {
	background: #f8f9fa;
	padding: 24rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 1rpx solid #e0e0e0;
	transition: all 0.3s ease;

	&:active {
		background: #f0f0f0;
		border-color: #667eea;
	}
}

.date-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;

	&:empty::before {
		content: attr(placeholder);
		color: #999;
	}
}

/* 保存按钮区域 */
.save-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx 30rpx;
	background: #ffffff;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	border-top: 1rpx solid #f0f0f0;
}

.save-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:active:not([disabled]) {
		transform: translateY(2rpx) scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.6);
	}

	&[disabled] {
		opacity: 0.6;
		transform: none;
	}
}

.save-btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 日期选择器样式 */
.date-picker-popup {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel {
	color: #999999;
	font-size: 28rpx;
}

.picker-title {
	color: #333333;
	font-size: 32rpx;
	font-weight: 600;
}

.picker-confirm {
	color: #667eea;
	font-size: 28rpx;
	font-weight: 600;
}

.date-picker-view {
	height: 400rpx;
	padding: 20rpx 0;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
}

/* 选择器样式 */
.picker-display {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	min-height: 80rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333333;

	&.placeholder {
		color: #c0c4cc;
	}
}

/* 自定义单选组件样式 */
.radio-group {
	display: flex;
	gap: 32rpx;
	margin-top: 8rpx;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e0e0e0;
	transition: all 0.3s ease;
	cursor: pointer;

	&.active {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		border-color: #667eea;
	}

	&:active {
		transform: scale(0.98);
	}
}

.radio-dot {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	border: 3rpx solid #ccc;
	background: #fff;
	transition: all 0.3s ease;

	&.active {
		border-color: #667eea;
		background: #667eea;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 8rpx;
			height: 8rpx;
			border-radius: 50%;
			background: #fff;
		}
	}
}

.radio-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
	
	.radio-item.active & {
		color: #667eea;
	}
}

/* 自定义 textarea 样式 */
.textarea-wrapper {
	position: relative;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
}

.custom-textarea {
	width: 100%;
	min-height: 120rpx;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
	border: none;
	outline: none;
	resize: none;
	line-height: 1.5;
}

.char-count {
	position: absolute;
	bottom: 8rpx;
	right: 12rpx;
	font-size: 22rpx;
	color: #999999;
	background: rgba(255, 255, 255, 0.8);
	padding: 2rpx 6rpx;
	border-radius: 6rpx;
}
</style>