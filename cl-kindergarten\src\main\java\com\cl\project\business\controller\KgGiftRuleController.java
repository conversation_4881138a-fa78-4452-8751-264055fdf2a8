package com.cl.project.business.controller;

import java.util.List;

import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.project.business.domain.KgGiftRule;
import com.cl.project.business.service.IKgGiftRuleService;

import static com.cl.framework.web.domain.AjaxResult.success;

/**
 * 赠送规则配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/business/gift/rule")
public class KgGiftRuleController extends BaseController
{
    @Autowired
    private IKgGiftRuleService kgGiftRuleService;

    /**
     * 查询赠送规则配置列表
     */
//    @SaCheckPermission("business:gift:rule:list")
    @GetMapping("/list")
    public TableDataInfo list(KgGiftRule kgGiftRule)
    {
        startPage();
        List<KgGiftRule> list = kgGiftRuleService.selectKgGiftRuleList(kgGiftRule);
        return getDataTable(list);
    }

    /**
     * 导出赠送规则配置列表
     */
//    @SaCheckPermission("business:gift:rule:export")
    @Log(title = "赠送规则配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgGiftRule kgGiftRule)
    {
        List<KgGiftRule> list = kgGiftRuleService.selectKgGiftRuleList(kgGiftRule);
        ExcelUtil<KgGiftRule> util = new ExcelUtil<KgGiftRule>(KgGiftRule.class);
        return util.exportExcel(list, "赠送规则配置数据");
    }

    /**
     * 获取赠送规则配置详细信息
     */
//    @SaCheckPermission("business:gift:rule:query")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        return success(kgGiftRuleService.selectKgGiftRuleByRuleId(ruleId));
    }

    /**
     * 新增赠送规则配置
     */
//    @SaCheckPermission("business:gift:rule:add")
    @Log(title = "赠送规则配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgGiftRule kgGiftRule)
    {
        return toAjax(kgGiftRuleService.insertKgGiftRule(kgGiftRule));
    }

    /**
     * 修改赠送规则配置
     */
//    @SaCheckPermission("business:gift:rule:edit")
    @Log(title = "赠送规则配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgGiftRule kgGiftRule)
    {
        return toAjax(kgGiftRuleService.updateKgGiftRule(kgGiftRule));
    }

    /**
     * 删除赠送规则配置
     */
//    @SaCheckPermission("business:gift:rule:remove")
    @Log(title = "赠送规则配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleIds}")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(kgGiftRuleService.deleteKgGiftRuleByRuleIds(ruleIds));
    }

    /**
     * 查询启用的赠送规则列表
     */
    @GetMapping("/active")
    public AjaxResult getActiveRules()
    {
        List<KgGiftRule> list = kgGiftRuleService.selectActiveGiftRules();
        return success(list);
    }

    /**
     * 根据规则类型查询启用的规则
     */
    @GetMapping("/active/{ruleType}")
    public AjaxResult getActiveRulesByType(@PathVariable String ruleType)
    {
        List<KgGiftRule> list = kgGiftRuleService.selectActiveGiftRulesByType(ruleType);
        return success(list);
    }

    /**
     * 修改规则状态
     */
//    @SaCheckPermission("business:gift:rule:edit")
    @Log(title = "修改规则状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody KgGiftRule kgGiftRule)
    {
        return toAjax(kgGiftRuleService.updateKgGiftRule(kgGiftRule));
    }
}
