<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">托管考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date" @click="showDatePickerDialog">
						<text class="date-text">{{ currentDate }}</text>
						<u-icon name="calendar" color="#667eea" size="16" class="calendar-icon"></u-icon>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

		<!-- 统计卡片显示 -->
		<view class="stats-section">
			<view
				class="stat-card total"
				:class="{ active: activeTab === 'all' }"
				@click="setActiveTab('all')"
			>
				<view class="stat-icon">👥</view>
				<view class="stat-info">
					<text class="stat-number">{{ statistics.totalStudents }}</text>
					<text class="stat-label">总人数</text>
				</view>
			</view>
			<view
				class="stat-card present"
				:class="{ active: activeTab === 'present' }"
				@click="setActiveTab('present')"
			>
				<view class="stat-icon">🏫</view>
				<view class="stat-info">
					<text class="stat-number">{{ statistics.presentCount }}</text>
					<text class="stat-label">已到课</text>
				</view>
			</view>
			<view
				class="stat-card absent"
				:class="{ active: activeTab === 'absent' }"
				@click="setActiveTab('absent')"
			>
				<view class="stat-icon">❌</view>
				<view class="stat-info">
					<text class="stat-number">{{ statistics.absentCount }}</text>
					<text class="stat-label">未到课</text>
				</view>
			</view>
		</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					type="text"
					placeholder="请输入学生姓名搜索"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
			
			<!-- 课程分类筛选 -->
			<view class="course-filter-section">
				<scroll-view scroll-x="true" class="course-filter-scroll">
					<view class="course-filter-tabs">
						<view 
							class="filter-tab" 
							:class="{ active: selectedCourseFilter === 'all' }" 
							@click="setCourseFilter('all')"
						>
							<text class="tab-text">全部</text>
							<text class="tab-count">({{ attendanceList.length }})</text>
						</view>
						<view 
							v-for="course in courseOptions" 
							:key="course.courseId"
							class="filter-tab" 
							:class="{ active: selectedCourseFilter === course.courseId }" 
							@click="setCourseFilter(course.courseId)"
						>
							<text class="tab-text">{{ course.courseName }}</text>
							<text class="tab-count">({{ getCourseStudentCount(course.courseId) }})</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-if="errorMessage && attendanceList.length === 0" class="error-state">
			<view class="error-icon">⚠️</view>
			<text class="error-text">{{ errorMessage }}</text>
			<view class="error-actions">
				<view class="error-action" @click="getList">
					<text class="action-text">重新加载</text>
				</view>
				<view class="error-action secondary" @click="refreshData">
					<text class="action-text">刷新页面</text>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="!loading && attendanceList.length === 0" class="empty-state">
			<view class="empty-icon">📋</view>
			<text class="empty-text">{{ currentDate }}暂无考勤记录</text>
			<view class="empty-sub-text">
				<text>今日还没有学生签到考勤</text>
			</view>
<!-- 			<view class="empty-action" @click="handleCheckin">
				<text class="action-text">开始签到</text>
			</view>
			<view class="empty-action secondary" @click="getList">
				<text class="action-text">刷新数据</text>
			</view> -->
		</view>

		<!-- 考勤记录列表 -->
		<view v-for="record in filteredAttendanceList" :key="record.attendanceId" class="attendance-record">
			<view class="record-header">
				<!-- 学生头像和基本信息 -->
				<view class="student-avatar">
					<text class="avatar-text">{{ record.studentName.charAt(0) }}</text>
				</view>
				<view class="record-info">
					<text class="student-name">{{ record.studentName }}</text>
					<text class="course-name">{{ record.courseName || '未分配课程' }}</text>
					<text class="attendance-date">{{ record.attendanceDate }}</text>
				</view>
				
				<!-- 考勤状态 -->
				<view class="attendance-status" :class="record.attendanceStatus">
					<text class="status-text">{{ getStatusText(record.attendanceStatus) }}</text>
				</view>
			</view>
			
			<!-- 考勤详细信息 -->
			<view class="record-details">
				<view v-if="record.checkInTime" class="detail-item">
					<text class="detail-label">签到时间:</text>
					<text class="detail-value">{{ record.checkInTime }}</text>
				</view>
				<view v-if="record.checkOutTime" class="detail-item">
					<text class="detail-label">签退时间:</text>
					<text class="detail-value">{{ record.checkOutTime }}</text>
				</view>
				<view v-if="record.remark" class="detail-item">
					<text class="detail-label">备注:</text>
					<text class="detail-value">{{ record.remark }}</text>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="record-actions">
				<view v-if="!record.isConfirmed" class="action-btn confirm" @click="handleConfirm(record)">
					<text class="btn-text">确认</text>
				</view>
				<view class="action-btn delete" @click="handleDelete(record)">
					<text class="btn-text">删除</text>
				</view>
			</view>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-action-btn" @click="toggleFloatingMenu">
			<u-icon :name="showFloatingActions ? 'close' : 'plus'" color="#ffffff" size="24"></u-icon>
		</view>

		<!-- 浮动操作菜单 -->
		<view v-if="showFloatingActions" class="floating-actions-menu">
			<view class="floating-action-item" @click="handleCheckin">
				<view class="action-icon checkin">
					<u-icon name="account" color="#ffffff" size="20"></u-icon>
				</view>
				<text class="action-text">学生签到</text>
			</view>
			<view v-if="hasUnconfirmedRecords" class="floating-action-item" @click="showBatchConfirmDialog">
				<view class="action-icon confirm">
					<u-icon name="checkmark-circle" color="#ffffff" size="20"></u-icon>
				</view>
				<text class="action-text">批量确认</text>
			</view>
		</view>

		<!-- 遮罩层 -->
		<view v-if="showFloatingActions" class="floating-mask" @click="closeFloatingMenu"></view>

		<!-- 日期选择器 -->
		<CustomDatePicker
			:show="showDatePicker"
			:value="pickerValue"
			title="选择考勤日期"
			@confirm="onDateConfirm"
			@cancel="onDateCancel"
			@update:show="showDatePicker = $event">
		</CustomDatePicker>

		<!-- 签到表单弹窗 -->
		<view v-if="showSignInDialog" class="dialog-overlay" @click="closeSignInDialog">
			<view class="dialog-container" @click.stop>
				<view class="dialog-header">
					<text class="dialog-title">学生签到</text>
					<view class="dialog-close" @click="closeSignInDialog">
						<u-icon name="close" color="#999999" size="20"></u-icon>
					</view>
				</view>
				
				<view class="dialog-content">
					<!-- 报名记录选择 -->
					<view class="form-item">
						<text class="form-label">选择学生</text>
						<view class="form-control">
							<picker 
								:range="enrollmentPickerOptions" 
								:value="selectedEnrollmentIndex"
								@change="handleEnrollmentChange"
								class="enrollment-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ selectedEnrollmentIndex >= 0 ? enrollmentPickerOptions[selectedEnrollmentIndex] : '请选择报名学生' }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 学生信息显示 -->
					<view v-if="signInForm.enrollmentId" class="student-info-card">
						<view class="info-row">
							<text class="info-label">学生姓名:</text>
							<text class="info-value">{{ selectedEnrollment.studentName }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">课程名称:</text>
							<text class="info-value">{{ selectedEnrollment.courseName }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">剩余课时:</text>
							<text class="info-value">{{ selectedEnrollment.remainingSessions }}课时</text>
						</view>
					</view>

					<!-- 授课教师选择 -->
					<view class="form-item">
						<text class="form-label">授课教师</text>
						<view class="form-control">
							<picker 
								:range="teacherPickerOptions" 
								:value="selectedTeacherIndex"
								@change="handleTeacherChange"
								class="teacher-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ selectedTeacherIndex >= 0 ? teacherPickerOptions[selectedTeacherIndex] : '请选择教师' }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 考勤日期 -->
					<view class="form-item">
						<text class="form-label">考勤日期</text>
						<view class="form-control">
							<view class="date-display" @click="openSignInDatePicker">
								<text class="date-text">{{ signInForm.attendanceDate || '选择日期' }}</text>
								<u-icon name="calendar" color="#999999" size="14"></u-icon>
							</view>
						</view>
					</view>

					<!-- 考勤状态选择 -->
					<view class="form-item">
						<text class="form-label">考勤状态</text>
						<view class="form-control">
							<picker 
								:range="statusPickerOptions" 
								:value="selectedStatusIndex"
								@change="handleStatusChange"
								class="status-picker"
							>
								<view class="picker-display">
									<text class="picker-text">
										{{ statusPickerOptions[selectedStatusIndex] }}
									</text>
									<u-icon name="arrow-down" color="#999999" size="14"></u-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 备注 -->
					<view class="form-item">
						<text class="form-label">备注</text>
						<view class="form-control">
							<textarea 
								v-model="signInForm.remark"
								placeholder="请输入备注信息"
								class="remark-textarea"
								maxlength="200"
							></textarea>
						</view>
					</view>
				</view>

				<view class="dialog-footer">
					<view class="dialog-btn cancel" @click="closeSignInDialog">
						<text class="btn-text">取消</text>
					</view>
					<view class="dialog-btn confirm" @click="submitSignIn">
						<text class="btn-text">确定签到</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import {
	listCourseAttendance,
	getCourseAttendance,
	courseCheckin,
	confirmSingleAttendance,
	confirmCourseAttendance,
	delCourseAttendance,
	listAllCourse,
	listActiveEnrollment,
	listAllTeacher
} from '@/api/custodyAttendance.js'
import CustomDatePicker from '@/components/CustomDatePicker/CustomDatePicker.vue'

export default {
	components: {
		CustomDatePicker
	},
	data() {
		return {
			loading: false,
			currentDate: '',
			currentDateValue: '', // 用于API请求的日期格式
			selectedCourse: 'all',
			
			// 考勤记录列表（对应web端的attendanceList）
			attendanceList: [],
			
			// 统计数据（对应web端的statistics）
			statistics: {
				totalStudents: 0,
				presentCount: 0,
				absentCount: 0,
				attendanceRate: 0
			},
			
			// 筛选和显示状态（对应web端）
			activeTab: 'all', // 当前活动标签页
			courseOptions: [], // 课程选项
			selectedCourseFilter: 'all', // 选中的课程筛选
			enrollmentOptions: [], // 报名记录选项
			teacherOptions: [], // 教师选项
			selectedEnrollment: {}, // 选中的报名记录
			
			errorMessage: '', // 错误信息
			retryCount: 0, // 重试次数
			maxRetries: 3, // 最大重试次数
			
			// 搜索相关
			searchKeyword: '', // 搜索关键词
			searchTimer: null, // 搜索防抖定时器
			
			// 浮动菜单相关
			showFloatingActions: false,
			
			// 日期选择器相关
			showDatePicker: false,
			pickerValue: '',
			
			// 签到表单相关
			showSignInDialog: false,
			signInForm: {
				attendanceId: null,
				enrollmentId: null,
				studentId: null,
				courseId: null,
				teacherId: null,
				attendanceDate: null,
				attendanceStatus: "present",
				checkInMethod: "manual",
				remark: null
			},
			
			// 表单选择器相关
			selectedEnrollmentIndex: -1,
			selectedTeacherIndex: -1,
			selectedStatusIndex: 0,
			
			// 查询参数（对应web端的queryParams）
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				studentName: null,
				courseId: null,
				attendanceDate: null,
				attendanceStatus: null
			},
			
			// 表单数据（对应web端的form）
			form: {
				attendanceId: null,
				enrollmentId: null,
				studentId: null,
				courseId: null,
				teacherId: null,
				attendanceDate: null,
				startTime: null,
				endTime: null,
				attendanceStatus: "present",
				checkInMethod: "manual",
				remark: null
			},
			
			// UI状态
			open: false, // 弹窗状态（对应web端）
			title: '', // 弹窗标题
			total: 0, // 总数量
			ids: [], // 选中的ID列表
			single: true,
			multiple: true,
			showSearch: true
		}
	},
	
	computed: {
		// 根据活动标签和课程筛选过滤考勤列表（对应web端的filteredAttendanceList）
		filteredAttendanceList() {
			let list = this.attendanceList;
			
			// 根据课程筛选
			if (this.selectedCourseFilter !== 'all') {
				list = list.filter(item => 
					item.courseId && item.courseId.toString() === this.selectedCourseFilter.toString()
				);
			}
			
			// 根据活动标签过滤 (原有逻辑保留)
			if (this.activeTab !== 'all') {
				list = list.filter(item => 
					item.courseId && item.courseId.toString() === this.activeTab
				);
			}
			
			return list;
		},
		
		// 报名记录选择器选项
		enrollmentPickerOptions() {
			return this.enrollmentOptions.map(item => 
				`${item.studentName} - ${item.courseName} (剩余${item.remainingSessions}课时)`
			);
		},
		
		// 教师选择器选项
		teacherPickerOptions() {
			return this.teacherOptions.map(item => item.teacherName);
		},
		
		// 状态选择器选项
		statusPickerOptions() {
			return ['出勤', '缺勤', '迟到', '早退'];
		},
		
		// 检查是否有未确认的记录
		hasUnconfirmedRecords() {
			return this.attendanceList.some(item => !item.isConfirmed);
		}
	},

	onLoad() {
		this.initCurrentDate()
		this.initDatePicker()
		this.getList() // 对应web端的getList方法
		this.loadOptions() // 对应web端的loadOptions方法
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.getList().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	// 页面显示时刷新数据
	onShow() {
		// 如果数据已加载过，则静默刷新
		if (this.attendanceList.length > 0) {
			this.getList()
		}
	},
	
	methods: {
		// 初始化当前日期
		initCurrentDate() {
			const today = new Date()
			const year = today.getFullYear()
			const month = String(today.getMonth() + 1).padStart(2, '0')
			const day = String(today.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[today.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateValue = `${year}-${month}-${day}`
		},

		// 初始化日期选择器
		initDatePicker() {
			this.pickerValue = this.currentDateValue
		},

		// 显示日期选择器
		showDatePickerDialog() {
			this.showDatePicker = true
		},

		// 日期选择确认
		onDateConfirm(selectedDate) {
			this.currentDateValue = selectedDate
			this.pickerValue = selectedDate

			// 更新显示的日期
			const date = new Date(selectedDate)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[date.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.showDatePicker = false

			// 重置页码并重新加载数据
			this.queryParams.pageNum = 1
			this.getList()
		},

		// 日期选择取消
		onDateCancel() {
			this.showDatePicker = false
		},

		// 切换日期 (对应web端的日期筛选)
		changeDate(days) {
			const currentDate = new Date(this.currentDateValue)
			currentDate.setDate(currentDate.getDate() + days)

			const year = currentDate.getFullYear()
			const month = String(currentDate.getMonth() + 1).padStart(2, '0')
			const day = String(currentDate.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[currentDate.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateValue = `${year}-${month}-${day}`

			// 重置页码并重新加载数据
			this.queryParams.pageNum = 1
			this.getList()
		},

		/** 查询考勤列表（对应web端的getList方法） */
		async getList() {
			try {
				console.log('开始查询考勤列表...');
				this.loading = true;
				
				// 简化查询参数，只传必要的参数
				const params = {
					pageNum: this.queryParams.pageNum,
					pageSize: this.queryParams.pageSize,
					attendanceDate: this.currentDateValue
				};
				
				// 只有在有值时才添加其他参数
				if (this.queryParams.studentName) {
					params.studentName = this.queryParams.studentName;
				}
				if (this.queryParams.courseId) {
					params.courseId = this.queryParams.courseId;
				}
				if (this.queryParams.attendanceStatus) {
					params.attendanceStatus = this.queryParams.attendanceStatus;
				}
				
				console.log('查询参数:', params);
				
				const response = await listCourseAttendance(params);
				console.log('接口响应:', response);
				
				if (response.code === 200) {
					this.attendanceList = response.rows || [];
					this.total = response.total || 0;
					console.log('考勤列表加载成功:', this.attendanceList.length, '条记录');
					console.log('考勤数据:', this.attendanceList);
					this.calculateStatistics();
					this.retryCount = 0;
				} else {
					console.error('接口返回错误:', response);
					throw new Error(response.msg || '获取数据失败');
				}
			} catch (error) {
				console.error('查询考勤列表失败:', error);
				this.errorMessage = error.message || '获取数据失败';
				
				if (this.retryCount < this.maxRetries) {
					uni.showModal({
						title: '网络错误',
						content: `加载失败，是否重试？(${this.retryCount + 1}/${this.maxRetries})`,
						success: (res) => {
							if (res.confirm) {
								this.retryCount++;
								setTimeout(() => {
									this.getList();
								}, 1000);
							}
						}
					});
				} else {
					toast(this.errorMessage);
				}
			} finally {
				this.loading = false;
			}
		},

		/** 计算统计数据（对应web端的calculateStatistics方法） */
		calculateStatistics() {
			const list = this.activeTab === 'all' ? this.attendanceList : this.filteredAttendanceList;
			this.statistics.totalStudents = list.length;
			this.statistics.presentCount = list.filter(item => item.attendanceStatus === 'present').length;
			this.statistics.absentCount = list.filter(item => item.attendanceStatus === 'absent').length;
			this.statistics.attendanceRate = this.statistics.totalStudents > 0 
				? Math.round((this.statistics.presentCount / this.statistics.totalStudents) * 100)
				: 0;
		},

		/** 加载选项数据（对应web端的loadOptions方法） */
		async loadOptions() {
			try {
				console.log('开始加载选项数据...');
				
				// 加载课程选项
				try {
					const courseResponse = await listAllCourse();
					console.log('课程响应:', courseResponse);
					if (courseResponse.code === 200) {
						this.courseOptions = courseResponse.data || [];
						console.log('课程选项加载成功:', this.courseOptions.length, '个');
					} else {
						console.error('课程加载失败:', courseResponse.msg);
					}
				} catch (error) {
					console.error('课程接口调用失败:', error);
				}
				
				// 加载活跃的报名记录
				try {
					const enrollmentResponse = await listActiveEnrollment({ status: 'active' });
					console.log('报名响应:', enrollmentResponse);
					if (enrollmentResponse.code === 200) {
						this.enrollmentOptions = enrollmentResponse.rows || [];
						console.log('报名选项加载成功:', this.enrollmentOptions.length, '个');
					} else {
						console.error('报名加载失败:', enrollmentResponse.msg);
					}
				} catch (error) {
					console.error('报名接口调用失败:', error);
				}
				
				// 加载教师选项
				try {
					const teacherResponse = await listAllTeacher();
					console.log('教师响应:', teacherResponse);
					if (teacherResponse.code === 200) {
						this.teacherOptions = teacherResponse.data || [];
						console.log('教师选项加载成功:', this.teacherOptions.length, '个');
					} else {
						console.error('教师加载失败:', teacherResponse.msg);
					}
				} catch (error) {
					console.error('教师接口调用失败:', error);
				}
				
				console.log('选项数据加载完成');
			} catch (error) {
				console.error('加载选项数据失败:', error);
			}
		},

		/** 搜索按钮操作（对应web端的handleQuery方法） */
		handleQuery() {
			this.queryParams.pageNum = 1;
			this.getList();
		},

		/** 重置查询（对应web端的resetQuery方法） */
		resetQuery() {
			this.searchKeyword = '';
			this.queryParams = {
				pageNum: 1,
				pageSize: 10,
				studentName: null,
				courseId: null,
				attendanceDate: this.currentDateValue,
				attendanceStatus: null
			};
			this.getList();
		},

		/** 设置活动标签页（对应web端的activeTab切换） */
		setActiveTab(tab) {
			this.activeTab = tab;
			this.calculateStatistics();
		},

		/** 切换记录选择状态 */
		toggleRecordSelection(record) {
			const attendanceId = record.attendanceId;
			const index = this.selectedStudents.indexOf(attendanceId);
			if (index > -1) {
				this.selectedStudents.splice(index, 1);
			} else {
				this.selectedStudents.push(attendanceId);
			}
			// 更新多选状态
			this.ids = this.selectedStudents;
			this.single = this.selectedStudents.length !== 1;
			this.multiple = !this.selectedStudents.length;
		},

		/** 确认按钮操作（对应web端的handleConfirm方法） */
		async handleConfirm(record) {
			try {
				uni.showLoading({ title: '确认中...' });
				
				const response = await confirmSingleAttendance(record.attendanceId);
				
				if (response.code === 200) {
					toast('确认成功');
					this.getList(); // 重新加载数据
				} else {
					toast(response.msg || '确认失败');
				}
			} catch (error) {
				console.error('确认考勤失败:', error);
				toast('确认失败，请重试');
			} finally {
				uni.hideLoading();
			}
		},

		/** 修改按钮操作（对应web端的handleUpdate方法） */
		async handleUpdate(record) {
			try {
				this.loading = true;
				const response = await getCourseAttendance(record.attendanceId);
				
				if (response.code === 200) {
					this.form = response.data;
					this.open = true;
					this.title = "修改考勤";
					// 这里可以跳转到编辑页面或显示弹窗
					uni.navigateTo({
						url: `/pages/admin/custody/attendance/edit?attendanceId=${record.attendanceId}`
					});
				} else {
					toast(response.msg || '获取考勤信息失败');
				}
			} catch (error) {
				console.error('获取考勤信息失败:', error);
				toast('获取信息失败，请重试');
			} finally {
				this.loading = false;
			}
		},

		/** 删除按钮操作（对应web端的handleDelete方法） */
		handleDelete(record) {
			const attendanceId = record.attendanceId;
			uni.showModal({
				title: '确认删除',
				content: `确定要删除${record.studentName}的考勤记录吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '删除中...' });
							
							const response = await delCourseAttendance(attendanceId);
							
							if (response.code === 200) {
								toast('删除成功');
								this.getList(); // 重新加载数据
							} else {
								toast(response.msg || '删除失败');
							}
						} catch (error) {
							console.error('删除考勤记录失败:', error);
							toast('删除失败，请重试');
						} finally {
							uni.hideLoading();
						}
					}
				}
			});
		},

		/** 签到按钮操作（对应web端的handleCheckin方法） */
		handleCheckin() {
			console.log('点击签到按钮');
			// 关闭浮动菜单
			this.closeFloatingMenu();
			
			// 检查是否有可用的报名记录
			if (this.enrollmentOptions.length === 0) {
				toast('暂无可签到的学生，请先确保有学生报名');
				return;
			}
			
			// 重置表单并显示弹窗
			this.resetSignInForm();
			this.showSignInDialog = true;
		},
		
		// 重置签到表单
		resetSignInForm() {
			this.signInForm = {
				attendanceId: null,
				enrollmentId: null,
				studentId: null,
				courseId: null,
				teacherId: null,
				attendanceDate: this.currentDateValue, // 默认为当前日期
				attendanceStatus: "present",
				checkInMethod: "manual",
				remark: null
			};
			this.selectedEnrollmentIndex = -1;
			this.selectedTeacherIndex = -1;
			this.selectedStatusIndex = 0;
			this.selectedEnrollment = {};
		},
		
		// 关闭签到表单弹窗
		closeSignInDialog() {
			this.showSignInDialog = false;
		},
		
		// 处理报名记录选择变化
		handleEnrollmentChange(e) {
			const index = e.detail.value;
			this.selectedEnrollmentIndex = index;
			
			if (index >= 0 && index < this.enrollmentOptions.length) {
				const enrollment = this.enrollmentOptions[index];
				this.selectedEnrollment = enrollment;
				this.signInForm.enrollmentId = enrollment.enrollmentId;
				this.signInForm.studentId = enrollment.studentId;
				this.signInForm.courseId = enrollment.courseId;
				
				// 自动选择教师（如果有默认教师）
				if (enrollment.teacherId) {
					const teacherIndex = this.teacherOptions.findIndex(t => t.teacherId === enrollment.teacherId);
					if (teacherIndex >= 0) {
						this.selectedTeacherIndex = teacherIndex;
						this.signInForm.teacherId = enrollment.teacherId;
					}
				}
			}
		},
		
		// 处理教师选择变化
		handleTeacherChange(e) {
			const index = e.detail.value;
			this.selectedTeacherIndex = index;
			
			if (index >= 0 && index < this.teacherOptions.length) {
				const teacher = this.teacherOptions[index];
				this.signInForm.teacherId = teacher.teacherId;
			}
		},
		
		// 处理状态选择变化
		handleStatusChange(e) {
			const index = e.detail.value;
			this.selectedStatusIndex = index;
			
			const statusMap = ['present', 'absent', 'late', 'early'];
			this.signInForm.attendanceStatus = statusMap[index];
		},
		
		// 打开签到日期选择器
		openSignInDatePicker() {
			// 可以复用现有的日期选择器或者创建新的
			this.pickerValue = this.signInForm.attendanceDate || this.currentDateValue;
			this.showDatePicker = true;
		},
		
		// 提交签到表单
		async submitSignIn() {
			// 表单验证
			if (!this.signInForm.enrollmentId) {
				toast('请选择学生');
				return;
			}
			
			if (!this.signInForm.attendanceDate) {
				toast('请选择考勤日期');
				return;
			}
			
			try {
				uni.showLoading({ title: '签到中...' });
				
				console.log('提交签到表单:', this.signInForm);
				
				const response = await courseCheckin(this.signInForm);
				console.log('签到响应:', response);
				
				if (response.code === 200) {
					toast(`${this.selectedEnrollment.studentName} 签到成功`);
					this.closeSignInDialog();
					this.getList(); // 重新加载考勤列表
				} else {
					toast(response.msg || '签到失败');
				}
			} catch (error) {
				console.error('签到失败:', error);
				toast('签到失败，请重试');
			} finally {
				uni.hideLoading();
			}
		},

		/** 批量确认按钮操作（对应web端的handleBatchConfirm方法） */
		async handleBatchConfirm() {
			if (this.selectedStudents.length === 0) {
				toast('请先选择考勤记录');
				return;
			}

			try {
				uni.showLoading({ title: '批量确认中...' });
				
				const response = await confirmCourseAttendance(this.selectedStudents);
				
				if (response.code === 200) {
					toast(`批量确认成功，共${this.selectedStudents.length}条记录`);
					this.selectedStudents = [];
					this.isSelectionMode = false;
					this.getList(); // 重新加载数据
				} else {
					toast(response.msg || '批量确认失败');
				}
			} catch (error) {
				console.error('批量确认失败:', error);
				toast('批量确认失败，请重试');
			} finally {
				uni.hideLoading();
			}
		},

		// 搜索处理
		handleSearch() {
			// 防抖处理
			clearTimeout(this.searchTimer);
			this.searchTimer = setTimeout(() => {
				console.log('搜索:', this.searchKeyword);
				this.queryParams.studentName = this.searchKeyword || null;
				this.queryParams.pageNum = 1;
				this.getList();
			}, 300);
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.queryParams.studentName = null
			this.getList()
		},

		// 切换浮动菜单显示
		toggleFloatingMenu() {
			this.showFloatingActions = !this.showFloatingActions;
		},

		// 关闭浮动菜单
		closeFloatingMenu() {
			this.showFloatingActions = false;
		},

		// 显示批量确认对话框
		showBatchConfirmDialog() {
			// 关闭浮动菜单
			this.closeFloatingMenu();
			
			const unconfirmedRecords = this.attendanceList.filter(item => !item.isConfirmed);
			
			if (unconfirmedRecords.length === 0) {
				toast('暂无待确认的考勤记录');
				return;
			}
			
			uni.showModal({
				title: '批量确认考勤',
				content: `确认批量确认 ${unconfirmedRecords.length} 条待确认的考勤记录吗？`,
				confirmText: '确认',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 获取所有未确认记录的ID
						const attendanceIds = unconfirmedRecords.map(item => item.attendanceId);
						this.performBatchConfirm(attendanceIds);
					}
				}
			});
		},

		// 执行批量确认
		async performBatchConfirm(attendanceIds) {
			try {
				uni.showLoading({ title: '批量确认中...' });
				
				const response = await confirmCourseAttendance(attendanceIds);
				
				if (response.code === 200) {
					toast(`批量确认成功，共${attendanceIds.length}条记录`);
					this.getList(); // 重新加载数据
				} else {
					toast(response.msg || '批量确认失败');
				}
			} catch (error) {
				console.error('批量确认失败:', error);
				toast('批量确认失败，请重试');
			} finally {
				uni.hideLoading();
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 刷新数据
		refreshData() {
			this.getList()
		},

		// 获取状态文本 (对应web端的状态映射)
		getStatusText(status) {
			const statusMap = {
				present: '已到课',
				absent: '未到课',
				late: '迟到',
				early: '早退',
				leave: '请假'
			}
			return statusMap[status] || '未知'
		},

		/** 设置课程筛选 */
		setCourseFilter(courseId) {
			this.selectedCourseFilter = courseId;
			this.calculateStatistics();
		},

		/** 获取某课程的学生数量 */
		getCourseStudentCount(courseId) {
			return this.attendanceList.filter(item => 
				item.courseId && item.courseId.toString() === courseId.toString()
			).length;
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	position: absolute;
	left: 32rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.header-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 加载状态 */
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

/* 日期和统计卡片 */
.date-stats-card {
	background: #ffffff;
	margin: 30rpx;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	overflow: hidden;
}

.date-section {
	padding: 32rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.current-date {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx 32rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 40rpx;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.98);
	}
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.calendar-icon {
	margin-left: 8rpx;
}

/* 统计区域 */
.stats-section {
	display: flex;
	gap: 16rpx;
	padding: 30rpx 32rpx;
	background: #ffffff;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.active {
		transform: scale(1.05);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }

		&.active {
			background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
		}
	}

	&.present {
		background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
		&::before { background: linear-gradient(90deg, #03a9f4 0%, #0288d1 100%); }

		&.active {
			background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
		}
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }

		&.active {
			background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
		}
	}
}

.stat-icon {
	font-size: 48rpx;
}

.stat-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	line-height: 1;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 搜索栏样式 */
.search-section {
	margin: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:focus-within {
		border-color: rgba(102, 126, 234, 0.3);
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	}
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	margin-left: 16rpx;
	cursor: pointer;
}

/* 课程筛选区域 */
.course-filter-section {
	background: #ffffff;
	padding: 16rpx 30rpx;
	margin-top: 16rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.course-filter-scroll {
	white-space: nowrap;
}

.course-filter-tabs {
	display: flex;
	gap: 12rpx;
	padding: 8rpx 0;
}

.filter-tab {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 40rpx;
	padding: 16rpx 24rpx;
	flex-shrink: 0;
	transition: all 0.3s ease;
	min-height: 60rpx;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		transform: scale(1.02);
		
		.tab-text, .tab-count {
			color: #ffffff;
		}
	}
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	white-space: nowrap;
}

.tab-count {
	font-size: 24rpx;
	color: #999999;
	background: rgba(153, 153, 153, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	
	.filter-tab.active & {
		background: rgba(255, 255, 255, 0.2);
		color: #ffffff;
	}
}

/* 错误状态 */
.error-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.error-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.error-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.error-actions {
	display: flex;
	gap: 24rpx;
}

.error-action {
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&:not(.secondary) {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #e0e0e0;
	}
}

.action-text {
	font-size: 28rpx;
	font-weight: 500;

	.error-action:not(.secondary) & {
		color: #ffffff;
	}

	.error-action.secondary & {
		color: #333333;
	}
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.empty-sub-text {
	margin-top: -16rpx;
}

.empty-sub-text text {
	font-size: 28rpx;
	color: #cccccc;
	text-align: center;
}

.empty-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40rpx;
	margin-top: 16rpx;

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #e0e0e0;
		margin-top: 8rpx;
	}

	&:active {
		transform: scale(0.95);
	}
}

/* 考勤记录卡片 */
.attendance-record {
	background: #ffffff;
	margin: 0 30rpx 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}

.record-header {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	gap: 24rpx;
}

.record-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.course-name {
	font-size: 26rpx;
	color: #666666;
}

.attendance-date {
	font-size: 24rpx;
	color: #999999;
}

.attendance-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;

	&.present {
		background: rgba(76, 175, 80, 0.1);
		color: #4caf50;
	}

	&.absent {
		background: rgba(244, 67, 54, 0.1);
		color: #f44336;
	}
}

.record-details {
	padding: 0 32rpx 24rpx;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.detail-label {
	font-size: 26rpx;
	color: #666666;
	min-width: 120rpx;
}

.detail-value {
	font-size: 26rpx;
	color: #333333;
	flex: 1;
}

.record-actions {
	display: flex;
	justify-content: flex-end;
	gap: 16rpx;
	padding: 0 32rpx 24rpx;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.confirm {
		background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
		color: #ffffff;
	}

	&.delete {
		background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
		color: #ffffff;
	}
}

.btn-text {
	font-size: 24rpx;
	font-weight: 500;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 浮动操作按钮 */
.floating-action-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;
	z-index: 1001;

	&:active {
		transform: scale(0.9);
	}
}

/* 浮动操作菜单 */
.floating-actions-menu {
	position: fixed;
	bottom: 260rpx;
	right: 60rpx;
	z-index: 1000;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	animation: fadeInUp 0.3s ease-out;
}

.floating-action-item {
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 60rpx;
	padding: 16rpx 32rpx 16rpx 16rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
	min-width: 200rpx;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 1);
	}
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	
	&.checkin {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	}
	
	&.confirm {
		background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
	}
}

.action-text {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.floating-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: transparent;
	z-index: 999;
}

/* 签到表单弹窗样式 */
.dialog-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
	padding: 40rpx;
	backdrop-filter: blur(4rpx);
}

.dialog-container {
	background: #ffffff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 640rpx;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	animation: dialogScale 0.3s ease-out;
}

@keyframes dialogScale {
	0% {
		opacity: 0;
		transform: scale(0.8) translateY(40rpx);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

@keyframes fadeInUp {
	0% {
		opacity: 0;
		transform: translateY(40rpx);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.dialog-header {
	padding: 48rpx 48rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f2f5;
	background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);
}

.dialog-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #1a1a1a;
	display: flex;
	align-items: center;
	
	&::before {
		content: '';
		width: 6rpx;
		height: 36rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 3rpx;
		margin-right: 16rpx;
	}
}

.dialog-close {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: #f5f5f7;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	&:active {
		background: #e5e5e7;
		transform: scale(0.9);
	}
}

.dialog-content {
	padding: 40rpx 48rpx;
	max-height: 65vh;
	overflow-y: auto;
}

.form-item {
	margin-bottom: 48rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #1a1a1a;
	margin-bottom: 20rpx;
	
	&::after {
		content: '*';
		color: #ff4757;
		margin-left: 8rpx;
		display: none;
	}
	
	&.required::after {
		display: inline;
	}
}

.form-control {
	position: relative;
}

.picker-display, .date-display {
	background: #f8f9fb;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 28rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.3s ease;
	position: relative;
	
	&:active {
		background: #f0f2f6;
		border-color: #667eea;
		transform: scale(0.98);
	}
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 16rpx;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
		opacity: 0;
		transition: all 0.3s ease;
		z-index: -1;
	}
	
	&:active::before {
		opacity: 1;
	}
}

.picker-text, .date-text {
	font-size: 30rpx;
	color: #1a1a1a;
	flex: 1;
	font-weight: 500;
}

.student-info-card {
	background: linear-gradient(135deg, #f8f9fb 0%, #e9ecf3 100%);
	border: 2rpx solid #e4e6ea;
	border-radius: 20rpx;
	padding: 32rpx;
	margin: 24rpx 0 40rpx;
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 6rpx;
		height: 100%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}
}

.info-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.info-label {
	font-size: 28rpx;
	color: #6c757d;
	min-width: 140rpx;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
}

.remark-textarea {
	background: #f8f9fb;
	border: 2rpx solid #e4e6ea;
	border-radius: 16rpx;
	padding: 24rpx;
	font-size: 30rpx;
	color: #1a1a1a;
	width: 100%;
	min-height: 140rpx;
	resize: none;
	transition: all 0.3s ease;
	font-family: inherit;
	
	&:focus {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
	
	&::placeholder {
		color: #9ca3af;
	}
}

.dialog-footer {
	padding: 24rpx 48rpx 48rpx;
	display: flex;
	gap: 24rpx;
	justify-content: flex-end;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fb 100%);
}

.dialog-btn {
	padding: 24rpx 48rpx;
	border-radius: 16rpx;
	font-size: 30rpx;
	font-weight: 600;
	text-align: center;
	min-width: 160rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s ease;
	}
	
	&:active {
		transform: scale(0.95);
	}
	
	&:active::before {
		left: 100%;
	}
	
	&.cancel {
		background: #f8f9fb;
		color: #6c757d;
		border: 2rpx solid #e4e6ea;
		
		&:active {
			background: #e9ecef;
			color: #495057;
		}
	}
	
	&.confirm {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
		
		&:active {
			box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
		}
	}
}

.btn-text {
	font-size: 30rpx;
	font-weight: 600;
	position: relative;
	z-index: 1;
}
</style>