package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 预交款账户对象 kg_prepayment_account
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class KgPrepaymentAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账户ID */
    private Long accountId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 账户类型（tuition园费、course托管费） */
    @Excel(name = "账户类型", readConverterExp = "t=uition园费、course托管费")
    private String accountType;

    /** 累计预交金额 */
    @Excel(name = "累计预交金额")
    private BigDecimal totalPrepaid;

    /** 累计使用金额 */
    @Excel(name = "累计使用金额")
    private BigDecimal totalUsed;

    /** 当前余额 */
    @Excel(name = "当前余额")
    private BigDecimal balance;

    /** 冻结金额（退园时冻结） */
    @Excel(name = "冻结金额", readConverterExp = "退=园时冻结")
    private BigDecimal frozenAmount;

    /** 可用余额 */
    @Excel(name = "可用余额")
    private BigDecimal availableBalance;

    /** 最后充值时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后充值时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastRechargeTime;

    /** 最后使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsageTime;

    /** 账户状态（active活跃、frozen冻结、closed关闭） */
    @Excel(name = "账户状态", readConverterExp = "a=ctive活跃、frozen冻结、closed关闭")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setAccountId(Long accountId) 
    {
        this.accountId = accountId;
    }

    public Long getAccountId() 
    {
        return accountId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setAccountType(String accountType) 
    {
        this.accountType = accountType;
    }

    public String getAccountType() 
    {
        return accountType;
    }
    public void setTotalPrepaid(BigDecimal totalPrepaid) 
    {
        this.totalPrepaid = totalPrepaid;
    }

    public BigDecimal getTotalPrepaid() 
    {
        return totalPrepaid;
    }
    public void setTotalUsed(BigDecimal totalUsed) 
    {
        this.totalUsed = totalUsed;
    }

    public BigDecimal getTotalUsed() 
    {
        return totalUsed;
    }
    public void setBalance(BigDecimal balance) 
    {
        this.balance = balance;
    }

    public BigDecimal getBalance() 
    {
        return balance;
    }
    
    public void setCurrentBalance(BigDecimal currentBalance) 
    {
        this.balance = currentBalance;
    }

    public BigDecimal getCurrentBalance() 
    {
        return balance;
    }
    public void setFrozenAmount(BigDecimal frozenAmount) 
    {
        this.frozenAmount = frozenAmount;
    }

    public BigDecimal getFrozenAmount() 
    {
        return frozenAmount;
    }
    public void setAvailableBalance(BigDecimal availableBalance) 
    {
        this.availableBalance = availableBalance;
    }

    public BigDecimal getAvailableBalance() 
    {
        return availableBalance;
    }
    public void setLastRechargeTime(Date lastRechargeTime) 
    {
        this.lastRechargeTime = lastRechargeTime;
    }

    public Date getLastRechargeTime() 
    {
        return lastRechargeTime;
    }
    public void setLastUsageTime(Date lastUsageTime) 
    {
        this.lastUsageTime = lastUsageTime;
    }

    public Date getLastUsageTime() 
    {
        return lastUsageTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setAccountStatus(String accountStatus) 
    {
        this.status = accountStatus;
    }

    public String getAccountStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("accountId", getAccountId())
            .append("studentId", getStudentId())
            .append("accountType", getAccountType())
            .append("totalPrepaid", getTotalPrepaid())
            .append("totalUsed", getTotalUsed())
            .append("balance", getBalance())
            .append("frozenAmount", getFrozenAmount())
            .append("availableBalance", getAvailableBalance())
            .append("lastRechargeTime", getLastRechargeTime())
            .append("lastUsageTime", getLastUsageTime())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
