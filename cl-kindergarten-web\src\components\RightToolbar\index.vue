<template>
  <div class="right-toolbar">
    <el-row :gutter="10">
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="显示/隐藏搜索" placement="top">
          <el-button 
            size="mini" 
            circle 
            type="text" 
            icon="el-icon-search" 
            @click="toggleSearch"
          />
        </el-tooltip>
      </el-col>
      <el-col :span="1.5">
        <el-tooltip class="item" effect="dark" content="刷新" placement="top">
          <el-button 
            size="mini" 
            circle 
            type="text" 
            icon="el-icon-refresh" 
            @click="queryTable"
          />
        </el-tooltip>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'RightToolbar',
  props: {
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch);
    },
    queryTable() {
      this.$emit('queryTable');
    }
  }
};
</script>

<style lang="scss" scoped>
.right-toolbar {
  float: right;
  margin-right: 20px;
}
</style>
